"""
job 数据库模版

@author: <PERSON>
@data: 2022.08.18
"""

from utils.init import db


class JobRunInfo(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    job_id = db.Column(db.Integer, unique=True)
    create_at = db.Column(db.DateTime)
    end_at = db.Column(db.DateTime)
    order_num = db.Column(db.Integer)


class Job(db.Model):
    id = db.Column(db.String(36), primary_key=True)
    status = db.Column(db.String(10), default="scheduling")
    command = db.Column(db.String(100))
    operator = db.Column(db.String(50))
    create_at = db.Column(db.String(30))
    start_at = db.Column(db.String(30))
    end_at = db.Column(db.String(30))
