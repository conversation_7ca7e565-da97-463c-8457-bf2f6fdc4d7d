"""
权限审核记录表

@author: <PERSON>
@data: 2022.08.18
"""

from utils.init import db


class Permission(db.Model):
    """申请记录"""
    id = db.Column(db.String(36), primary_key=True)
    job_id = db.Column(db.String(36))  # 申请任务ID
    applicant = db.Column(db.String(36))  # 申请人名称
    reviewer = db.Column(db.String(36))  # 审核人open_id
    command = db.Column(db.String(100))  # 命令待执行
    create_at = db.Column(db.String(30))  # 创建时间
    end_at = db.Column(db.String(30))  # 结束时间
    status = db.Column(db.Integer())  # 状态
    chat_type = db.Column(db.String(5))  # p2p or group

    @classmethod
    def status_choice(cls, status: int):
        status_map_list = ["待审核", "审核通过", "审核被拒绝"]
        return status_map_list[status]
