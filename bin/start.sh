#!/bin/bash
if [[ ! -z $1 ]]; then
  SERVICE=$1
elif [[ -z $SERVICE ]]; then
  SERVICE="WORKER"
fi

# 捕获信号
_term() {
    echo "Caught SIGTERM signal!"
    echo "kill -TERM "$child""
    exit
    kill -TERM "$child" 2>/dev/null
    wait "$child"
    pids=`pgrep -P $child`
    for(( i=0;i<${#pids[@]};i++ ))
    do
        wait ${pids[i]};
    done
}

trap _term SIGTERM

function start_SERVER() {
  # 配置aliyun cli
  aliyun configure set --profile akProfile --mode AK --region cn-beijing --access-key-id $ALIYUN_KEY_ID --access-key-secret $ALIYUN_KEY_SECRET
  # 启动服务
  gunicorn server:app -c conf/gunicorn.conf.py
}

function start_WORKER() {
  # 启动客户端
  if [[ -z $KEYFILE_PATH ]]; then
    KEYFILE_PATH="/conf/gcp/keyfile.json"
  fi
  if [[ -z $GCP_CLUSTER ]]; then
    GCP_CLUSTER="tapdata-cloud-cluster"
  fi
  if [[ -z $GCP_REGION ]]; then
    GCP_REGION="asia-east2"
  fi
  gcloud auth login --cred-file="$KEYFILE_PATH" -q
  gcloud container clusters get-credentials $GCP_CLUSTER --region=$GCP_REGION -q
  python worker.py
}

function start_CRONJOB() {
  python cronjob.py
}

if [[ $SERVICE == "SERVER" ]]; then
  start_SERVER
elif [[ $SERVICE == "WORKER" ]]; then
  start_WORKER
elif [[ $SERVICE == "CRONJOB" ]]; then
  start_CRONJOB
else
  echo "SERVICE must be [SERVER|WORKER|CRONJOB]"
fi