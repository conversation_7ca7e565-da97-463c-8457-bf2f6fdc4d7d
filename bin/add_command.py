"""
自动增加handler的模版函数

@author: <PERSON>
@date: 2022.08.31
"""
import os.path
import sys
from datetime import datetime
from jinja2 import Template
import pathlib


BASE_PATH = str(pathlib.Path(__file__).parent.absolute())
HANDLER_PATH = os.path.sep.join([BASE_PATH, "..", "handler"])


def main():
    command = sys.argv[1]
    date = datetime.now().strftime("%Y.%m.%d")
    author = ""
    with open(os.path.sep.join([BASE_PATH, "template"]), "r") as f:
        rendered_text = Template(f.read())
        res = rendered_text.render(command=command, date=date, author=author)
    file_path = os.path.sep.join([HANDLER_PATH, f"{command}.py"])
    if os.path.exists(file_path):
        print("file already exists.")
        return
    with open(file_path, "w") as f:
        f.write(res)


if __name__ == '__main__':
    main()
