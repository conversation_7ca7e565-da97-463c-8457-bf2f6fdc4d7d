"""
解析命令参数并处理

@author: <PERSON>
@data: 2022.08.16
"""
from __future__ import annotations

import base64
import copy
import os
import re
import traceback
import json

import requests

from lib.api import send_text

from utils.connector import redis_cli
from utils.exception import CommandException
from utils.option import Option
from utils.schdule import Schedule, Context
from utils.init import app
from utils import message_card as mc, datastruct
from utils import exception


class Number:
    """数字"""

    @staticmethod
    def check(arg):
        return True if re.match("^[0-9]+$", arg) else False


class Bool:
    """布尔值 0，1"""

    @staticmethod
    def check(arg):
        return True if re.match("^[1|0]$", arg) else False


class Letter:
    """字母"""

    @staticmethod
    def check(arg):
        return True if re.match("^[a-zA-Z]+$", arg) else False


class Pipeline:

    @staticmethod
    def get_current_command(command: str):
        if "|" in command:
            current_command = command.split(" | ")[0]
        else:
            current_command = command
        app.logger.info(f"解析后当前命令: {current_command}")
        if current_command.startswith("cache"):
            values = " ".join(current_command.split(" ")[2:])
            return current_command.split(" ")[1:2] + [values]
        return current_command.split(" ")[1:]

    @staticmethod
    def get_next_command(command: str):
        if "|" in command:
            next_command = command.split("|")[1].strip()
            future_command = " | ".join([s.strip() for s in command.split("|")[2:]])
            return Pipeline.get_concurrency_command(next_command), future_command
        return False

    @staticmethod
    def get_concurrency_command(command: str):
        """
        获取并发任务
        :param command: 命令
        :return:
        """
        if "%" in command:
            return command.split("%")
        return command


class Handler:
    check_template = None
    command = None
    help = None
    message_type = "text"
    except_cache = False  # 缓存避免，为True将不加入缓存
    help_map = {}

    def __init__(self):
        self.builtin_args = {
            "quite": False
        }
        self.magic_arg = {}
        self.concurrency_limit = False  # 并发限制
        self.output = None  # 传递给下一个命令
        self.permissions = []  # 权限，结构为 [uid1, uid2]
        Handler.help_map[self.command] = self.help
        self.option_obj: Option | None = None

    @property
    def _help(self):
        format_describe = self.help.get("format", "")
        return "\n".join(
            [f"格式说明：{format_describe}"] + [f"{k}: v" for k, v in self.help.items()]
        )

    def check(self, *args):
        """检查参数格式"""
        if self.command is None:
            raise CommandException
        if self.check_template is None:
            return
        for i, v in enumerate(self.check_template):
            require = v.get("require", False)
            reg = v.get("reg")
            arg_type = v.get("type")
            try:
                arg = args[i]
                # 判断正则
                if reg is not None and not re.match(reg, arg):
                    raise CommandException
                # 判断类型
                if arg_type is not None and not arg_type.check(arg):
                    raise CommandException
            except IndexError as e:
                if require:
                    app.logger.warn(traceback.format_exc())
                    raise CommandException

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        """处理函数，必须被实现"""
        raise NotImplementedError

    def _next(self, next_command, future_command, open_id, chat_type, **kwargs):
        schedule = Schedule()
        kwargs.update({
            "open_id": open_id,
            "chat_type": chat_type,
            "job_id": self.job_id,
        })
        context = Context(**kwargs)
        if self.output:
            context.command = f"{next_command} {self.output} | {future_command}"
            context.open_id = open_id
            schedule.register(context)
        elif len(next_command) != 0:
            context.command = f"{next_command}"
            context.open_id = open_id
            schedule.register(context)

    def get_cache(self, *args, **kwargs) -> str:
        """返回被缓存的数据"""
        if self.except_cache:
            raise NotImplementedError

    def limit_concurrency(self, **kwargs):
        open_id = kwargs["open_id"]
        command = kwargs["command"]
        if self.concurrency_limit:
            # check whether job is running
            ret = redis_cli.hget(open_id, command)
            # no job is run
            if ret is None:
                redis_cli.hset(open_id, command, "1")
                return True
            else:
                return False
        return True

    def _del_concurrent_tag(self, **kwargs):
        open_id = kwargs["open_id"]
        command = kwargs["command"]
        if self.concurrency_limit:
            redis_cli.hdel(open_id, command)

    def _make_click_multi(self):
        message_card = mc.MessageCard(mc.Header("重复点击，确认要启动多个流程吗", color="red"), mc.Config())
        div_1 = mc.Div(text=mc.Text(f'你可以选择以下任意一种解决方式：'))
        div_2 = mc.Div(text=mc.Text(f'- 等待任务执行结束'))
        div_3 = mc.Div(text=mc.Text(f'- 手动停止任务'))
        div_4 = mc.Div(text=mc.Text(f'- 等待两分钟后重置'))
        message_card.add_element(div_1, div_2, div_3, div_4)
        return message_card

    def _parse_specious(self, args):
        tmp_args = copy.deepcopy(args)
        args = list(args)
        for index, arg in enumerate(tmp_args):
            val = arg.split("=")
            for k in self.magic_arg.keys():
                if val[0] == "job_id":
                    self.job_id = val[1]
                print(k, val[0])
                if k == val[0]:
                    val = val[1]
                    print(val.strip().lower(), val.strip().lower() in ["true", "false"])
                    if val.strip().lower() in ["true", "false"]:
                        self.magic_arg[k] = val.strip().lower() == "true"
                    else:
                        self.magic_arg[k] = val
                    args.remove(arg)
                    break
        return args

    def has_permission(self, kwargs) -> (bool, str):
        """
        判断是否有权限进行某操作
        - 如果permissions为空默认没有权限限制
        - 如果permissions不为空，则要求user_id在permissions指定的列表之内
        此方法可以被重写
        :param kwargs:
        :return:
        """
        user_id = kwargs.get("user_id")
        # 临时处置方式，定时任务没有user_id
        if user_id is None:
            return True
        github_name, user_id, user_name = datastruct.FeishuUserInfo.get_user_info(user_id)
        app.logger.info(f"github_name: {github_name}, "
                        f"user_id: {user_id}, "
                        f"user_name: {user_name}, "
                        f"审核者: {self.permissions}")
        # 如果permission为空，默认没有权限限制
        permissions = self.permissions
        if not permissions:
            return True
        # open_id在permissions之内则有权限
        if github_name in self.permissions or user_id in self.permissions or user_name in self.permissions:
            return True
        return False

    def _parse_kwargs(self, kwargs):
        chat_id = kwargs.get("chat_id") if kwargs.get("chat_id") is not None else kwargs["open_id"]
        chat_type = kwargs.get("chat_type", "p2p")
        if kwargs.get("chat_type"):
            del kwargs["chat_type"]
        open_id = kwargs["open_id"]
        del kwargs["open_id"]
        return chat_id, chat_type, kwargs["command"], open_id

    def _register_next_job(self, command, status, open_id, chat_type, kwargs):
        command = Pipeline.get_next_command(command)
        if command and status:
            next_command = command[0]
            future_command = command[1]
            app.logger.info(f"是Pipeline，下一条命令为: {next_command}, 剩余的命令为: {future_command}")
            if isinstance(next_command, list):
                for cmd in next_command:
                    self._next(cmd, future_command, open_id, chat_type, **kwargs)
            else:
                self._next(next_command, future_command, open_id, chat_type, **kwargs)

    def permission_message_card(self, permission_item):
        message_card = mc.MessageCard(mc.Header("权限申请", color="blue"), mc.Config())
        message_card.add_element(mc.Div())

    def _send_message(self, ret, chat_id, chat_type):
        print("json size of request body: ", len(json.dumps(ret)) / 1024, "KB")
        if os.getenv("WORKER_NAME") != "" and os.getenv("WORKER_NAME") != "default":
            requests.post("https://lark-api.tapdata.net/send_forward", json={
                "msg_type": self.message_type,
                "open_id": chat_id,
                "chat_type": chat_type,
                "content": ret
            })
            return
        if ret and not self.magic_arg.get("quite"):
            send_text(self.message_type, chat_id, ret, chat_type)

    def ret_msg(self, **kwargs):
        self.job_id = kwargs["job_id"]
        print(f"job_id: {self.job_id}")
        # 解析必要参数
        chat_id, chat_type, command, open_id = self._parse_kwargs(kwargs)
        self.open_id = open_id
        self.chat_type = chat_type
        # 设置option类
        self.option_obj = Option(open_id)
        # 获取当前命令
        args = Pipeline.get_current_command(command)
        # 解析特殊参数
        self.magic_arg.update(self.builtin_args)
        args = self._parse_specious(args)
        # 权限判断
        is_permitted = self.has_permission(kwargs)
        # 没有权限，发起权限申请
        if not is_permitted:
            # 将空格全部替换为特殊字符，防止扰乱命令解析器
            command = base64.urlsafe_b64encode(command.encode("utf8")).decode("utf8")
            # command 特殊参数用来传递通过权限审核之后执行的命令
            permission = f"permission job_id={self.job_id} " \
                         f"permission_item={self.magic_arg.get('permission_item')} " \
                         f"applicant={kwargs['user_id']} " \
                         f"command={command} chat_type={chat_type} " \
                         f"reviewer={','.join(self.permissions)}"
            app.logger.info(f"permission is: {permission}")
            ret = mc.not_permitted(self.magic_arg.get("permission_item"), self.permissions, permission).to_dict()
            app.logger.info(f"发送权限不足消息给: {chat_id}")
            self._send_message(ret, chat_id, chat_type)
            return ret, False
        # 校验参数的正确性
        self.check(*args)
        # 处理函数
        ret, status = self.handle(open_id, chat_type, *args, **kwargs)
        # 如果是pipeline，存在下一个任务，则掉调用下一个任务
        if not command.startswith("permission"):
            self._register_next_job(command, status, open_id, chat_type, kwargs)
        # 如果返回值存在，发送信息
        self._send_message(ret, chat_id, chat_type)
        # 调用get_cache缓存信息
        if self.except_cache:
            ret = self.get_cache()
        return ret
