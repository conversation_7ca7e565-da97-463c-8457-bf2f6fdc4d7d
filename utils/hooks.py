from flask import request

from utils.init import app


@app.before_request
def log_request():
    """"""
    headers = '\n'.join([f'{key}: {value}' for key, value in request.headers.items()])
    app.logger.info(f"Request Path: {request.path}, Method: {request.method}, IP: {request.remote_addr}, Params: {request.args}, Data: {request.data}, Header: {headers}")


@app.after_request
def log_response(response):
    """"""
    app.logger.info(f"Request Path: {request.path}, Response: {response.data}")
    return response
