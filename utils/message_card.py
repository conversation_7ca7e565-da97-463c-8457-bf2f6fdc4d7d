"""
消息卡片

@author: <PERSON>
@date: 2022.08.24
"""
from utils import exception, datastruct, common


class Config:

    def __init__(self, enable_forward=True, update_multi=False, wide_screen_mode=True):
        self.enable_forward = enable_forward  # 允许转发
        self.update_multi = update_multi  # 共享卡片
        self.wide_screen_mode = wide_screen_mode  # 自适应宽度

    def to_dict(self):
        return {
            "enable_forward": self.enable_forward,
            "update_multi": self.update_multi,
            "wide_screen_mode": self.wide_screen_mode,
        }


class Header:

    def __init__(self, title, color="green"):
        self.title = title
        self.color = color

    def to_dict(self):
        return {
            "title": {
                "tag": "plain_text",
                "content": self.title,
            },
            "template": self.color
        }


class TextType:
    plain_text = "plain_text"
    lark_md = "lark_md"


class Text:

    def __init__(self, content, tag_type=TextType.plain_text, lines: int = None):
        self.tag_type = tag_type
        self.content = content
        self.lines = lines

    def to_dict(self):
        ret = {
            "tag": self.tag_type,
            "content": self.content,
        }
        if self.lines is not None:
            ret.update({
                "lines": int(self.lines)
            })
        return ret


class Fields:

    def __init__(self):
        self.children = []

    def add_child(self, text: Text, is_short=False):
        self.children.append({
            "is_short": is_short,
            "text": text.to_dict(),
        })

    def to_dict(self):
        return self.children


class Image:

    def __init__(self, image_key, alt: Text, preview=True):
        """
        :param image_key: 图片资源
        :param alt: 图片hover
        :param preview: 是否可以放大
        """
        self.image_key = image_key
        self.alt = alt
        self.preview = preview

    def to_dict(self):
        return {
            "tag": "img",
            "img_key": self.image_key,
            "alt": {
                "tag": "plain_text",
                "content": self.alt,
            },
        }


class Url:

    def __init__(self, url, android_url, ios_url, pc_url):
        self.url = url
        self.android_url = android_url
        self.ios_url = ios_url
        self.pc_url = pc_url

    def to_dict(self):
        return {
            "url": self.url,
            "android_url": self.android_url,
            "ios_url": self.ios_url,
            "pc_url": self.pc_url,
        }


class Confirm:

    def __init__(self, title: Text, text: Text):
        self.title = title
        self.text = text

    def to_dict(self):
        return {
            "title": self.title.to_dict(),
            "text": self.text.to_dict()
        }


class Button:

    def __init__(self,
                 text: Text,
                 url=None,
                 multi_url: Url = None,
                 button_type='default',
                 confirm: Confirm = None,
                 value: dict = None):
        """
        按钮
        :param text: 文本
        :param url: 跳转链接
        :param multi_url: 多端跳转链接
        :param button_type: 按钮类型，"default"/"primary"/"danger"
        :param confirm: 二次确认的弹框
        """
        self.text = text
        self.url = url
        self.multi_url = multi_url
        self.button_type = button_type
        self.confirm = confirm
        self.value = value

    def to_dict(self):
        ret = {
            "tag": "button",
            "text": self.text.to_dict(),
        }
        if self.url:
            ret.update({'url': self.url})
            ret.update({"value": {"key": "value"}})
        if self.multi_url:
            ret.update({"multi_url": self.multi_url})
        if self.button_type:
            ret.update({"type": self.button_type})
        if self.confirm:
            ret.update({"confirm": self.confirm.to_dict()})
        if self.value:
            ret.update({"value": self.value})
        return ret


class Hr:

    def to_dict(self):
        return {"tag": "hr"}


class Note:

    def __init__(self):
        self.elements = []

    def add_child(self, content):
        if not isinstance(content, Text) and not isinstance(content, Image):
            raise exception.MessageCardFormatError
        self.elements.append(content.to_dict())

    def to_dict(self):
        return {
            "tag": "note",
            "elements": self.elements
        }


class SelectType:
    select_static = "select_static"
    select_person = "select_person"


class Option:

    def __init__(self, text: Text = None, value: str = None, url: str = None, multi_url: Url = None):
        self.text = text
        self.value = value
        self.url = url
        self.multi_url = multi_url

    def to_dict(self):
        ret = {}
        if self.text:
            ret.update({"text": self.text.to_dict()})
        if self.value:
            ret.update({"value": self.value})
        if self.url:
            ret.update({"url": self.url})
        if self.multi_url:
            ret.update({"multi_url": self.multi_url.to_dict()})
        return ret


class SelectMenu:

    def __init__(self, tag: str, placeholder: Text = None, initial_option: str = None, value: dict = None,
                 confirm: Confirm = None):
        self.tag = tag
        self.placeholder = placeholder
        self.initial_option = initial_option
        self.options = []
        self.value = value
        self.confirm = confirm

    def add_option(self, option: Option):
        self.options.append(option.to_dict())

    def to_dict(self):
        ret = {
            "tag": self.tag,
        }
        if self.tag == SelectType.select_static and self.initial_option:
            ret.update({
                "initial_option": self.initial_option
            })
        if self.placeholder:
            ret.update({
                "placeholder": self.placeholder.to_dict()
            })
        if self.options:
            ret.update({
                "options": self.options
            })
        if self.value:
            ret.update({
                "value": self.value
            })
        else:
            ret.update({
                "value": {"key": "value"}
            })
        if self.confirm:
            ret.update({
                "confirm": self.confirm.to_dict()
            })
        return ret


class Overflow:

    def __init__(self, value: dict = None, confirm: Confirm = None):
        self.value = value
        self.confirm = confirm
        self.options = []

    def add_option(self, option: Option):
        self.options.append(option.to_dict())

    def to_dict(self):
        ret = {
            "tag": "overflow",
            "options": self.options,
        }
        if self.value:
            ret.update({
                "value": self.value
            })
        if self.confirm:
            ret.update({
                "confirm": self.confirm.to_dict()
            })
        return ret


class DataPickerType:
    data_picker = "date_picker"
    picker_time = "picker_time"
    picker_datetime = "picker_datetime"


class DataPicker:

    def __init__(self, tag,
                 initial_date,
                 initial_time,
                 initial_datetime,
                 placeholder: Text,
                 value: dict,
                 confirm: Confirm):
        """
        时间选择器
        :param tag: 元素标签 DataPickerType.data_picker DataPickerType.picker_time DataPickerType.picker_datetime
        :param initial_date: 日期模式的初始值 格式"yyyy-MM-dd"
        :param initial_time: 时间模式的初始值 格式"HH:mm"
        :param initial_datetime: 日期时间模式的初始值 格式"yyyy-MM-dd HH:mm"
        :param placeholder: 占位符，无初始值时必填
        :param value: 用户选定后返回业务方的数据
        :param confirm: 二次确认的弹框
        """
        self.tag = tag
        self.initial_date = initial_date
        self.initial_time = initial_time
        self.initial_datetime = initial_datetime
        self.placeholder = placeholder
        self.value = value
        self.confirm = confirm

    def to_dict(self):
        ret = {
            "tag": self.tag
        }
        if self.initial_time:
            ret.update({
                "initial_time": self.initial_time
            })
        if self.initial_date:
            ret.update({
                "initial_date": self.initial_date
            })
        if self.initial_datetime:
            ret.update({
                "initial_datetime": self.initial_datetime
            })
        if self.placeholder:
            ret.update({
                "placeholder": self.placeholder.to_dict()
            })
        if self.value:
            ret.update({
                "value": self.value
            })
        if self.confirm:
            ret.update({
                "confirm": self.confirm.to_dict()
            })
        return ret


class Div:

    def __init__(self, text: Text = None, extra=None, fields: Fields = None):
        """
        内容
        :param text: 单文本展示
        :param extra: 附加元素
        :param fields: 多文本展示
        """
        if extra and (
                isinstance(extra, Image) or
                isinstance(extra, Button) or
                isinstance(extra, SelectMenu) or
                isinstance(extra, Overflow) or
                isinstance(extra, DataPicker) or
                isinstance(extra, Input)
        ):
            self.extra = extra
        else:
            self.extra = None
        self.tag = "div"
        self.fields = fields
        self.text = text

    def to_dict(self):
        ret = {
            "tag": self.tag,
        }
        if self.text:
            ret.update({
                "text": self.text.to_dict()
            })
        if self.fields:
            ret.update({
                "fields": self.fields.to_dict()
            })
        if self.extra:
            ret.update({
                "extra": self.extra.to_dict()
            })
        return ret


class Layout:
    bisected = "bisected"
    trisection = "trisection"
    flow = "flow"


class Action:

    def __init__(self, layout=None):
        self.tag = "action"
        self.layout = layout
        self.actions = []

    def add_action(self, action):
        if not isinstance(action, Button) and not isinstance(action, SelectMenu) and \
                not isinstance(action, DataPicker) and not isinstance(action, Overflow):
            raise exception.MessageCardFormatError
        self.actions.append(action.to_dict())

    def to_dict(self):
        ret = {
            "tag": self.tag,
            "actions": self.actions,
        }
        if self.layout:
            ret.update({
                "layout": self.layout
            })
        return ret


class MessageCard:

    def __init__(self, header: Header, config: Config):
        self.elements = []
        self.header = header
        self.config = config

    @classmethod
    def _action_div(cls, action_name):
        return Div(text=Text(f"**{action_name}**", tag_type=TextType.lark_md))

    @classmethod
    def _actions(cls, action_name, buttons: [Button], line_count=2):
        """
        生成一个Action面板

        :param action_name: 操作面板的名称
        :param buttons: 按钮Button列表
        :param line_count: 每行按钮数量
        :return:
        """
        # 操作面板名称
        actions_div = cls._action_div(action_name)
        actions = []
        # 将按钮分行，每行$line_count个
        for index, button in enumerate(buttons):
            if index % line_count == 0:
                action = Action()
                actions.append(action)
            action.add_action(button)
        return actions_div, actions

    @classmethod
    def _button(cls, button_name, command):
        return single_button(
            button_name,
            command=command,
            button_type="primary"
        )

    @classmethod
    def actions(cls, t: dict):
        """
        格式化成actions列表，actions列表为Div结构和Action结构列表
        Div结构用来表示一组操作的类型，比如说企业版操作、云版操作
        Action结构列表用来表示一组操作的按钮列表，比如说企业版操作有打包、更新等按钮

        :param t:
        """
        try:
            actions_div, actions = cls._actions(t["action_name"], [cls._button(**button) for button in t["buttons"]])
        except KeyError:
            raise KeyError("actions参数错误")
        return actions_div, actions

    @classmethod
    def _message_card(cls, title, color, *args):
        header = Header(title=title, color=color)
        message_card = MessageCard(header=header, config=Config())
        for arg in args:
            message_card.add_element(arg)
        return message_card

    @classmethod
    def from_dict(cls, header_text: str, ts: [dict], color="blue"):
        """
        从字典列表中解析
        :param header_text: 标题
        :param ts: [{"action_name": "操作类型", "buttons": [{"button_name": "按钮名称", "command": "按钮命令"}]}]
        :param color: 颜色
        :return:
        """
        elements = []
        for t in ts:
            action_div, actions = cls.actions(t)
            elements.append(action_div)
            elements += actions
        return cls._message_card(header_text, color, *elements)

    def add_element(self, *elements):
        for element in elements:
            self.elements.append(element.to_dict())

    def to_dict(self):
        ret = {
            "config": self.config.to_dict(),
            "elements": self.elements,
            "header": self.header.to_dict()
        }
        return ret


class InputType:
    text = "text"
    multiline_text = "multiline_text"


class Input:
    def __init__(self, name: str, label: str = "请输入文本", required=False,
                 placeholder = "请输入", input_type: InputType = InputType.text):
        """
        输入框
        """
        self.name = name
        self.label = label
        self.required = required
        self.placeholder = placeholder
        self.input_type = input_type

    def to_dict(self):
        ret = {
            "tag": "input",
            "name": self.name,
            "label": Text(self.label).to_dict(),
            "placeholder": Text(self.placeholder).to_dict(),
            "input_type": self.input_type,
            "required": self.required
        }
        return ret


def make_select(options, menu_name, initial_option=None, placeholder=Text("请选择")) -> SelectMenu:
    if initial_option is None:
        select_menu = SelectMenu(
            tag=SelectType.select_static,
            placeholder=placeholder,
            value={"command": f"cache {menu_name}"},
            # initial_option=initial_option,
        )
    else:
        select_menu = SelectMenu(
            tag=SelectType.select_static,
            placeholder=placeholder,
            value={"command": f"cache {menu_name}"},
            initial_option=initial_option,
        )
    for index, branch in enumerate(options):
        option = Option(text=Text(branch), value=str(index + 1))
        select_menu.add_option(option)
    return select_menu


def trigger_button(text: str, command="deploy update", button_type="primary"):
    """触发部署按钮, 用一层Action容器包裹"""
    trigger = Button(text=Text(text), button_type=button_type, value={"command": command})
    trigger_div = Action()
    trigger_div.add_action(trigger)
    return trigger_div


def single_button(text: str, command="deploy update", button_type="primary"):
    """单独的Button组件"""
    return Button(text=Text(text), button_type=button_type, value={"command": command})


def trigger_button_many(values=None):
    if values is None:
        values = {}
    trigger_div = Action()
    for v in values:
        button = Button(text=Text(v[0]), button_type=v[1], value={"command": v[2]})
        trigger_div.add_action(button)
    return trigger_div


def make_select_div(name: str, *args, **kwargs) -> Div:
    select_menu = make_select(*args, **kwargs)
    select_menu_div = Div(Text(name), extra=select_menu)
    return select_menu_div


def not_permitted(permitted_item: str, permitted: [str], command: str) -> MessageCard:
    """
    权限不足
    :param permitted_item: 权限文字提示
    :param permitted: 拥有权限的用户
    :param command: 确认申请命令
    :return:
    """
    if not permitted:
        raise Exception("有权限的用户列表不能为空")
    message_card = MessageCard(Header("权限不足❗", color="red"), Config())
    user_names = []
    for user in permitted:
        _, _, user_name = datastruct.FeishuUserInfo.get_user_info(user)
        user_names.append(user_name)
    permitted = "\n\t" + "\n\t".join([f"- {user}" for user in user_names])
    notice_text = f"您正在运行**“{permitted_item}“**操作，然而您没有足够的权限，请确认以下操作。\n" \
                  f"点击申请执行可以向以下用户提起申请：" + permitted
    notice_div = Div(text=Text(notice_text, tag_type=TextType.lark_md))
    applicant_button = trigger_button("确认申请", command=command)
    canceled_button = trigger_button("取消流程", command="clear")
    message_card.add_element(notice_div, applicant_button, canceled_button)
    return message_card


def limit_permitted(reviewer: str, action: str) -> MessageCard:
    if action == "pass":
        header = "申请通过，任务即将执行 🎉"
        color = "green"
        content = Div(text=Text(f"您的任务由{reviewer}申请通过，即将执行...", tag_type=TextType.lark_md))
    else:
        header = "申请被拒绝，任务已取消❗"
        color = "red"
        content = Div(text=Text(f"您的任务由{reviewer}申请拒绝，任务已取消...", tag_type=TextType.lark_md))
    message_card = MessageCard(Header(header, color=color), Config())
    message_card.add_element(content)
    return message_card


def apply_success():
    message_card = MessageCard(Header("申请发送成功", color="blue"), Config())
    message_card.add_element(Div(text=Text(f"已成功发送申请，请等待申请结果", tag_type=TextType.lark_md)))
    return message_card


def permitted_request(applicant: str, permitted_item: str, permission_id: str):
    """
    发送给审核者的消息卡片
    :param applicant: 申请人
    :param permitted_item: 申请项目
    :param permission_id: 申请记录表ID
    :return: 消息卡片结构体
    """
    header = f"{applicant} 正在发起“{permitted_item}”申请"
    message_card = MessageCard(Header(header, color="blue"), Config())
    text_div = Div(text=Text(f"**请确认是否同意**", tag_type=TextType.lark_md))
    action = trigger_button_many([("同意", "primary", f"permission {permission_id} pass"),
                                  ("拒绝", "primary", f"permission {permission_id} reject")])
    message_card.add_element(text_div, action)
    return message_card


def make_error_branch():
    message_card = MessageCard(Header("分支选择错误", color="red"), Config())
    div_1 = Div(text=Text(f"未选择分支"))
    div_2 = Div(text=Text(f'请重新选择一遍分支，如果已经选了那就是BUG了，跟jerry报下bug吧'))
    message_card.add_element(div_1, div_2)
    return message_card


def make_workflow_scheduling_notes(runner_id, detail_url):
    message_card = MessageCard(Header("▶️ 构建任务等待调度结束，正在运行", color="green"), Config())
    div_1 = Div(text=Text(f"▶️ **流程链接**\n{detail_url}", tag_type=TextType.lark_md))
    div_2 = Div(text=Text(f"▶️ **流程ID**\n{runner_id}", tag_type=TextType.lark_md))
    cancel_button = trigger_button("取消该流程", command=f"cancel cicd {runner_id} | clear")
    check_button = trigger_button("查看任务进度", command=f"check cicd {runner_id}")
    message_card.add_element(div_1, div_2)
    message_card.add_element(cancel_button, check_button)
    return message_card


def make_workflow_scheduling_notes_opensource(runner_id, detail_url):
    message_card = MessageCard(Header("▶️ 构建任务等待调度结束，正在运行", color="green"), Config())
    div_1 = Div(text=Text(f"▶️ **流程链接**\n{detail_url}", tag_type=TextType.lark_md))
    div_2 = Div(text=Text(f"▶️ **流程ID**\n{runner_id}", tag_type=TextType.lark_md))
    message_card.add_element(div_1, div_2)
    return message_card


def workflow_start_error():
    message_card = MessageCard(Header("📣 构建任务启动失败", color="red"), Config())
    div_1 = Div(text=Text(f"可能是Runner被关闭，或者其他情况", tag_type=TextType.lark_md))
    message_card.add_element(div_1)
    return message_card


def make_input(name:str, label:str, placeholder: str, required: bool = False,
               input_type:InputType = InputType.text) -> Input:
    _input = Input(
        name=name,
        label=label,
        placeholder=placeholder,
        required=required,
        input_type=input_type
    )
    return _input


def make_input_div(div_name: str, *args, **kwargs) -> Div:
    _input = make_input(*args, **kwargs)
    input_div = Div(Text(div_name), extra=_input)
    return input_div
