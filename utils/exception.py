"""自定义Excetion类型"""


class BaseCustomException(Exception):
    message = "命令格式错误, 可以使用 help 获取帮助"

    def __init__(self):
        super(BaseCustomException, self).__init__(self.message)

    def __str__(self):
        return self.message


class ModeError(BaseCustomException):
    message = "程序运行模式设置错误"


class CommandException(BaseCustomException):
    message = "命令格式错误, 可以使用 h/help 获取帮助"


class InternalCommandSyncTaxError(BaseCustomException):
    message = "内部命令语法错误"


class AppNameException(BaseCustomException):
    message = "应用名称不能为空"


class CommandEmptyException(BaseCustomException):
    message = "command不能为空"


class MessageTypeError(BaseCustomException):
    message = "message_type 必须是 text/message_card 其中一个"


class MessageCardFormatError(BaseCustomException):
    message = "消息卡片格式错误"


class ConnectorsIsEmpty(BaseCustomException):
    message = "连接器不能为空"


class CronJobSetError(BaseCustomException):
    message = "定时任务设置错误"


class CronJobDateSyntaxError(BaseCustomException):
    message = "定时任务语法错误"


class DrsBranchIsNone(BaseCustomException):
    message = "云版分支不能为空且必须为str"


class RequireOptionNotProvide(BaseCustomException):
    message = "必选项未选"


class EnvLengthIsEmpty(BaseCustomException):
    message = "当前环境为空，无法进行更新"


class K8sError(BaseCustomException):
    message = "Kubernetes接口调取失败，具体请查看日志"


class ShellCommandReturn1Error(BaseCustomException):
    message = "命令执行失败"


class ShellCommandReturn2Error(BaseCustomException):
    message = "命令被滥用, 权限不足"


class ShellCommandReturn126Error(BaseCustomException):
    message = "命令无法执行"


class ShellCommandReturn127Error(BaseCustomException):
    message = "找不到该命令"


class ShellCommandReturn128Error(BaseCustomException):
    message = "退出时参数无效"


class ShellCommandReturn130Error(BaseCustomException):
    message = "脚本被Control+C终止"


class PermissionsError(BaseCustomException):
    message = "权限不足，确实需要执行请联系相关人员"


class LimitNotFound(BaseCustomException):
    message = "找不到相关权限申请记录"


class LimitError(BaseCustomException):
    message = "申请状态已经被处理"


class ShellCommandErrorUnknown(BaseCustomException):
    message = "未知错误"


class ReviewersIsNone(BaseCustomException):
    message = "审核者不能为空"


class SystemClose(BaseCustomException):
    message = "程序关闭，线程终止"


class OptionListNotFound(BaseCustomException):
    message = "选项列表缓存获取失败"


class InternalError(BaseCustomException):
    message = "内部错误"


class OptionKeyNotFound(BaseCustomException):
    message = "从选项组中找不到对应的key"
