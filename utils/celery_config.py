import os

from celery import chain, group
from celery.schedules import crontab


beat_schedule = {
    # 30min check once for below tasks
    # 1-1 review and merge all requests to develop branch (tapdata and tapdata-enterprise repo)
    'check-no-pr-for-task-1-1': {
        'task': "check-no-pr-for-task-1-1",
        'schedule': crontab(minute="*/30"),
    },
    # 1-2. review and merge all requests to develop branch (tcm repo)
    'check-no-pr-for-task-1-2': {
        'task': "check-no-pr-for-task-1-2",
        'schedule': crontab(minute="*/30"),
    },
    # 1-3. review and merge all requests to develop branch (web repo)
    'check-no-pr-for-task-1-3': {
        'task': "check-no-pr-for-task-1-3",
        'schedule': crontab(minute="*/30"),
    },
    # 1-4. review and merge all requests to develop branch (connectors and enterprise connectors repo)
    'check-no-pr-for-task-1-4': {
        'task': "check-no-pr-for-task-1-4",
        'schedule': crontab(minute="*/30"),
    },
    # 1-5. merge code from main -> develop for tapdata and tapdata-enterprise repo
    'check-no-content-to-merge-for-task-1-5': {
        'task': "check-no-content-to-merge-for-task-1-5",
        'schedule': crontab(minute="*/30"),
    },
    # 1-6. merge code from main -> develop for connectors and enterprise-connectors repo
    'check-no-content-to-merge-for-task-1-6': {
        'task': "check-no-content-to-merge-for-task-1-6",
        'schedule': crontab(minute="*/30"),
    },
    # task 1 had been finished
    'check-no-content-to-merge-for-task-1': {
        'task': "utils.tasks.check_subtask_status",
        'schedule': crontab(minute="*/30"),
        'kwargs': {"task_prefix": "1."}
    },
    'count-aliyun-fee-everyday': {
        'task': "count-aliyun-fee-everyday",
        'schedule': crontab(minute=0, hour=10),
        'args': ('LTAI5tNpNKc8iD8eedm5zRvn', '******************************',)
    },
}

broker_url = os.getenv("CELERY_BROKER_URL")
result_backend = os.getenv("CELERY_RESULT_BACKEND")
timezone = "Asia/Shanghai"
