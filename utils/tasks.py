import base64
import hashlib
import hmac
import json
import time
from collections.abc import Iterable
from datetime import datetime, date, timedelta

import requests
from celery import chain, group
from celery.exceptions import MaxRetriesExceededError
from sqlalchemy import exc

from model.task import Task
from utils import api, common, option
from utils.api import KubernetesCommand, GithubApi
from utils.common import sam, jarad
from utils.init import celery, session
from lib.api import send_text
from utils.connector import MongoConnector
from urllib.parse import quote, urlencode

tony = "ou_894a699805e516d48b95b97c05b9b992"
jerry = "ou_677b941f814ae3c4235d046a1f8f5478"
group_id = "oc_f811a33fb1326d7016f6b2d164b721e1"


@celery.task
def trigger_workflow(owner, repo, workflow_id, branch, **kwargs):
    return api.GithubApi(owner, repo).workflow_dispatch_trigger(
        workflow_id,
        branch,
        **kwargs
    )


@celery.task
def trigger_workflow_and_wait(owner, repo, workflow_id, branch, **kwargs):
    return api.GithubApi(owner, repo).workflow_dispatch_trigger_and_wait(
        workflow_id,
        branch,
        **kwargs
    )


@celery.task
def send_message_to_feishu(message_type, open_id, text_content, chat_type):
    send_text(message_type, open_id, text_content, chat_type)


@celery.task
def build(**kwargs):
    """
    构建制品包
    """
    branches = {
        "FRONTEND_BRANCH": "develop",
        "OPENSOURCE_BRANCH": "develop",
        "ENTERPRISE_BRANCH": "develop",
        "CONNECTORS_BRANCH": "develop#develop",
        "LISENSE_BRANCH": "main",
        "IS_TAR": False,
        "FRONTEND_MODE": "production",
        "TAPDATA_PLATFORM_VERSION": "Linux",
    }
    branches.update(kwargs)
    run_id = api.GithubApi('tapdata', 'tapdata-application').workflow_dispatch_trigger_and_wait(
        'build-tapdata-op.yaml', 'main', **branches)
    return run_id


def _send_text_retry(self, message_type, open_id, text_content, chat_type):
    """当发送消息失败时，调用self.retry进行重试"""
    try:
        send_text(message_type, open_id, text_content, chat_type)
    except Exception as e:
        raise self.retry(exc=e)


@celery.task(bind=True, max_retries=2, default_retry_delay=10)
def artifact_build_result_send(self, build_result, next_step_text="更新到OP环境", repo="tapdata-application"):
    """
    发送构建结果信息
    """
    workflow_id, build_result = build_result
    action_url = f"https://github.com/tapdata/{repo}/actions/runs/{workflow_id}"
    failed_jobs = api.GithubApi("tapdata", "tapdata-application").get_failed_job_by_workflow_id(workflow_id)

    if not build_result:

        message = common.field_list_message_card(
            header="❗️制品构建失败",
            流程链接=action_url,
            失败的步骤="\n".join([f"- {job}" for job in failed_jobs]),
            color="red",
            is_short=False,
        )
        # send_text("message_card", group_id, message.to_dict(), "group")
        _send_text_retry(self, "message_card", group_id, message.to_dict(), "group")
        raise Exception(f"制品构建失败, 构建失败，或流程被取消, workflow_id: {build_result}")
    else:
        message = common.field_list_message_card(
            header="📣 制品构建完成",
            流程链接=f"https://github.com/tapdata/{repo}/actions/runs/{workflow_id}",
            下一步=next_step_text,
        )
        send_text("message_card", tony, message.to_dict(), "p2p")
    return workflow_id


@celery.task
def update_op_env(build_result, op_env="dev(3030)"):
    """
    更新到op环境
    """
    tag = common.get_tag_by_workflow_id(build_result)
    kwargs = {
        "TAG_NAME": tag,
        "DEPLOY": op_env,
        "JAVA_VERSION": "java17",
    }
    run_id = api.GithubApi("tapdata", "tapdata-application").workflow_dispatch_trigger_and_wait(
        "deploy-tapdata-op.yaml", "main", **kwargs
    )
    return run_id


@celery.task(bind=True, max_retries=2, default_retry_delay=10)
def env_update_result_send(self, update_result):
    run_id, update_result = update_result
    if not update_result:
        message = common.field_list_message_card(
            header="❗️部署失败",
            失败详情="无法触发流程",
            流程链接=f"https://github.com/tapdata/tapdata-application/actions/runs/{run_id}",
            处理方式=f"手动更新环境",
            color="red",
        )
        # send_text("message_card", group_id, message.to_dict(), "group")
        _send_text_retry(self, "message_card", group_id, message.to_dict(), "group")
        raise Exception(f"部署失败, 无法触发流程, {update_result}")
    else:
        # 制品构建成功, 发送消息
        message = common.field_list_message_card(
            header="📣 部署完成",
            访问链接=f"http://192.168.1.184:3030",
        )
        send_text("message_card", tony, message.to_dict(), "p2p")
    return update_result


@celery.task(bind=True, max_retries=2, default_retry_delay=10)
def open_cloud_gray_env(self, build_result, env_name="cloud-net"):
    """
    打开云版灰度环境
    """
    MongoConnector(option.ResourcePoll.agent_info[env_name]["mongo_uri"]).update_one(
        "net-dfs_gw", "settings", {"key": "enableGrayScale"}, {"$set": {"value": True}}
    )
    result = MongoConnector(option.ResourcePoll.agent_info[env_name]["mongo_uri"]).find_one("net-dfs_gw", "settings", {
        "key": "enableGrayScale"
    })
    if result.get("value", False):
        message = common.field_list_message_card(
            header="📣打开灰度成功",
            下一步="发布agent",
        )
        send_text("message_card", tony, message.to_dict(), "p2p")
    else:
        message = common.field_list_message_card(
            header="❗打开灰度标记失败",
            处理方式=f"已通知Tony手动打开灰度环境，不影响后续更新" + (
                "" if self.request.retries == self.max_retries else f"，即将重试"),
            color="red" if self.request.retries == self.max_retries else "yellow",
        )
        # 即使达到了最大的失败次数，也不抛出失败
        try:
            _send_text_retry(self, "message_card", group_id, message.to_dict(), "group")
            self.retry()
        except MaxRetriesExceededError:
            print("打开灰度标记失败, 已达到最大重试次数")
    return build_result


@celery.task(bind=True, max_retries=1, default_retry_delay=2 * 60)
def check_passed_for_cloud_env(self, build_result, deployment, namespace):
    """
    检查镜像是否更新，并且检查是否已经成功启动

    1. 先等待2分钟等待镜像更新
    2. 验证deployment镜像版本,等待2分钟
    3. 验证更新是否已经完成，等待10分钟，重试1次
    """
    tag = common.get_tag_by_workflow_id(build_result)
    # 1. 先等待2分钟等待镜像更新
    time.sleep(120)
    # 2. 验证deployment镜像版本，等待2分钟
    out, err, ret_code = KubernetesCommand("gcp").get_deployment_image(deployment, namespace)
    print(f"tag: {tag}, out: {out.split(':')[-1]}")
    equal = out.split(':')[-1] == tag
    count = 0
    while not equal and count < 1:
        out, err, ret_code = KubernetesCommand("gcp").get_deployment_image(deployment, namespace)
        equal = out.split(':')[-1] == tag
        count += 1
        time.sleep(10)
    if not equal:
        message = common.field_list_message_card(
            header=f"服务 {deployment} 更新失败",
            color="red" if self.request.retries == 1 else "yellow",
            具体原因="镜像更新失败" + ("" if self.request.retries == 1 else f"，即将重试"),
            is_short=False,
        )
        # send_text("message_card", group_id, message.to_dict(), "group")
        _send_text_retry(self, "message_card", tony, message.to_dict(), "p2p")
        exception = Exception(f"服务 {deployment} 更新失败, 镜像未替换, out: {out.split(':')[-1]}, tag: {tag}")
        self.retry(exc=exception)
        raise exception
    # 3. 验证更新是否已经完成
    index, count, updated = 0, 2, False
    while index < count:
        out, err, ret_code = KubernetesCommand("gcp").rollout_status(deployment, namespace, timeout=10)
        print(f"ret_code: {ret_code}, type ret_code: {type(ret_code)}")
        if ret_code == 0:
            updated = True
            break
        index += 1
        time.sleep(10)
    if not updated:
        message = common.field_list_message_card(
            header=f"服务 {deployment} 更新失败",
            具体原因="镜像已更新，但是启动失败" + ("" if self.request.retries == 1 else f"，即将重试"),
            color="red" if self.request.retries == 1 else "yellow",
            is_short=False,
        )
        # send_text("message_card", group_id, message.to_dict(), "group")
        _send_text_retry(self, "message_card", tony, message.to_dict(), "p2p")
        exception = Exception(
            f"服务 {deployment} 更新失败, 镜像已更新，但是启动失败, out: {out.split(':')[-1]}, tag: {tag}")
        self.retry(exc=exception)
        raise exception
    message = common.field_list_message_card(
        header=f"服务 {deployment} 更新成功",
        具体原因="镜像已更新",
        is_short=False,
    )
    send_text("message_card", tony, message.to_dict(), "p2p")
    return build_result


@celery.task(bind=True)
def sync_agent_version(self, build_result):
    """
    同步agent版本到各个环境
    """
    if isinstance(build_result, list):
        build_result = build_result[0]
    print(f"build_result: {build_result}")
    tag = common.get_tag_by_workflow_id(build_result)
    run_id, result = api.GithubApi("tapdata", "tapdata-application").workflow_dispatch_trigger_and_wait(
        "sync-agent-image.yaml", "main", AGENT_VERSION=tag
    )
    if not result:
        message = common.field_list_message_card(
            header="❗同步Agent到环境失败",
            版本=tag,
            color="red",
            流程链接=f"https://github.com/tapdata/tapdata-application/actions/runs/{run_id}",
        )
        # send_text("message_card", group_id, message.to_dict(), "group")
        send_text("message_card", tony, message.to_dict(), "p2p")
        raise Exception(f"同步Agent到环境失败, tag: {tag}")
    return build_result


@celery.task(bind=True)
def release_agent_version(self, build_result):
    """
    发布agent到灰度环境
    """
    
    if isinstance(build_result, list):
        build_result = build_result[0]

    tag = common.get_tag_by_workflow_id(build_result)
    document = {
        "version": tag,
        "lbsVersion": tag,
        "enable": True,
        "productType": "agent",
        "createAt": datetime.now(),
        "supportResPools": [
            {
                "region": "-",
                "zone": "-"
            },
            {
                "region": "-",
                "zone": "-",
                "provider": "-"
            },
            {
                "region": "cn-hongkong",
                "zone": "-",
                "provider": "AliCloud"
            },
            {
                "region": "cn-beijing",
                "zone": "-",
                "provider": "AliCloud"
            },
            {
                "region": "asia-east2",
                "zone": "-",
                "provider": "GCP"
            }
        ],
        "tmServerUrl": "https://cloud.tapdata.net/console/v3/tm/api/",
        "downloadUrl": "https://resource.tapdata.net/package/feagent/dfs-{version}/",
        "links": [
            {
                "os": "windows",
                "command": "{token}"
            },
            {
                "os": "linux",
                "command": "wget '{downloadUrl}tapdata' && chmod +x tapdata && ./tapdata start backend --downloadUrl {downloadUrl} --token '{token}'"
            },
            {
                "os": "docker",
                "command": "docker run --pull always --restart always -itd tapdata-docker.pkg.coding.net/dfs/flow-engine/dfs-flow-engine:latest " + "/opt/agent/tapdata start backend  --token '{token}'"
            },
            {
                "os": "AliComputenest",
                "url": "https://computenest.console.aliyun.com/user/cn-hangzhou/serviceInstanceCreate?ServiceId=service-1dda29c3eca648cfb0cb&params=%7B%22RegionId%22%3A%22cn-hangzhou%22%2C%22Parameters%22%3A%7B%22PayType%22%3A%22PostPaid%22%2C%22PayPeriodUnit%22%3A%22Month%22%2C%22PayPeriod%22%3A1%2C%22TapdataAgentVersion%22%3A%22{version}%22%2C%22TapdataAgentToken%22%3A%22{token}%22%7D%2C%22TemplateName%22%3A%22TapdataCloudAgentPrivateDeployTemplate%22%7D&spm=5176.24779694.0.0.29494d22hmLPRe",
                "trialUrl": "https://computenest.console.aliyun.com/user/cn-hangzhou/serviceInstanceCreate?ServiceId=service-1dda29c3eca648cfb0cb&isTrial=true&params=%7B%22RegionId%22%3A%22cn-hangzhou%22%2C%22Parameters%22%3A%7B%22PayType%22%3A%22PostPaid%22%2C%22PayPeriodUnit%22%3A%22Month%22%2C%22PayPeriod%22%3A1%2C%22TapdataAgentVersion%22%3A%22{version}%22%2C%22TapdataAgentToken%22%3A%22{token}%22%7D%2C%22TemplateName%22%3A%22TapdataCloudAgentPrivateDeployTemplate%22%7D&spm=5176.24779694.0.0.29494d22hmLPRe"
            }
        ],
        "changeList": "1. 修复已知缺陷",
        "enableGrayScale": True,
        "packageSize": 235929600,
        "releaseNoteUri": "https://mp.weixin.qq.com/s/eBHKEZBVkuQ0ah8Kv0wRKQ",
        "estimatedUpgradeTime": 600
    }
    db = option.ResourcePoll.agent_info["cloud-net"]["database"]
    MongoConnector(option.ResourcePoll.agent_info["cloud-net"]["mongo_uri"]).insert_one(
        db, "drs_product_release", document
    )
    result = MongoConnector(option.ResourcePoll.agent_info["cloud-net"]["mongo_uri"]).get(db, "drs_product_release", {
        "version": tag
    })
    if not result:
        message = common.field_list_message_card(
            header="❗发布agent版本失败",
            版本=tag,
            color="red",
        )
        send_text("message_card", tony, message.to_dict(), "p2p")
        raise Exception(f"发布agent版本失败, tag: {tag}")
    message = common.field_list_message_card(
        header=f"服务更新成功",
        具体原因="云版灰度环境已全部更新",
        is_short=False,
    )
    send_text("message_card", tony, message.to_dict(), "p2p")
    # send_text("message_card", group_id, message.to_dict(), "group")


@celery.task
def scale_deployment(deployment, namespace):
    KubernetesCommand("gcp").scale_deployment(deployment, namespace, 1)


def _finish_feishu_task(_tk, _user_id_type):
    _url = f"https://open.feishu.cn/open-apis/task/v2/tasks/{_tk}?user_id_type={_user_id_type}"
    _result = requests.patch(_url, headers=common.get_lark_header(), json={
        "task": {
            "completed_at": f"{common.get_current_timestamp()}"
        },
        "update_fields": [
            "completed_at"
        ]
    })
    if _result.status_code != 200:
        _message = common.field_list_message_card(
            header="❗更新飞书任务失败，请手动更新任务状态",
            处理方式="请将任务置为完成状态",
            color="red",
        )
        print(f"更新飞书任务失败, task_id: {_tk}")
        print(_result.json())
        send_text("message_card", tony, _message.to_dict(), "p2p")


@celery.task(bind=True, max_retries=2, default_retry_delay=1 * 60)  # 失败重试2次
def finish_feishu_task(self, build_result, task_guid, user_id_type="open_id"):
    """
    完成飞书任务及其子任务

    1. 获取子任务
    2. 将任务及其子任务置为完成状态
    """
    # 获取当前任务的前一个任务
    try:
        tasks = session.query(Task).filter_by(id=task_guid).all()
        current_task = tasks[0]
        version = current_task.release_version
        tasks = session.query(Task).filter_by(release_version=version).all()
        for t in tasks:
            if current_task.summary.startswith("1.") and t.summary.startswith("3."):
                task_guid = t.id
            elif current_task.summary.startswith("6.") and t.summary.startswith("7."):
                task_guid = t.id
        session.commit()
        print(task_guid)
    except exc.SQLAlchemyError as e:
        session.rollback()
        try:
            self.retry(exc=e)
        except self.MaxRetriesExceededError:
            pass
        return
    except Exception as e:
        import traceback
        traceback.print_exc()
    finally:
        # 注意回收这个连接，防止celery长时间运行导致mysql server has gone away
        session.remove()

    # 1. 获取子任务
    url = F"https://open.feishu.cn/open-apis/task/v2/tasks/{task_guid}/subtasks"
    result = requests.get(url, headers=common.get_lark_header())
    subtask = []
    for item in result.json()["data"]["items"]:
        subtask.append(item["guid"])
    # 2. 将任务及其子任务置为完成状态
    for tk in [task_guid] + subtask:
        _finish_feishu_task(tk, user_id_type)


@celery.task(bind=True, max_retries=2, default_retry_delay=60)
def check_subtask_status(self, task_prefix=None):
    """
    检查子任务状态, 如果子任务都已完成，则完成当前任务

    :param task_prefix: 任务前缀，如"1.",则子任务前缀为"1-"，默认为None
    """
    if not task_prefix:
        raise ValueError("请传入任务前缀")
    try:
        # 查看当前迭代版本
        version = common.get_current_release().replace("release-v", "")
        # 根据当前迭代版本和任务前缀,检查任务是否完成
        # 如果该任务为已完成，则不再继续
        task = session.query(Task).filter(Task.summary.startswith(task_prefix), Task.release_version == version).first()
        if not task:
            raise ValueError(f"当前迭代版本 `{version}` 不存在 `{task_prefix}` 开头的任务")
        elif task.status == "done":
            raise ValueError(f"当前任务 `{task.summary}` 已完成")
        # 检查是否有未完成的子任务
        # 如果有，则抛出异常，不在继续
        # 如果没有，则返回结束该任务
        subtasks = session.query(Task).filter(
            Task.summary.startswith(f"{task_prefix.replace('.', '')}-"),
            Task.release_version == version
        ).all()
        for subtask in subtasks:
            if subtask.status != "done":
                raise ValueError(f"当前任务 `{task.summary}` 存在未完成的子任务")
        _finish_feishu_task(task.id, "open_id")
        task.status = "done"
        session.commit()
    except exc.SQLAlchemyError as e:
        session.rollback()
        self.retry(exc=e)
    finally:
        session.remove()


@celery.task(bind=True, max_retries=2, default_retry_delay=60)
def finish_task_in_database(self, *args, task_id=None):
    """
    在数据库中将todo改为done，如果传入了task_id，则修改当前任务及其对应的任务；
    如果当前task_id属于任务“1.”开头，则修改该任务及任务“3.”开头的任务；
    如果参数中传入了task_id，则上述为task_id，如果不传，则为args中的第一个参数

    :param task_id: 任务ID，如果传入了task_id，则修改当前任务及其对应的任务；
    :param args: 任务ID，但是会被task_id覆盖
    """
    # 确定任务ID
    if task_id:
        task_guid = task_id
    else:
        if len(args) == 0:
            raise Exception("请传入任务ID")
        task_guid = args[0]
    try:
        # 获取当前任务
        current_task = session.query(Task).filter_by(id=task_guid).first()
        # 获取当前任务的前一个任务
        tasks = session.query(Task).filter_by(release_version=current_task.release_version).all()
        for t in tasks:
            if current_task.summary.startswith("1.") and t.summary.startswith("3."):
                t.status = "done"
            elif current_task.summary.startswith("6.") and t.summary.startswith("7."):
                t.status = "done"
        # 将当前任务和对应的任务置为完成状态
        current_task.status = "done"
        session.commit()
    except exc.SQLAlchemyError as e:
        session.rollback()
        self.retry(exc=e)
    finally:
        session.remove()


@celery.task(bind=True, max_retries=2, default_retry_delay=60)
def send_text_to_feishu(self, *args, text=""):
    # 连接失败时，触发重试
    _send_text_retry(self, "text", group_id, text, "group")


@celery.task(bind=True, max_retries=2, default_retry_delay=60)
def check_no_pr(self, *args, repo="tapdata", source_branch=None, target_branch="develop", state="open", task_prefix="1-1"):
    """
    在发版任务创建后, 自动完成一些任务。如果已经没有未关闭的合并到 develop 的请求, 那自动将 1-1, 1-2, 1-3, 1-4 标记为完成

    当前pr开始时，检查 $repo 是否有未合并到目标为 $target_branch 的PR
        - 如果有，则抛出异常，重试
        - 如果没有，则返回True
    :param repo: 仓库名
    :param source_branch: 源分支, 默认为None
    :param target_branch: 目标分支, 默认为develop
    :param state: PR状态, 默认为open
    :param task_prefix: 任务前缀
    """
    try:
        # 查看当前迭代版本
        version = common.get_current_release().replace("release-v", "")
        # 根据当前迭代版本和任务前缀,检查任务是否完成
        # 如果该任务为已完成，则不再继续
        print(f"task_prefix: {task_prefix}, version: {version}")
        task = session.query(Task).filter(Task.summary.startswith(task_prefix), Task.release_version == version).first()
        if task is None:
            raise ValueError(f"当前迭代版本 `{version}` 不存在 `{task_prefix}` 开头的任务")
        elif task.status == "done":
            print(f"task status: {task.status}")
            raise ValueError(f"当前任务 `{task.summary}` 已完成")
        # 检查是否有未合并到目标分支的PR
        # 如果有，则抛出异常，不在继续
        # 如果没有，则返回该任务的ID
        res = GithubApi("tapdata", repo).list_pr(repo, state=state,
                                                 source_branch=source_branch, target_branch=target_branch)
        if len(res) > 0:
            raise ValueError(f"当前仓库 `{repo}` 存在未合并到 `{target_branch}` PR, 运行终止")
        else:
            return task.id
    except exc.SQLAlchemyError as e:
        session.rollback()
        self.retry(exc=e)
    finally:
        session.remove()


@celery.task(bind=True, max_retries=2, default_retry_delay=60)
def check_no_content_to_merge(self, *args, repo="tapdata", source_branch="main", target_branch="develop",
                              task_prefix="1-5", notification_group=None, at_user_id=None):
    """
    尝试发起一个 main -> develop 的合并请求, 但是发现无法发起, 因为没有可以合并的内容
        - 如果可以发起，则发起并抛出异常，阻止下一步任务的进行
        - 如果因为没有可以合并的内容而无法发起，则返回当前任务的task_id

    :param repo: 仓库名
    :param source_branch: 源分支, 默认为main
    :param target_branch: 目标分支, 默认为develop
    :param task_prefix: 任务前缀, 默认为1-5
    :param notification_group: 通知群组
    :param at_user_id: @用户ID
    """
    try:
        # 查看当前迭代版本
        version = common.get_current_release().replace("release-v", "")
        # 根据当前迭代版本和任务前缀,检查任务是否完成
        # 如果该任务为已完成，则不再继续
        task = session.query(Task).filter(Task.summary.startswith(task_prefix), Task.release_version == version).first()
        if not task:
            raise ValueError(f"当前迭代版本 `{version}` 不存在 `{task_prefix}` 开头的任务")
        elif task.status == "done":
            raise ValueError(f"当前任务 `{task.summary}` 已完成")
        # 尝试发起一个 main -> develop 的合并请求
        result, response = GithubApi("tapdata", repo).create_pr(source_branch, target_branch)
        if result:
            if notification_group and at_user_id:
                text = f'已发起 {repo}仓库 {source_branch} -> {target_branch} 的合并请求, 请<at user_id="{at_user_id}"></at>查看'
                send_text("text", notification_group, text, "group")
            raise ValueError(f"当前仓库 `{repo}` 可以发起 `{source_branch}` -> `{target_branch}` 的合并请求")
        error_message = response.json()["errors"][0]["message"]
        if "No commits between" in error_message:
            return task.id
    except exc.SQLAlchemyError as e:
        session.rollback()
        self.retry(exc=e)
    finally:
        session.remove()


@celery.task(bind=True, max_retries=2, default_retry_delay=60)
def finish_tasks(self, task_id):
    """
    :task_id: 任务ID(str),为ID或多个相同的task.id
    """
    try:
        # 检查task_id是否为可迭代对象，并且元素相同
        if isinstance(task_id, Iterable) and len(set(task_id)) == 1:
            task_id = task_id[0]
        task = session.query(Task).filter_by(id=task_id).first()
        task.status = "done"
        session.commit()
        # 将飞书任务置为完成
        _finish_feishu_task(task.id, "open_id")
    except exc.SQLAlchemyError as e:
        session.rollback()
        self.retry(exc=e)
    finally:
        session.remove()


@celery.task
def handle_opensource_build_error(request, exc, traceback):
    """处理开源版构建错误的 Celery 任务"""
    try:
        # 发送失败通知给tony
        message = common.field_list_message_card(
            header="❗️开源版构建失败",
            失败详情=f"开源版构建失败: {str(exc)}",
            处理方式="请手动检查状态",
            color="red",
        )
        send_text("message_card", tony, message.to_dict(), "p2p")
    except Exception as e:
        print(f"Error in handle_opensource_build_error: {e}")
    # 返回None，允许任务链继续执行
    return None


@celery.task
def handle_cloud_update_error(request, exc, traceback):
    """处理云版灰度环境更新错误的 Celery 任务"""
    try:
        # 发送失败通知给tony
        message = common.field_list_message_card(
            header="❗️云版灰度环境更新失败",
            失败详情=f"云版灰度环境更新失败: {str(exc)}",
            处理方式="请手动检查状态",
            color="red",
        )
        send_text("message_card", tony, message.to_dict(), "p2p")
    except Exception as e:
        print(f"Error in handle_cloud_update_error: {e}")
    # 返回None，允许任务链继续执行
    return None


@celery.task(name='check-no-pr-for-task-1-1')
def check_no_pr_for_task_1_1():
    return chain(
            group(
                check_no_pr.s(repo="tapdata", task_prefix="1-1"),
                check_no_pr.s(repo="tapdata-enterprise", task_prefix="1-1"),
            ),
            finish_tasks.s(),
        ).apply_async()


@celery.task(name='check-no-pr-for-task-1-2')
def check_no_pr_for_task_1_2():
    return chain(
            check_no_pr.s(repo="tapdata-cloud", task_prefix="1-2"),
            finish_tasks.s(),
        ).apply_async()


@celery.task(name='check-no-pr-for-task-1-3')
def check_no_pr_for_task_1_3():
    return chain(
            check_no_pr.s(repo="tapdata-enterprise-web", task_prefix="1-3"),
            finish_tasks.s(),
        ).apply_async()


@celery.task(name='check-no-pr-for-task-1-4')
def check_no_pr_for_task_1_4():
    return chain(
            group(
                check_no_pr.s(repo="tapdata-connectors", task_prefix="1-4"),
                check_no_pr.s(repo="tapdata-connectors-enterprise", task_prefix="1-4"),
            ),
            finish_tasks.s(),
        ).apply_async()


@celery.task(name='check-no-content-to-merge-for-task-1-5')
def check_no_content_to_merge_for_task_1_5():
    return chain(
            group(
                chain(
                    check_no_pr.s(repo="tapdata", source_branch="main", target_branch="develop", task_prefix="1-5"),
                    check_no_content_to_merge.s(repo="tapdata", source_branch="main", target_branch="develop",
                                                task_prefix="1-5", notification_group=group_id, at_user_id=sam),
                ),
                chain(
                    check_no_pr.s(repo="tapdata-enterprise", source_branch="main", target_branch="develop",
                                  task_prefix="1-5"),
                    check_no_content_to_merge.s(repo="tapdata-enterprise", source_branch="main", target_branch="develop",
                                                task_prefix="1-5", notification_group=group_id, at_user_id=sam),
                ),
            ),
            finish_tasks.s(),
        ).apply_async()


@celery.task(name='check-no-content-to-merge-for-task-1-6')
def check_no_content_to_merge_for_task_1_6():
    return chain(
            group(
                chain(
                    check_no_pr.s(repo="tapdata-connectors", source_branch="main", target_branch="develop",
                                  task_prefix="1-6"),
                    check_no_content_to_merge.s(repo="tapdata-connectors", source_branch="main", target_branch="develop",
                                                task_prefix="1-6", notification_group=group_id, at_user_id=jarad),
                ),
                chain(
                    check_no_pr.s(repo="tapdata-connectors-enterprise", source_branch="main", target_branch="develop",
                                  task_prefix="1-6"),
                    check_no_content_to_merge.s(repo="tapdata-connectors-enterprise", source_branch="main",
                                                target_branch="develop", task_prefix="1-6", notification_group=group_id, at_user_id=jarad),
                ),
            ),
            finish_tasks.s(),
        ).apply_async()


@celery.task(name='count-aliyun-fee-everyday', bind=True, max_retries=2, default_retry_delay=60)
def count_aliyun_fee_everyday(self, access_key_id, access_key_secret):

    def percent_encode(string):
        return quote(string.encode('utf-8'), '').replace('+', '%20').replace('*', '%2A').replace('%7E', '~')

    def generate_signature(parameters):
        sorted_parameters = sorted(parameters.items())
        query_string = urlencode(sorted_parameters)
        string_to_sign = f"GET&%2F&{percent_encode(query_string)}"
        secreted_string = f"{access_key_secret}&"
        h = hmac.new(secreted_string.encode('utf-8'), string_to_sign.encode('utf-8'), hashlib.sha1)
        signature = base64.b64encode(h.digest()).decode('utf-8')
        return signature

    def get_daily_expense():
        endpoint = 'https://business.aliyuncs.com'
        action = 'DescribeInstanceBill'
        today = datetime.now().strftime('%Y-%m-%d')

        params = {
            'Action': action,
            'BillingCycle': today,
            'Format': 'JSON',
            'Version': '2017-12-14',
            'AccessKeyId': access_key_id,
            'Granularity': 'DAILY',
            'BillingDate': today,
            'SignatureMethod': 'HMAC-SHA1',
            'SignatureVersion': '1.0',
            'SignatureNonce': str(int(time.time())),
            'Timestamp': datetime.utcnow().strftime('%Y-%m-%dT%H:%M:%SZ')
        }

        signature = generate_signature(params)
        params['Signature'] = signature
        response = requests.get(endpoint, params=params, verify=False)
        data = response.json()

        return data

    def format_expense_data(data):
        if 'Data' not in data or 'Items' not in data['Data']:
            return {}

        items = data['Data']['Items']
        total_amount = sum(float(item['PretaxAmount']) for item in items)
        total_amount_without_discount = sum(
            float(item['InvoiceDiscount']) + float(item['PretaxAmount']) for item in items)

        rows = []
        for item in items:
            row = {
                "sku_id": item['InstanceID'],
                "sku": item['ProductName'],
                "payment": f"{float(item['PretaxAmount']):.2f}",
                "total": f"{float(item['PretaxAmount']) + float(item['InvoiceDiscount']):.2f}"
            }
            # 金额小于1 忽略
            if float(row['payment']) < 1:
                continue
            rows.append(row)

        formatted_data = {
            "template_id": "AAqH4XBKPrk78",
            "template_version_name": "1.0.2",
            "template_variable": {
                "startAt": datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ'),
                "endAt": (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%dT%H:%M:%SZ'),
                "amount": f"{total_amount:.2f}",
                "totalAmount": f"{total_amount_without_discount:.2f}",
                "rows": rows,
                "src": "Aliyun",
                "currency": "¥",
            }
        }

        return formatted_data

    def _send_message_to_feishu(data):
        webhook_url = 'https://open.feishu.cn/open-apis/bot/v2/hook/1b0793fe-a8f3-42dc-9937-d78add5aa3b2'
        headers = {'Content-Type': 'application/json'}
        payload = {
            "msg_type": "interactive",
            "card": {
                "type": "template",
                "data": data
            },

        }
        response = requests.post(webhook_url, headers=headers, data=json.dumps(payload))
        return response.status_code

    expense_data = get_daily_expense()
    formatted_data = format_expense_data(expense_data)
    status_code = _send_message_to_feishu(formatted_data)
    if status_code == 200:
        print("消息发送成功")
    else:
        print("消息发送失败")
