# -*-coding:utf-8-*-
"""
获取选项信息

"""
import json
import re
import pickle
import inspect
import sys

from utils.session import Session
from . import common
from . import message_card
from . import exception
from .connector import MongoConnector


def find_method(method_name):
    current_module = inspect.currentframe().f_back.f_globals['__name__']
    current_methods = inspect.getmembers(sys.modules[current_module], inspect.isfunction)
    for name, method in current_methods:
        if name == method_name:
            return method
    return None


class Option:

    def __init__(self, open_id: str):
        self.open_id = open_id

    def find_option_value_in_list(self, *options):
        """
        从配置列表中找到选项对应的配置值

        :param options: 选项列表
        """
        option_list = [f"cache {op}" for op in options]
        ret = Session().find(self.open_id, *options, *option_list)
        return common.parse_branch(ret, len(options))

    def find_value(self, key):
        """缓存中找到配置值"""
        ret = Session().find(self.open_id, key)
        return ret

    def find_branch(self,
                    tapdata_branch: str,
                    tapdata_enterprise_branch: str,
                    tapdata_enterprise_web_branch: str,
                    tapdata_cloud_branch: str = None
                    ):
        """
        获取当前分支
        :param tapdata_branch: 开源仓库分支
        :param tapdata_enterprise_branch: 闭源仓库分支
        :param tapdata_enterprise_web_branch: 前端仓库分支
        :param tapdata_cloud_branch: 云版仓库分支
        :return tapdata, tapdata-enterprise, frontend_branch, cloud_branch(if cloud)
        """
        if tapdata_cloud_branch is None:
            return self.find_option_value_in_list(
                tapdata_branch, tapdata_enterprise_branch, tapdata_enterprise_web_branch
            )
        else:
            return self.find_option_value_in_list(
                tapdata_branch, tapdata_enterprise_branch, tapdata_enterprise_web_branch, tapdata_cloud_branch
            )

    def find_branch_in_console(self,
                               tapdata_branch: str,
                               tapdata_enterprise_branch: str,
                               tapdata_enterprise_web_branch: str,
                               tapdata_cloud_branch: str = None
                               ):
        """
        获取当前分支
        传入的参数有两种模式，1.分支名 2.分支列表的index

        :param tapdata_branch: 开源仓库分支
        :param tapdata_enterprise_branch: 闭源仓库分支
        :param tapdata_enterprise_web_branch: 前端仓库分支
        :param tapdata_cloud_branch: 云版仓库分支
        :return tapdata, tapdata-enterprise, frontend_branch, cloud_branch(if cloud)
        """
        # 模式1 分支名
        if not re.match(r"[0-9]+", tapdata_branch):
            if tapdata_cloud_branch is None:
                return tapdata_branch, tapdata_enterprise_branch, tapdata_enterprise_web_branch
            return tapdata_branch, tapdata_enterprise_branch, tapdata_enterprise_web_branch, tapdata_cloud_branch
        # 模式2 分支列表的index
        if tapdata_cloud_branch is None:
            branches = Session().find(
                self.open_id, "enterprise_branch", "opensource_branch", "frontend_branch",
            )
            branch_result = [
                None if branches[0] is None else json.loads(branches[0]).get(tapdata_enterprise_branch),
                None if branches[1] is None else json.loads(branches[1]).get(tapdata_branch),
                None if branches[2] is None else json.loads(branches[2]).get(tapdata_enterprise_web_branch),
            ]
        else:
            branches = Session().find(
                self.open_id, "enterprise_branch", "opensource_branch", "frontend_branch", "cloud_branch",
            )
            branch_result = [
                None if branches[0] is None else json.loads(branches[0]).get(tapdata_enterprise_branch),
                None if branches[1] is None else json.loads(branches[1]).get(tapdata_branch),
                None if branches[2] is None else json.loads(branches[2]).get(tapdata_enterprise_web_branch),
                None if branches[3] is None else json.loads(branches[3]).get(tapdata_cloud_branch),
            ]
        for branch in branch_result:
            if branch is None:
                return None
        return branch_result


def convert_frontend_mode(ret):
    if ret == "默认":
        return "production"
    elif ret == "海事局":
        return "msa"
    elif ret == "IKAS":
        return "ikas"
    elif ret == "周生生":
        return "chowsangsang"
    elif ret == "数存OEM":
        return "datapp"
    elif ret == "normal":
        return "normal"
    elif ret == "OEM":
        return "oem"
    elif ret == "蒸汽云数":
        return "steamory"
    elif ret == "万应低代码":
        return "onein"
    elif ret == "数存代理商":
        return "datapp_normal"
    elif ret == "HA医管局":
        return "ha"
    raise exception.InternalError


def convert_true_false(ret):
    return ret == "是"


def connectors_convert(record):
    if record == "默认":
        return "activemq-connector,clickhouse-connector,csv-connector,custom-connector,db2-connector,doris-connector," \
               "dummy-connector,elasticsearch-connector,excel-connector,json-connector,kafka-connector," \
               "mariadb-connector,mongodb-connector,mssql-connector,mysql-connector,oracle-connector," \
               "rabbitmq-connector,redis-connector,rocketmq-connector,tidb-connector,xml-connector,postgres-connector"
    else:
        return ""


def convert_cloud_type(record):
    cloud_map = {
        "青云": "qingcloud",
        "阿里云-北京": "aliCloud-Beijing",
        "阿里云-香港": "aliCloud-HongKong",
        "华为云": "huaweiCloud",
    }
    return cloud_map.get(record, cloud_map.values())


class BaseOption:
    """
    选项
    - 以键值对存在，键为选项的名称，值为一个选项列表
    - 当选择某个选项，可以获取键名称和选项的列表的索引id+1，以此找到用户的选项
    - 会有两条缓存，一条存储选项列表，一条存储选项值，redis中的key分别以list_cache和value_cache结尾
    - 每条缓存以action_id和open_id来区分用户和操作
    """

    option_list: list = []  # 选项列表
    option_key = ""  # 选项Key
    initial_option = None  # 默认值 为选项列表值的索引ID +1
    div_name = ""  # 卡片显示的选项名称
    fn = None  # 回调函数

    def __init__(self, action_id, open_id, **kwargs):
        """
        :param action_id: 操作ID
        :param open_id: 用户ID
        :param kwargs:
        """
        self.action_id = action_id
        self.open_id = open_id
        for key, value in kwargs.items():
            setattr(self, key, value)

    @property
    def _option_list_cache(self):
        # 选项列表
        return f"{self.action_id}#{self.open_id}#{self.option_key}#list_cache"

    @property
    def _option_value_cache(self):
        # 选项值
        return f"{self.action_id}#{self.open_id}#{self.option_key}#value_cache"

    @staticmethod
    def parse_option_key(option_key: str):
        action_id, open_id, option_key = option_key.split("#")
        return action_id, open_id, option_key

    def cache_list(self):
        # 缓存选项列表
        Session().add_session(self.open_id, self._option_list_cache, self.option_list)

    def cache_value(self, t=None):
        # 缓存默认值
        print(f"t is: {t}, type t is: {type(t)}")
        if t is None and self.initial_option is not None:
            Session().add_session(self.open_id, self._option_value_cache, self.initial_option)
        if t is not None:
            if isinstance(t, int):
                Session().add_session(self.open_id, self._option_value_cache, t)
            elif isinstance(t, str):
                Session().add_session(self.open_id, self._option_value_cache, self.option_list.index(t)+1)

    def make_select(self, initial_option=None, placeholder="请选择") -> message_card.Div:
        """
        选项卡片
        """

        initial_option = initial_option if initial_option else self.initial_option

        kwargs = {}
        if initial_option is not None:
            kwargs.update({"initial_option": str(initial_option)})
        if placeholder is not None:
            kwargs.update({"placeholder": message_card.Text(placeholder)})

        self.cache_list()

        return message_card.make_select_div(
            self.div_name,
            self.option_list,
            self._option_value_cache,
            **kwargs
        )

    def _get_value(self, ret):
        """获取默认值"""
        cache_value, cache_list = ret
        print(f"cache_value is: {cache_value}, cache_list is: {cache_list}")
        if cache_list is None:
            if self.option_list is None:
                raise exception.OptionListNotFound
            else:
                cache_list = str(self.option_list)
        if cache_value is not None:
            return eval(cache_list)[int(cache_value) - 1]
        if self.initial_option is None:
            return None
        else:
            return eval(cache_list)[int(self.initial_option) - 1]

    def get_option(self, fn=None):
        """获取选项值
        :param fn: 回调函数
        """
        # 当找不到缓存时，会返回None
        ret = Session().find(self.open_id, f"{self._option_value_cache}", self._option_list_cache)
        ret = self._get_value(ret)
        if fn is not None:
            return fn(ret)
        elif self.fn is not None:
            return type(self).fn(ret)
        else:
            return ret


class OptionGroupCache(BaseOption):
    """用来缓存选项组OptionGroup的option_list"""
    option_key = "option_group"
    div_name = "选项组类"


class OptionGroupCacheHook(BaseOption):
    option_key = "option_group_hook"
    div_name = "选项组类钩子"
    option_list = ["convert_true_false"]
    initial_option = 1


OptionList = [dict]


class OptionGroup:
    """
    选项组
    """
    def __init__(self, job_id, open_id, option_list: OptionList, fn=None):
        """
        :param job_id: 任务ID
        :param open_id: 用户ID
        :param option_list: 选项组列表,
        - 格式为[{'option_key': 'xxx', 'option_list': [xxx, xxx, xxx], 'initial_option': 1, 'div_name': 'xxx', 'fn': xxx}]
        """
        self.option_list = option_list
        self.job_id = job_id
        self.open_id = open_id
        self.fn = fn
        self.option_class_list = self._make_option_class_list()

    def _get_fn(self):
        if self.fn is not None:
            return self.fn
        else:
            method_name = OptionGroupCacheHook(self.job_id, self.open_id).get_option()
            return find_method(method_name)

    def _make_option_class_list(self):
        option_class_list = []
        for option in self.option_list:
            option.update({"action_id": self.job_id, "open_id": self.open_id, "fn": self._get_fn()})
            option_class_list.append(type("Option", (BaseOption,), option))
        return option_class_list

    def cache_option_list(self):
        option_list = []
        for option in self.option_list:
            if option.get("fn"):
                option.pop("fn")
            option_list.append(option)
        ogc = OptionGroupCache(self.job_id, self.open_id, option_list=[json.dumps(option_list)])
        ogc.cache_list()
        ogc.cache_value(1)
        ogch = OptionGroupCacheHook(self.job_id, self.open_id)
        ogch.cache_value(self.fn.__name__)

    def make_select(self, initial_option=None, placeholder="请选择") -> [message_card.Div]:
        """
        选项卡片列表
        """
        div_list = []
        for option_class in self.option_class_list:
            print("option_class is: ", option_class)
            print("type option_class is: ", type(option_class))
            div_list.append(option_class(self.job_id, self.open_id).make_select(initial_option, placeholder))
        self.cache_option_list()
        return div_list

    def get_option(self, option_key, fn=None):
        """
        匹配option_key，获取选项值
        :param fn: 回调函数
        """
        for option_class in self.option_class_list:
            if option_class.option_key == option_key:
                return option_class().get_option(fn)
        raise exception.OptionKeyNotFound

    def get_option_list(self):
        """获取选项组所有的key，返回一个键值对，key为option_key，value为get_option的返回值"""
        option_dict = {}
        for option_class in self.option_class_list:
            option_dict.update({option_class.option_key: option_class(self.job_id, self.open_id).get_option()})
        print("option_dict is: ", option_dict)
        return option_dict


class EnableGray(BaseOption):
    option_key = "enable_gray"
    option_list = ["否", "是"]
    initial_option = 1
    div_name = "是否启用灰度"
    fn = convert_true_false


class ResourcePoll:
    """资源池"""
    agent_info = {
        "cloud-dev": {
            "mongo_uri": "mongodb://root:Gotapd8!@58.251.34.123:37017/dev_dfs_tcm?authSource=admin&directConnection=true",
            "database": "dev_dfs_tcm",
            "tmServerUrl": 'https://dev.cloud.tapdata.net/console/tm/api/',
            "downloadUrl": 'https://resource.tapdata.net/package/test/dfs-{version}/',
        },
        "cloud-test": {
            "mongo_uri": "mongodb://root:Gotapd8!@58.251.34.123:37017/test_dfs_tcm?authSource=admin&directConnection=true",
            "database": "test_dfs_tcm",
            "tmServerUrl": 'https://test.cloud.tapdata.net/console/tm/api/',
            "downloadUrl": 'https://resource.tapdata.net/package/test/dfs-{version}/',
        },
        "cloud-uat": {
            "mongo_uri": "mongodb://34.96.213.48:32000/admin?authSource=admin&directConnection=true",
            "database": "uat_dfs_tcm",
            "tmServerUrl": 'https://uat.cloud.tapdata.net/console/tm/api/',
            "downloadUrl": 'https://resource.tapdata.net/package/test/dfs-{version}/',
        },
        "cloud-prod": {
            "mongo_uri": 'mongodb://root:XikIlSIIvWRP@172.19.1.5:37017/dfs_gw?authSource=admin&directConnection=true',
            "database": "dfsTcm3",
            "tmServerUrl": 'https://cloud.tapdata.io/console/tm/api/',
            "downloadUrl": 'https://resource.tapdata.net/package/feagent/dfs-{version}/',
        },
        "cloud-net": {
            "mongo_uri": 'mongodb://root:XikIlSIIvWRP@172.19.1.5:37017/dfs_gw?authSource=admin&directConnection=true',
            "database": "net-dfsTcm3",
            "tmServerUrl": 'https://cloud.tapdata.net/console/v3/tm/api/',
            "downloadUrl": 'https://resource.tapdata.net/package/feagent/dfs-{version}/',
        },
    }

    @classmethod
    def get_resource_pool(cls, cloud_env):
        """获取资源池列表
        从数据库表drs_k8s_config中获取记录，如果不存在返回空列表"""
        mongo_uri = cls.agent_info[cloud_env]["mongo_uri"]
        database = cls.agent_info[cloud_env]["database"]
        mongo_cli = MongoConnector(mongo_uri)
        query = {
            "enabled": True
        }
        print("drs_k8s_config data: ", mongo_cli.get(database, "drs_k8s_config", query))
        print("type is ", type(mongo_cli.get(database, "drs_k8s_config", query)))
        return [{
            "region": i["region"],
            "zone": i["zone"],
            "provider": i["provider"],
        } for i in mongo_cli.get_many(database, "drs_k8s_config", query)]

    @classmethod
    def format_option_key(cls, resource_poll):
        return f"{resource_poll['provider']}_{resource_poll['region']}_{resource_poll['zone']}"

    @classmethod
    def make_select(cls, job_id, open_id, cloud_env) -> [message_card.Div]:
        """生成选项卡片"""
        option_list = []
        for resource_poll in cls.get_resource_pool(cloud_env):
            option_list.append({
                "option_key": cls.format_option_key(resource_poll),
                "option_list": ["否", "是"],
                "div_name": cls.format_option_key(resource_poll),
                "initial_option": 2,
            })
        return OptionGroup(job_id, open_id, option_list, fn=convert_true_false).make_select()


class EnterpriseBranch(BaseOption):
    option_key = "enterprise_branch"
    div_name = "Tapdata-Enterprise分支"


class OpenSourceBranch(BaseOption):
    option_key = "opensource_branch"
    div_name = "Tapdata分支"


class ReleaseVersion(BaseOption):
    option_key = "release_version"
    div_name = "选择版本"

    def __init__(self, action_id, open_id, **kwargs):
        super().__init__(action_id, open_id, **kwargs)
        self.option_list = list(common.all_release_branch().keys())


class ConnectorsBranch(BaseOption):
    option_key = "connectors_branch"
    div_name = "Connectors分支"


class ConnectorsEnterpriseBranch(BaseOption):
    option_key = "connectors_enterprise_branch"
    div_name = "EnterpriseConnectors分支"


class FrontendBranch(BaseOption):
    option_key = "frontend_branch"
    div_name = "Console分支"


class TapFlowBranch(BaseOption):
    option_key = "tapflow_branch"
    div_name = "TapFlow分支"


class CloudBranch(BaseOption):
    option_key = "cloud_branch"
    div_name = "云版分支"


class LicenseBranch(BaseOption):
    option_key = "license_branch"
    div_name = "Tapdata-License分支"
    

class RunTest(BaseOption):
    option_key = "run_test"
    div_name = "是否运行测试"
    option_list = ["是", "否"]
    initial_option = 2  # 默认不运行测试
    fn = convert_true_false

class IncludeApiServer(BaseOption):
    option_key = "include_apiserver"
    div_name = "是否包含API服务"
    option_list = ["是", "否"]
    initial_option = 2  # 默认不包含
    fn = convert_true_false


class Deployment(BaseOption):
    option_key = "deployment"
    div_name = "更新的环境"


class ImageTagList(BaseOption):
    option_key = "image_tag_list"
    div_name = "镜像版本列表"


class PushToQingcloud(BaseOption):
    option_key = "push_to_qingcloud"
    option_list = ["否", "是"]
    initial_option = 2
    div_name = "推送到青云镜像仓库"
    fn = convert_true_false


class OpenSourceCompile(BaseOption):
    option_key = "opensource_compile"
    option_list = ["否", "是"]
    initial_option = 1
    div_name = "编译开源版本"


class PushToCoding(BaseOption):
    option_key = "push_to_coding"
    option_list = ["否", "是"]
    initial_option = 1
    div_name = "推送到Coding镜像仓库"
    fn = convert_true_false


class PushToGithubRepo(BaseOption):
    option_key = "push_to_github_repo"
    option_list = ["否", "是"]
    initial_option = 1
    div_name = "推送到Github镜像仓库"
    fn = convert_true_false


class PushToPrivateRepo(BaseOption):
    option_key = "push_to_private_repo"
    option_list = ["否", "是"]
    initial_option = 2
    div_name = "推送到私有镜像仓库"
    fn = convert_true_false


class IsPackageTar(BaseOption):
    option_key = "is_package_tar"
    option_list = ["否", "是"]
    initial_option = 1
    div_name = "输出压缩包制品"
    fn = convert_true_false


class IsDockerSave(BaseOption):
    option_key = "is_docker_save"
    option_list = ["否", "是"]
    initial_option = 1
    div_name = "输出镜像压缩包"
    fn = convert_true_false


class BuildFull(BaseOption):
    option_key = "build_full"
    option_list = ["否", "是"]
    initial_option = 2
    div_name = "打包数据源和pdk"
    fn = convert_true_false


class FrontendMode(BaseOption):
    option_key = "frontend_mode"
    option_list = ["默认", "HA医管局", "海事局", "IKAS", "周生生", "数存OEM", "Normal", "OEM", "蒸汽云数", "万应低代码", "数存代理商"]
    initial_option = 1
    div_name = "前端mode参数"
    fn = convert_frontend_mode


class PlatformOption(BaseOption):
    option_key = "platform_option"
    option_list = ["Linux", "Windows"]
    initial_option = 1
    div_name = "平台选择"


class UpdateConsole(BaseOption):
    option_key = "update_console"
    option_list = ["是", "否"]
    initial_option = 1
    div_name = "更新Console"
    fn = convert_true_false


class UpdateTapFlow(BaseOption):
    option_key = "update_tapflow"
    option_list = ["是", "否"]
    initial_option = 2
    div_name = "更新TapFlow"
    fn = convert_true_false


class UpdateTCM(BaseOption):
    option_key = "update_tcm"
    option_list = ["是", "否"]
    initial_option = 1
    div_name = "更新TCM"
    fn = convert_true_false


class UpdateTM(BaseOption):
    option_key = "update_tm"
    option_list = ["是", "否"]
    initial_option = 1
    div_name = "更新TM"
    fn = convert_true_false


class UpdateAgent(BaseOption):
    option_key = "update_agent"
    option_list = ["是", "否"]
    initial_option = 2
    div_name = "更新Agent/TapdataAgent"
    fn = convert_true_false


class UpdateTicket(BaseOption):
    option_key = "update_ticket"
    option_list = ["是", "否"]
    initial_option = 2
    div_name = "更新Ticket"
    fn = convert_true_false


class UpdateTapdataAgent(BaseOption):
    option_key = "update_tapdata_agent"
    option_list = ["是", "否"]
    initial_option = 1
    div_name = "更新Tapdata-Agent"
    fn = convert_true_false


class UpdateConnector(BaseOption):
    option_key = "update_connector"
    option_list = ["是", "否"]
    initial_option = 1
    div_name = "注册数据源"
    fn = convert_true_false


class ImageRepo(BaseOption):
    option_key = "image_repo"
    option_list = ["dockerhub.qingcloud.com",
                   "registry.cn-beijing.aliyuncs.com",
                   "registry.cn-hongkong.aliyuncs.com",
                   "asia-docker.pkg.dev/crypto-reality-377106",
                   "swr.ap-southeast-1.myhuaweicloud.com"]
    initial_option = 3


class CloudEnv(BaseOption):
    option_key = "cloud_env"
    option_list = ["cloud-dev", "cloud-test", "cloud-uat", "cloud-net", "cloud-prod", "prod3", "gcp-gray", "gcp-prod", "gcp-net", "gcp-dev", "gcp-test", "gcp-uat"]
    initial_option = 1


class AgentVersion(BaseOption):
    option_key = "agent_version"
    div_name = "Agent版本"


class ImageVersion(BaseOption):
    option_key = "sync_image_version"
    div_name = "镜像版本"


class Connectors(BaseOption):
    option_key = "connector"
    div_name = "数据源（默认全部）"


class SourceCloudType(BaseOption):
    option_key = "source_cloud_type"
    div_name = "源云商类型"
    option_list = ["qingcloud", "aliCloud-Beijing", "coding"]
    initial_option = 3


class TargetCloudType(BaseOption):
    option_key = "target_cloud_type"
    option_list = ["所有可用云商", "青云", "阿里云-北京", "阿里云-香港", "GCP"]
    div_name = "目标云商类型"
    initial_option = 1
    fn = convert_cloud_type


class Components(BaseOption):
    option_key = "tapdata_cloud_components"
    option_list = ["dfs-flow-engine", "dfs-gateway", "dfs-tm-java", "dfs-tcm", "dfs-console"]
    div_name = "Components"
    initial_option = 1


class OpConnectors(BaseOption):
    option_key = "op_connector"
    option_list = ["全部"]
    div_name = "数据源"
    initial_option = 1
    fn = connectors_convert


class JavaVersion(BaseOption):
    option_key = "java_version"
    option_list = ["java8", "java11", "java17"]
    div_name = "Java版本"
    initial_option = 3


class ConnectorsOptions(BaseOption):
    option_key = "connectors_options"
    option_list = ["nightly-build", "performance-test", "lightweight", "all"]
    div_name = "数据源选项"
    initial_option = 4


class SaveTemplateOptions(BaseOption):
    option_key = "save_template"
    option_list = ["否", "是"]
    initial_option = 1
    div_name = "保存为构建模版"
    fn = convert_true_false


DOCKER_REGISTRY = {
    "qingcloud": {
        "registry": "dockerhub.qingcloud.com",
        "component": {
            "dfs-tcm": "tapdata",
            "dfs-tm-java": "tapdata",
            "dfs-console": "tapdata",
            "dfs-gateway": "tapdata",
            "dfs-flow-engine": "tapdata",
        },
        "login": "docker login -u Jerry -p Gotapd8! dockerhub.qingcloud.com",
    },  # 青云
    "aliCloud-Beijing": {
        "registry": "registry.cn-beijing.aliyuncs.com",
        "component": {
            "dfs-tcm": "tapdata",
            "dfs-tm-java": "tapdata",
            "dfs-console": "tapdata",
            "dfs-gateway": "tapdata",
            "dfs-flow-engine": "tapdata",
        },
        "login": "docker login --username=sysadmin@1809821306098986 registry.cn-beijing.aliyuncs.com -p Gotapd8!",
    },  # 阿里云 北京
    "aliCloud-HongKong": {
        "registry": "registry.cn-hongkong.aliyuncs.com",
        "component": {
            "dfs-tcm": "tapdata",
            "dfs-tm-java": "tapdata",
            "dfs-console": "tapdata",
            "dfs-gateway": "tapdata",
            "dfs-flow-engine": "tapdata",
        },
        "login": "docker login --username=sysadmin@1809821306098986 registry.cn-hongkong.aliyuncs.com -p Gotapd8!",
    },  # 阿里云 香港
    "huaweiCloud": {
        "registry": "swr.ap-southeast-1.myhuaweicloud.com",
        "component": {
            "dfs-flow-engine": "tapdata",
            "dfs-gateway": "tapdata",
            "dfs-tm-java": "tapdata",
            "dfs-tcm": "tapdata",
            "dfs-console": "tapdata",
        },
        "login": "docker login -u ap-southeast-1@5IWVYC6OA687WXVJDPQD -p 9de4acd4311d0ce31976549325c0ec1ca4282b05c52ca6465b6b0ed5bc9a65fb swr.ap-southeast-1.myhuaweicloud.com",
    },  # 华为云 香港
    "coding": {
        "registry": "tapdata-docker.pkg.coding.net",
        "component": {
            "dfs-flow-engine": "dfs/flow-engine",
            "dfs-tm-java": "tapdata/tapdata",
            "dfs-tcm": "tapdata/tapdata",
            "dfs-console": "tapdata/tapdata",
            "dfs-gateway": "tapdata/tapdata",
        },
        "login": "docker login -u tapdata-1668675309097 -p 7548670226e4ab592246dd0fd11ae96b3a6104b1 tapdata-docker.pkg.coding.net"
    },  # Coding
}
