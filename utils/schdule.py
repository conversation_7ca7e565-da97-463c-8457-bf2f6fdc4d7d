"""
任务执行

@author: <PERSON>
@data: 2022.08.18
"""
from __future__ import annotations

import copy
import ctypes
import inspect
import json
import logging
import os
import sys
import threading
import time
import uuid
import re
import signal
import typing as t
from threading import Thread, Event
import traceback
from datetime import datetime

import redis
from croniter import croniter
from croniter import CroniterBadCronError
import gevent

from model.job import Job as Job_model
from lib.api import send_text
from utils.connector import redis_cli
from utils.init import app, db
from utils.session import Session
from utils.exception import CronJobSetError, SystemClose
from fuzzywuzzy import fuzz
from utils.user_setup import UserSetup


class Context:
    """
    上下文管理器
    """

    def __init__(self, **kwargs):
        self.command = kwargs["command"]
        self.open_id = kwargs["open_id"]
        self.chat_type = kwargs["chat_type"]
        self.context = kwargs
        self.quite = kwargs.get("quite", False)

    def add_item(self, key, value):
        setattr(self, key, value)
        self.context[key] = value

    def __getattr__(self, item):
        if item in self.__dir__():
            return getattr(self, item)
        return self.context.get(item, None)

    def to_dict(self):
        self.context.update({
            "command": self.command,
            "open_id": self.open_id,
            "chat_type": self.chat_type,
            "quite": self.quite
        })
        return self.context


class CommandParser:
    handler_callback_map = dict()

    @staticmethod
    def register(cls: t.Callable):
        def decorator(*args, **kwargs):
            instance = cls(*args, *kwargs)
            CommandParser.register_handler_with_command(cls.command, cls)
            return instance

        return decorator

    @staticmethod
    def register_handler_with_command(command, handler: t.Callable):
        CommandParser.handler_callback_map[command] = handler

    @staticmethod
    def get_msg_with_command(command):
        commands = command.split(" ")

        app.logger.info(f"[CommandParser] command is: {command}, commands is: {commands}")
        if commands[0] == "help" or commands[0] == "操作":
            app.logger.info(f"commands 0 is {commands[0]}, will use help callback")
            return CommandParser.handler_callback_map.get("help||h")().ret_msg

        if command == "云版商家情况" or "云版商家" in command:
            app.logger.info(f"commands 0 is {commands[0]}, will use cloud bussiness t callback")
            return CommandParser.handler_callback_map.get("云版商家情况")().ret_msg

        if "商家名字是" in command:
            return CommandParser.handler_callback_map.get("标记云版用户||是云版 VIP 用户||是云版普通用户||是云版付费用户||商家名字是")().ret_msg

        chat_start = ["咨询", "询问", "求助", "ask", "AI"]
        for s in chat_start:
            if str(command).startswith(s):
                return CommandParser.handler_callback_map.get("chat")().ret_msg

        if CommandParser.handler_callback_map.get(commands[0]) is not None:
            return CommandParser.handler_callback_map.get(commands[0])().ret_msg
        best_match_fn = {
            "score": 0,
            "fn": None
        }
        for k, fn in CommandParser.handler_callback_map.items():
            ks = [k]
            if "||" in k:
                ks = k.split("||")
            for kk in ks:
                if len(kk) >= 5 and kk in command:
                    best_match_fn = {
                        "score": 80,
                        "fn": fn,
                    }
                    break
                score = fuzz.token_set_ratio(command, kk)
                if score > best_match_fn["score"]:
                    best_match_fn = {
                        "score": score,
                        "fn": fn,
                    }
                if score < 40:
                    score1 = fuzz.token_set_ratio(command.split(":")[0], kk)
                    score2 = fuzz.token_set_ratio(command.split(" ")[0], kk)
                    score = max(score1, score2)
                    if score > best_match_fn["score"]:
                        best_match_fn = {
                            "score": score,
                            "fn": fn,
                        }

        if best_match_fn["score"] > 45:
            app.logger.info(f"[CommandParser] command is: {command}, fn is: {best_match_fn['fn']}")
            return best_match_fn["fn"]().ret_msg

        app.logger.info(f"[CommandParser] command is: {command}, fn is: CHAT")
        print(CommandParser.handler_callback_map)
        return CommandParser.handler_callback_map.get("chat")().ret_msg


class Status:
    running = "running"
    scheduling = "scheduling"
    completed = "completed"
    failure = "failure"


class Job:

    def __init__(self, context: Context, handler: t.Callable):
        """
        任务
        :param handler: handler function
        """
        self.id = context.job_id
        self.open_id = context.open_id
        self.handler = handler
        self.command = context.command
        self.status = Status.scheduling
        self.chat_type = context.chat_type
        self.context = context

    def _register_help(self):
        if self.chat_type == "p2p":
            self.context.command = "help"
            Schedule().register(self.context)
        elif self.chat_type == "group" and self.context.receive_text.startswith("@" + os.getenv("FEISHU_ROBOT_NAME")):
            self.context.command = "help"
            Schedule().register(self.context)

    def run(self):
        if self.handler is None:
            return self._register_help()
        try:
            app.logger.info("任务开始运行，任务ID: %s, 任务命令: %s" % (self.id, self.command))
            ret = self.handler(**self.context.to_dict())
            app.logger.info("任务运行成功，任务ID: %s, 任务命令: %s" % (self.id, self.command))
            return ret
        except Exception as e:
            app.logger.error(traceback.format_exc())
            app.logger.info("任务运行失败，任务ID: %s, 任务命令: %s" % (self.id, self.command))
            app.logger.info("开始发送失败通知...")
            send_text("text", self.open_id, str(e), self.chat_type)
            app.logger.info("发送失败通知结束...")
            app.logger.warn("%s" % traceback.format_exc())
            return False


class Actuator:
    """执行器"""

    def __init__(self, key: bytes, is_cronjob=False):
        context = Context(**json.loads(key.decode("utf8")))
        self.context = context
        self._format_command()
        self.job_id, self.command, self.open_id, self.chat_type = \
            context.job_id, context.command, context.open_id, context.chat_type
        self.job: Job_model = None
        self.is_cronjob = is_cronjob

    def _format_command(self):
        if "云版任务" not in self.context.command and "云版报错分析" not in self.context.command:
            command = self.context.command.split("|")
            first_command = command[0].split("@")[0].strip()
            if len(command[1:]) > 0:
                command = " | ".join([first_command] + [c.strip() for c in command[1:] if len(c.strip()) > 0])
            else:
                command = first_command
            self.context.command = command

    def _create_job(self):
        app.logger.info("开始创建新任务，保存到数据库, id: %s" % self.job_id)
        self.job = Job_model(
            id=self.job_id,
            status=Status.scheduling,
            command=self.command[:100],
            operator=self.open_id,
            create_at=datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        )
        db.session.add(self.job)
        db.session.commit()
        app.logger.info("保存到数据库成功, id: %s" % self.job_id)

    def _set_job(self):
        job = Job_model.query.filter_by(id=self.job_id).first()
        if job is not None:
            self.job = job
        else:
            self._create_job()

    def _update_status(self, status):
        app.logger.info("开始更新任务状态, id: %s" % self.job_id)
        if self.job is None:
            app.logger.error("Job instance is None")
        self.job.status = status
        db.session.commit()
        app.logger.info("更新任务状态成功, id: %s" % self.job_id)

    def _start_job(self):
        print(self.command)
        handler = CommandParser.get_msg_with_command(self.command)
        job = Job(self.context, handler)
        self.job.start_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        db.session.commit()
        return job.run()

    def make_now(self):
        now_time = datetime.now()
        now = str(now_time)
        return now

    def update_session(self, ret, clear_cache=True):
        if isinstance(ret, dict):
            for key, value in ret.items():
                if isinstance(value, dict) or isinstance(value, list):
                    Session().add_session(self.open_id, key, json.dumps(value), clear_cache=clear_cache)
                else:
                    Session().add_session(self.open_id, key, value, clear_cache=clear_cache)
        else:
            Session().add_session(self.open_id, self.command, ret, clear_cache=clear_cache)

    def exec(self):
        try:
            # 创建任务
            self._set_job()
            # 更新状态
            self._update_status(Status.running)
            # 启动任务
            ret = self._start_job()
            # 更新缓存
            if ret is not None and self.command.startswith("watch"):
                self.update_session(ret, clear_cache=False)
            elif ret is not None:
                self.update_session(ret)
            # 更新任务状态
            status = Status.completed if ret else Status.failure
            self._update_status(status)
            self.job.end_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            db.session.commit()
            return ret
        except Exception:
            print(traceback.format_exc())


def _async_raise(tid, exctype):
    """Raises an exception in the threads with id tid"""
    if not inspect.isclass(exctype):
        raise TypeError("Only types can be raised (not instances)")
    res = ctypes.pythonapi.PyThreadState_SetAsyncExc(ctypes.c_long(tid),
                                                     ctypes.py_object(exctype))
    if res == 0:
        raise ValueError("invalid thread id")
    elif res != 1:
        # "if it returns a number greater than one, you're in trouble,
        # and you should call it again with exc=NULL to revert the effect"
        ctypes.pythonapi.PyThreadState_SetAsyncExc(ctypes.c_long(tid), None)
        raise SystemError("PyThreadState_SetAsyncExc failed")


class BaseWorker(Thread):

    def __init__(self, work_type="worker", *args, **kwargs):
        super(BaseWorker, self).__init__(*args, **kwargs)
        self.id = str(uuid.uuid4())
        self.type = work_type
        self._stop_event = Event()

    def _get_my_tid(self):
        """determines this (self's) thread id

        CAREFUL: this function is executed in the context of the caller
        thread, to get the identity of the thread represented by this
        instance.
        """
        if not self.is_alive():
            raise threading.ThreadError("the thread is not active")

        # do we have it cached?
        if hasattr(self, "_thread_id"):
            return self._thread_id

        # no, look for it in the _active dict
        for tid, tobj in threading._active.items():
            if tobj is self:
                self._thread_id = tid
                return tid
        raise AssertionError("could not determine the thread's id")

    def stopped(self):
        return self._stop_event.is_set()

    def stop(self, exctype):
        app.logger.info("正在停止任务 %s" % self.id)
        self._stop_event.set()
        _async_raise(self._get_my_tid(), exctype)  # 抛出异常来停止任务
        app.logger.info("停止任务成功 %s" % self.id)


class WorkerThread(BaseWorker):

    def watch(self, key='job', timeout=1):
        """监听队列，阻塞式"""
        ret = redis_cli.blpop(key, timeout=timeout)
        if ret is None:
            raise redis.TimeoutError
        _, key = ret
        return key

    def run(self) -> None:
        worker_name = os.getenv("WORKER_NAME", "")
        queue = "job" if worker_name == "" else "job_%s" % worker_name
        while True:
            # 防止线程保存退出
            try:
                # 当程序即将退出时，状态将被置为done，则停止获取新任务
                if self.stopped():
                    break
                # 捕获超时事件
                try:
                    key = self.watch(queue)  # 阻塞式获取redis值
                except redis.TimeoutError:
                    continue
                act = Actuator(key)
                act.exec()
            except Exception as e:
                app.logger.error(e)


class CronJob(BaseWorker):

    def __init__(self, interval: int | float, context: str, *args, **kwargs):
        self.timer = threading.Timer(interval, self.run)
        self.context = context
        super(CronJob, self).__init__(work_type="worker_cron", *args, **kwargs)

    def start(self) -> None:
        self.timer.start()

    def run(self):
        app.logger.info("定时任务, key is %s" % self.id)
        try:
            self.running(self.context)
            act = Actuator(self.context.encode("utf-8"), is_cronjob=True)
            act.exec()
            self.done()
        except Exception as e:
            app.logger.error("定时任务执行失败: %s" % e)
        finally:
            context = Context(**json.loads(self.context))
            Schedule().register(context)
            app.logger.info("重新注册任务")

    def is_alive(self) -> bool:
        return self.timer.is_alive()

    def stop(self, exctype):
        app.logger.info("正在停止定时任务 %s" % self.id)
        self.timer.cancel()
        self._stop_event.set()
        app.logger.info("停止定时任务成功 %s" % self.id)


class Schedule:
    """调度器"""

    _instance = None
    _channel_sub = "__keyevent@0__:expired"
    _worker_list = []  # 工作线程状态列表
    _th_list: [WorkerThread] = []  # 工作线程列表
    _cron_th: [CronJob] = []  # 定时线程列表
    _status = "running"  # 处于何种状态将决定监听器是否继续监听新任务，当程序即将退出时，停止接收新任务

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(Schedule, cls).__new__(cls)
        return cls._instance

    def init_load_cronjob(self):
        # 加载预定定时任务
        app.logger.info("---------加载定时任务开始---------")
        for info in eval(os.getenv("CRONJOB")):
            command, open_id, chat_type, job_id = info
            context = Context(command=command, open_id=open_id, chat_type=chat_type, job_id=job_id)
            app.logger.info("正在加载定时任务 %s" % command)
            self.register(context)
        app.logger.info("-----------加载定时任务结束-----------")

    def _format_cron_time(self, command: str) -> int:
        cron_text = command.split("@")[-1].strip()
        target = 0
        tmp = None
        for i in re.findall(r"([0-9]+)([a-zA-Z]+)([0-9]+)?([a-zA-Z]+)?([0-9]+)?([a-zA-Z]+)?", cron_text)[0]:
            if i == '':
                continue
            try:
                tmp = int(i)
            except ValueError:
                if tmp is None:
                    raise CronJobSetError
                if i.lower() == 'd' or i.lower() == 'day':
                    target += 60 * 60 * 24 * tmp
                elif i.lower() == 'h' or i.lower() == 'hour':
                    target += 60 * 60 * tmp
                elif i.lower() == 'm' or i.lower() == 'minute':
                    target += 60 * tmp
                elif i.lower() == 's' or i.lower() == 'second':
                    target += tmp
                else:
                    raise CronJobSetError
        return target

    def _format_cron_date(self, command):
        cron_text = command.split("@")[-1].strip()
        try:
            return croniter(cron_text, datetime.now()).get_next(datetime)
        except CroniterBadCronError as e:
            app.logger.warn(e)
            return None
        except ValueError:
            return None
        except Exception as e:
            app.logger.warn(e)
            return None

    def _context_date(self, context: Context):
        command = context.command.split("|")[0].strip()
        if "@" in command:
            return self._format_cron_date(command)
        return None

    def _context_cron(self, context: Context):
        # 当存在@即为定时任务
        command = context.command.split("|")[0].strip()
        if "@" in command:
            try:
                return self._format_cron_time(command)
            except IndexError:
                return None
            except Exception as e:
                app.logger.warn(e)
                return None
        return None

    def _send_to_redis(self, key):
        redis_cli.rpush("job", key)

    def _add_timer(self, interval: int | float, context: str):
        cronjob = CronJob(interval, context)
        self._th_list.append(cronjob)
        self._cron_th.append(cronjob)
        cronjob.start()

    def register(self, context: Context):
        """
        注册任务
        :param context: context对象
        """
        # 定时任务格式化
        cron_time = self._context_cron(context)
        app.logger.info("cron_time: %s" % cron_time)
        # redis expire at格式化
        cron_date = self._context_date(context)
        app.logger.info("cron_date: %s" % cron_date)
        # 如果没有job_id则修改增加job_id
        job_id = str(uuid.uuid4()) if context.job_id is None else context.job_id
        context.add_item("job_id", job_id)
        key = json.dumps(context.to_dict())
        use_worker = UserSetup.get_user_worker(context.open_id)
        print(f"use_worker: {use_worker}")
        if use_worker:
            print("send_job_into_test_queue")
            UserSetup.send_job_into_test_queue(use_worker, key)
        elif cron_time is None and cron_date is None:
            self._send_to_redis(key)
        elif cron_time:
            self._add_timer(cron_time, key)
        elif cron_date:
            cron_time = datetime.timestamp(cron_date) - datetime.timestamp(datetime.now())
            self._add_timer(cron_time, key)

    def init_executor(self, count):
        """初始化执行器"""
        app.logger.info("------------启动线程-----------")
        for _ in range(count):
            th = WorkerThread()
            app.logger.info("正在启动线程 %s" % th.id)
            self._th_list.append(th)
            app.logger.info("成功启动线程 %s" % th.id)
            th.start()
        app.logger.info("------------启动线程结束-----------")

    def exit(self, *args):
        app.logger.info("------------正在关闭所有线程并清除状态-----------")
        # 判断当前工作线程存活状态，要求所有工作线程结束
        thread_done = True
        new_th_list = copy.copy(self._th_list)
        while True:
            tmp_th_list = copy.copy(new_th_list)
            for th in new_th_list:
                app.logger.info("------------正在检查线程状态: %s-----------" % th.id)
                print(th.stopped(), th.check_done())
                # 关闭不为stop并且没有处理任务的线程
                if not th.stopped() and th.check_done():
                    app.logger.info(f"线程 {th.id} 状态为 存活, 并且 没有在处理任务")
                    thread_done = False
                    app.logger.info("正在关闭线程： %s" % th.id)
                    th.stop(SystemClose)
                    print(th.check_done(), type(th))
                    app.logger.info("-------------尝试关闭线程： %s------------" % th.id)
                # 关闭不为stop且正在处理任务的线程
                elif not th.stopped() and not th.check_done():
                    # 暂时关闭任务 TODO: 缓存任务到redis保证不丢失任务
                    app.logger.info(f"线程 {th.id} 状态为 存活，并且 正在处理任务")
                    th.stop(SystemClose)
                    thread_done = False
                    app.logger.info("正在关闭线程： %s" % th.id)
                    app.logger.info("-------------尝试关闭线程： %s------------" % th.id)
                # 为stop的线程
                else:
                    tmp_th_list.remove(th)
                    app.logger.info("---------------线程状态为关闭: %s-------------" % th.id)
            new_th_list = tmp_th_list
            if thread_done:
                break
            thread_done = True
            time.sleep(1)
        app.logger.info("------------关闭所有线程并清除状态结束-----------")
        sys.exit(0)

    def dev_exit(self, *args):
        self.exit(*args)

    def prod_exit(self):
        self.exit()

    def init_exit_handler(self):
        if os.getenv("FLASK_ENV") == "development":
            signal.signal(signal.SIGTERM, self.exit)
            signal.signal(signal.SIGALRM, self.exit)
            signal.signal(signal.SIGQUIT, self.exit)
            signal.signal(signal.SIGHUP, self.exit)
            signal.signal(signal.SIGINT, self.exit)
        else:
            gevent.signal_handler(signal.SIGTERM, self.exit)
            gevent.signal_handler(signal.SIGALRM, self.exit)
            gevent.signal_handler(signal.SIGQUIT, self.exit)
            gevent.signal_handler(signal.SIGHUP, self.exit)
            gevent.signal_handler(signal.SIGINT, self.exit)

    @property
    def cron_th(self):
        return self._cron_th

    @classmethod
    def del_cron_th(cls, value):
        cls._cron_th.remove(value)
