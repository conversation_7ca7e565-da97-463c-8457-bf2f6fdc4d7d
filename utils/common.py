#!/usr/bin/env python3
import copy
import hashlib
import hmac
import json
import os
import random
import re
import time
from pathlib import Path
from queue import Queue
from collections import OrderedDict
from datetime import datetime
from functools import wraps

from sqlalchemy import desc

from utils import api
from utils.api import GithubApi
from utils import message_card as mc
from utils.init import session
from utils.shell import Shell
import cn2an
import datetime, pytz, requests, pymongo
from threading import Thread
from flask import request, jsonify
import jwt
from model.version import Version

from utils.api import KubernetesCommand

mongo_client = pymongo.MongoClient('mongodb://root:Gotapd8!@139.198.127.204:32550/qa?authSource=admin')

github_map_feishu = {
    "xbsura": ["gf9b5g97", "<PERSON>(肖贝贝)"],
    "11000100111010101100111": ["83f9g4b5", "<PERSON>(肖家海)"],
    "32073955": ["d5c7691b", "<PERSON>(尚俊彦)"],
    "cn-xufei": ["dgagf6dd", "<PERSON><PERSON><PERSON>, 许飞"],
    "dobybros": ["2439724e", "Aplomb, 陈卓"],
    "dreamcoin1998": ["4gbb7gd2", "Jerry Gao(高俊斌)"],
    "FannieGirl": ["3gcc4517", "Fannie, 郑芳"],
    "HarsenLin": ["abc2bfb7", "Harsen, 林浩生"],
    "jackin-code": ["6c1aba4a", "Jackin, 黄佳钦"],
    "jarad0628": ["4f8fb847", "Jarad Geng(耿杰)"],
    "jiuyetx": ["9be9ggff", "Jacques Liu(刘佳鑫)"],
    "matthewxuda": ["99eb81be", "David, 徐大伟"],
    "MiracleYoung": ["d339cfb7", "Arthur Yang, 杨庆麟"],
    "ningmeng777": ["5b4a5274", "Lemon Liu(刘龙飞)"],
    "openlg": ["9a4gbdg4", "Leon, 李汝飞"],
    "philbert1978": ["eg3de2e6", "Philbert Pang(庞博)"],
    "ply0011": ["g8f61ed5", "Sam Pan(潘浪宇)"],
    "SoloJu": ["g7adbg82", "Will Zhu(朱玮)"],
    "StormKennen": ["a242dfd3", "Kennen, 梁智开"],
    "Tapdata-Jackie": ["g8f7e6b6", "Jackie Li(李志超)"],
    "test-lily": ["4eda2fbg", "Lily Teng(腾莉莉)"],
    "tjworks": ["97135422", "TJ Tang(唐建法)"],
    "zed1201": ["6b454b53", "Zed, 舒富"],
    "zerohyuan": ["3df65fa8", "Harvey, 凌河源"]
}


def utc2local(t):
    if type(t) != datetime.datetime:
        return t
    from dateutil import tz
    from_zone = pytz.UTC
    to_zone = tz.gettz('Asia/Shanghai')
    t = t.replace(tzinfo=from_zone)
    return t.astimezone(to_zone).strftime("%Y-%m-%d %H:%M:%S")


class Obj(dict):
    def __init__(self, d):
        for a, b in d.items():
            if isinstance(b, (list, tuple)):
                setattr(self, a, [Obj(x) if isinstance(x, dict) else x for x in b])
            else:
                setattr(self, a, Obj(b) if isinstance(b, dict) else b)


def dict_2_obj(d: dict):
    return Obj(d)


def random_str(length=5):
    return "".join(random.sample("zyxwvutsrqponmlkjihgfedcba0123456789", length))


def compared_version(list1, list2):
    """
    传入不带英文的版本号,特殊情况："10.12.2.6.5">"10.12.2.6"
    :param list1: 版本号1
    :param list2: 版本号2
    :return: ver1< = >ver2返回-1/0/1
    """
    # 循环次数为短的列表的len
    for i in range(len(list1)) if len(list1) < len(list2) else range(len(list2)):
        if int(list1[i]) == int(list2[i]):
            pass
        elif int(list1[i]) < int(list2[i]):
            return -1
        else:
            return 1
    # 循环结束，哪个列表长哪个版本号高
    if len(list1) == len(list2):
        return 0
    elif len(list1) < len(list2):
        return -1
    else:
        return 1


def find_last_branch(branches, version_count=2):
    """
    找出指定version_count个版本号
    :param branches: 一个分支list
    :param version_count: 显示最新的前version_count个
    :return:
    """
    last_versions = [("0", "0", "0") for _ in range(version_count)]
    for branch in branches:
        # 正则匹配出版本号
        ret = re.findall(r"[0-9a-z]+-v([0-9]+).([0-9]+).([0-9]+)", branch)
        if len(ret) == 0:
            ret = re.findall(r"[0-9a-z]+-v([0-9]+).([0-9]+)", branch)
            if len(ret) == 0:
                continue
        version = ret[0]
        # 判断版本号，并将版本号大的插入到数组最前
        for index, last in enumerate(last_versions):
            if compared_version(version, last) == 1:
                last_versions.insert(index, version)
                last_versions = last_versions[:len(last_versions) - 1]
                break
            # 如果版本号一致则跳过
            if version == last:
                break
    ret_branch = []
    # 将版本号格式化
    for branch in branches:
        for last in last_versions:
            last_version = f"v{'.'.join(last)}"
            if last_version in branch:
                ret_branch.append(branch)

    return ret_branch


def make_text(items: list) -> str:
    """
    将items格式化为序号"tag：item\n"的形式
    :param items: 字符串列表
    :return:
    """
    if len(items) == 0:
        return "无"
    branches = {chr(97 + index): value for index, value in enumerate(items)}
    return '\n'.join([f"{key}: {value}" for key, value in branches.items()])


def is_contains_chinese(strs: str) -> bool:
    """
    是否含有中文字符
    :param strs: 字符串
    :return:
    """
    for _char in strs:
        if '\u4e00' <= _char <= '\u9fa5':
            return True
    return False


def get_dfs_images():
    owner = os.getenv("OWNER")
    ret = []
    image_list = api.GithubApi(owner, "tapdata-enterprise").image_tag()
    for image in image_list:
        for tag, version in image.items():
            if not tag.startswith("dfs"):
                continue
            count = 0
            for v in version:
                if count >= 2:
                    break
                count += 1
                ret.append(":".join([tag, v["tag"]]))
    return ret


def get_all_images():
    """
    获取所有的镜像tag列表
    :return:
    """
    owner = os.getenv("OWNER")
    ret = []
    image_list = api.GithubApi(owner, "tapdata-enterprise").image_tag()
    image_tags = []
    for image in image_list:
        for tag in image.keys():
            image_tags.append(tag)
    # 找到最新$tag_num个的符合要求的tag版本
    image_tag = find_last_branch(image_tags, version_count=100)
    for image in image_list:
        for tag, version in image.items():
            if tag not in image_tag:
                continue
            count = 0
            for v in version:
                if count >= 2:
                    break
                count += 1
                ret.append(":".join([tag, v["tag"]]))
    return ret


def get_all_env(namespace="dev"):
    """
    获取所有的环境列表
    :return:
    """
    envs, _, _ = KubernetesCommand(cloud_type="gcp").get_deployment_name(namespace)
    return envs.split("\n")[:-1]


def get_branch_threading(*repos, last_commit_day=30):
    """
    多线程获取仓库分支
    """

    def get_branch_inter(repository, q, last_commit_day):
        q.put({repository: api.GithubApi(owner, repository).branch(last_commit_day=last_commit_day)})

    q = Queue()
    threads = OrderedDict()
    results = OrderedDict()
    owner = os.getenv("OWNER")
    for repo in repos:
        th = Thread(target=get_branch_inter, args=(repo, q, last_commit_day))
        threads[repo] = th
        results[repo] = []
    for th in threads.values():
        th.start()
    for repo, th in threads.items():
        th.join()
    while not q.empty():
        results.update(q.get())
    if len(results.values()) == 1:
        return list(results.values())[0]
    return results.values()


def get_newest_branch(branch_type: str):
    """
    获取指定branch_type类型的最新分支
    :param branch_type: 分支类型，如develop，release，dfs
    :return: 最新的tapdata仓库分支，最新的tapdata-enterprise仓库分支, 最新的tapdata-enterprise-web分支, 最新的tapdata-cloud分支
    """
    # 获取全部分支
    opensource_branch, enterprise_branch, web_branch, cloud_branch = get_branch_threading(
        'tapdata', 'tapdata-enterprise', 'tapdata-enterprise-web', 'tapdata-cloud')
    branches = []
    # 格式化并执行shell命令获取最新分支
    for branch in [opensource_branch, enterprise_branch, web_branch, cloud_branch]:
        branch_text = "\n".join(branch)
        newest_branch, _, _ = Shell.execute(
            f'echo "{branch_text}" | grep "{branch_type}-v[0-9.]*$" | cut -d "/" -f 2 | sort -rV | head -n 1'
        )
        newest_branch = newest_branch.decode("utf8").replace("\n", "")
        branches.append(newest_branch)
    return branches


def get_connectors():
    """获取最新开发分支的所有connector"""
    newest_branch, _, _, _ = get_newest_branch("develop")
    return api.GithubApi(os.getenv("OWNER"), os.getenv("REPO")).get_connectors(
        "connectors",
        "tapdata",
        "tapdata-enterprise",
        except_path=['build', 'pom.xml', 'pom-ent.xml', 'config.xml'],
        ref=newest_branch.replace("\n", "")
    )


def get_saas_connectors():
    """获取saas数据源"""
    newest_branch, _, _, _ = get_newest_branch("develop")
    return api.GithubApi(os.getenv("OWNER"), os.getenv("REPO")).get_connectors(
        "connectors-javascript",
        "tapdata",
        except_path=['build', 'pom.xml', 'pom-ent.xml', 'config.xml', 'js-core'],
        ref=newest_branch.replace("\n", "")
    )


def get_yesterday_timestamp():
    # 今天日期
    today = datetime.date.today()
    # 昨天时间
    yesterday = today - datetime.timedelta(days=1)
    star = int(time.mktime(time.strptime(str(yesterday), '%Y-%m-%d')))
    star_time = star * 1000
    return star_time


def format_text(text_content):
    return {
        "text": text_content
    }


def parse_branch(cache_list: list, cache_count: int):
    branch_list = cache_list[:cache_count]
    tag_list = cache_list[cache_count:]
    resp = []
    for i, bl in enumerate(branch_list):
        if bl is None or tag_list[i] is None:
            return None
        resp.append(json.loads(bl).get(tag_list[i]))
    return resp


def make_cache(items: list):
    """
    构建缓存数据结构
    :param items:
    :return:
    """
    return {str(index + 1): value for index, value in enumerate(items)}


def field_list_message_card(header: str, color="green", is_short=True, **kwargs):
    message_card = mc.MessageCard(mc.Header(header, color=color), mc.Config())
    fields = mc.Fields()
    if len(kwargs) == 0:
        return message_card

    for k, v in kwargs.items():
        if v is None:
            continue
        field_text = f"**{k}**\n{v}"
        fields.add_child(mc.Text(field_text, mc.TextType.lark_md), is_short=is_short)
    div = mc.Div(fields=fields)
    message_card.add_element(div)
    return message_card


def make_fields_div(is_short=True, **kwargs) -> mc.Div:
    fields = mc.Fields()
    for k, v in kwargs.items():
        field_text = f"**{k}**\n{v}"
        fields.add_child(mc.Text(field_text, mc.TextType.lark_md), is_short=is_short)
    return mc.Div(fields=fields)


def format_time(t: str) -> str:
    if t is None:
        return str(None)
    time_utc = datetime.datetime.fromisoformat(t.replace('Z', ''))
    time_shanghai = time_utc + datetime.timedelta(hours=8)
    return time_shanghai.strftime("%Y-%m-%d %H:%M:%S")


def get_runner_info(runner_info: dict) -> dict:
    """
    获取Github runner信息
    :param runner_info: Runner ID
    :return: 运行状态/结果，运行时间，Job状态，运行到Job的哪一步
    """
    ret = {}
    jobs = runner_info["jobs"]
    for job in jobs:
        name = job["name"]
        job_info = {}
        # Job状态
        job_info["status"] = job["status"]
        # 运行结果
        job_info["conclusion"] = job["conclusion"]
        # 开始时间
        job_info["start_time"] = format_time(job["started_at"])
        # 结束时间
        job_info["completed_at"] = format_time(job["completed_at"])
        if job["status"] == "in_progress":
            # 运行到哪里
            current_step = "未知"
            for step in job["steps"]:
                if step["status"] == "in_progress":
                    current_step = step["name"]
                    break
            job_info["current_step"] = current_step
        ret[name] = copy.deepcopy(job_info)
    return ret


def upload_file_use_scp(dir_path, host, target_path):
    if not os.path.isdir(dir_path):
        raise Exception("上传制品时，必须指定目录")
    path = Path(os.path.dirname(os.path.realpath(__file__)))
    pem_file = f"{path}/../conf/public.pem"
    command = f'scp -i "{pem_file}" -r "{dir_path}" root@{host}:{target_path}'
    ret_code = Shell.run(command)
    return ret_code


def compile_match_version(s: str, reg: str) -> bool:
    """
    字符串是否符合某种正则规则
    :param s: 目标字符串
    :param reg: 正则表达式
    :return:
    """
    r = re.match(reg, s)
    if r is None:
        return False
    matched_str = r.group()
    return len(matched_str) == len(s)


def find_element_index_in_list(s, array: list) -> int:
    """
    返回s在array中的index
    :param s: 数组元素
    :param array: 数组
    :return:
    """
    for i, a in enumerate(array):
        if a == s:
            return i
    return -1


def verify_webhook_token(request, secret):
    sha_name, github_signature = request.headers.get("X-Hub-Signature").split('=')
    if sha_name != 'sha1':
        return False
    local_signature = hmac.new(secret.encode('utf-8'), msg=request.data, digestmod=hashlib.sha1)
    return hmac.compare_digest(local_signature.hexdigest(), github_signature)


def get_time_range(s):
    s = s.replace(" ", "")
    s = cn2an.transform(s)
    interval_days = {
        "天": 1,
        "周": 7,
        "星期": 7,
        "月": 30,
        "年": 365,
    }
    # 默认选最近一个月的时间
    now = datetime.datetime.now()
    r_default = [datetime.datetime(now.year, now.month, 1).timestamp(), time.time()]

    # 判断当前周期的时间
    def get_this_interval_range(this_interval):
        if this_interval == "天":
            return [(now - datetime.timedelta(hours=now.hour, minutes=now.minute, seconds=now.second)).timestamp(),
                    time.time()]
        if this_interval in ["周", "星期"]:
            return [now.timestamp() - now.weekday() * 86400, time.time()]
        if this_interval == "月":
            return [now.timestamp() - now.day * 86400, time.time()]
        if this_interval == "年":
            return [now.timestamp() - int(now.strftime("%j")) * 86400, time.time()]

    for interval, days in interval_days.items():
        if len(re.findall("^([这,本,今])(个?)({})([的,紧急,高级,工单])".format(interval), s)) > 0:
            return get_this_interval_range(interval)

    # 判断上个周期的时间
    for interval, days in interval_days.items():
        if len(re.findall("^([上,去])(个?)({})([的,紧急,高级,工单])".format(interval), s)) > 0:
            r = get_this_interval_range(interval)
            r[0] = r[0] - 86400 * days
            r[1] = r[0] + 86400 * days
            return r

    # 判断过去几个周期的时间, 这个不用太准确了, 直接减就行, 多加一个周期
    for interval, days in interval_days.items():
        x = re.findall("[最近,过去,这](\d*)(个)?({})([的,紧急,高级,工单])".format(interval), s)
        if len(x) > 0:
            try:
                n = float(x[0][0])
            except Exception as e:
                # 比如问最近几周这种情况, 按 3 来
                n = 3
            r = [time.time() - 86400 * days * n, time.time()]
            return r

    # 判断范围周期的时间, 只支持到月, 其他的太麻烦了, 意义也不大
    def get_month_range(ss):
        x = re.findall("([今,去,前,\d]*年)?(\d*)([月,月份])", ss)
        year = datetime.datetime.now().year
        month = datetime.datetime.now().month
        if len(x) > 0:
            if x[0][0] == "":
                year = now.year
            else:
                if x[0][0][0] == "去":
                    year = now.year - 1
                if x[0][0][0] == "前":
                    year = now.year - 2
                try:
                    year = float(x[0][0].rstrip("年"))
                except Exception as e:
                    pass
            if year < 100:
                year = int(now.year / 100) * 100 + year

            try:
                month = int(x[0][1])
            except Exception as e:
                month = 1
            r_start = datetime.datetime(year, month, 1).timestamp()
            return [r_start, r_start + 86400 * 30]
        return None

    if "到" not in s and "至" not in s:
        r = get_month_range(s)
        if r:
            return r
        return r_default

    if "到" in s:
        sx = s.split("到")
    if "至" in s:
        sx = s.split("至")
    r0 = get_month_range(sx[0])
    r1 = get_month_range(sx[1])
    return [r0[0], r1[1]]


def time_readable(seconds):
    seconds = int(seconds)
    if seconds < 60:
        return "{} seconds".format(seconds)
    minutes = int(seconds / 60)
    if minutes < 60:
        return "{} minutes".format(minutes)
    hours = int(minutes / 60)
    if hours <= 48:
        return "{} hours".format(hours)
    days = int(hours / 24)
    return "{} days".format(days)


def convert_utctime_to_local(utc: str, UTC_FORMAT: str) -> str:
    """
    将utc时间转换为北京时间
    :param UTC_FORMAT: 格式化字符串
    :param utc: 要转换的字符串
    :return:
    """
    utc = utc.split(".")[0]
    utcTime = datetime.datetime.strptime(utc, UTC_FORMAT)
    localtime = utcTime + datetime.timedelta(hours=8)
    return localtime.strftime("%Y-%m-%d %H:%M:%S")


def get_err_task(user, phone, task_id, task_name, last_seconds):
    condition = {}
    if user is not None:
        condition["username"] = user
    if task_id is not None:
        condition["taskId"] = task_id
    if task_name is not None:
        condition["taskName"] = task_name
    if phone is not None:
        condition["phone"] = phone

    if len(condition) == 0:
        return []

    condition["level"] = "ERROR"

    if last_seconds is not None:
        condition['date'] = {'$gt': datetime.datetime.now() - datetime.timedelta(seconds=last_seconds)}

    tasks = list(mongo_client["qa"]["OnlineTaskErrorLogAI"].find(condition).sort("date", pymongo.DESCENDING))
    distinct_tasks = []
    cache_map = {}
    for task in tasks:
        if task["taskId"] not in cache_map:
            cache_map[task["taskId"]] = True
            distinct_tasks.append(task)
        if len(distinct_tasks) >= 15:
            break
    return distinct_tasks


def get_coding_help(taskId):
    headers = {
        "Authorization": "token 9bb7d35bf5fd952fac672e2dc9897392a747541b",
        "Content-Type": "application/json",
    }
    title = "#0000: Cloud Auto Issue: " + taskId
    url = "https://tapdata.coding.net/open-api?Action=DescribeIssueListWithPage"
    payload = {
        "Action": "DescribeIssueListWithPage",
        "ProjectName": "tapdata",
        "IssueType": "DEFECT",
        "PageNumber": 1,
        "PageSize": 5,
        "Conditions": [{"Key": "KEYWORD", "Value": title}]
    }
    resp = requests.post(url=url, json=payload, headers=headers).json()
    if resp["Response"]["Data"]["TotalCount"] == 0:
        return None
    for issue in resp["Response"]["Data"]["List"]:
        issue_code = issue["Code"]
        url = "https://tapdata.coding.net/open-api?Action=DescribeIssueCommentList"
        payload = {
            "Action": "DescribeIssueCommentList",
            "ProjectName": "tapdata",
            "IssueCode": issue_code
        }
        resp = requests.post(url=url, json=payload, headers=headers).json()
        comment_list = resp["Response"]["CommentList"]
        for comment in comment_list:
            content = comment["RawContent"].strip()
            if content.startswith("客户"):
                if "客户:" in content:
                    return content.split("客户:")[1]
                return content.split("客户")[1]
        if issue["IssueStatusName"] not in ["待处理", "挂起", "已拒绝"] and issue["AssigneeId"] != 8390151:
            payload = {
                "Action": "DescribeTeamMembers",
                "PageNumber": 1,
                "PageSize": 100,
            }
            url = "https://tapdata.coding.net/open-api?Action=DescribeTeamMembers"
            resp = requests.post(url=url, json=payload, headers=headers).json()
            users = resp["Response"]["Data"]["TeamMembers"]
            assign = "研发"
            for user in users:
                if user["Id"] == issue["AssigneeId"]:
                    assign = user["Name"]
            return "这个问题被确认为产品 BUG, 目前已经交由 {} 处理, 当前状态为: {}".format(assign,
                                                                                           issue["IssueStatusName"])
        return None


def get_wiki_help(cause):
    qa_wikis = mongo_client["qa"]["QA_WIKI"].find()
    for wiki in qa_wikis:
        keys = wiki.get("key", None)
        if keys is None:
            continue
        for key in keys.split(","):
            if key.strip() in cause:
                text = "这个错误发生原因为: {}, 解决方法为: {}".format(wiki.get("reason"), wiki.get("action"))
                return text
    return None


def get_task_help(task, ri, result):
    def try_get_coding_help(task_id):
        try:
            n = time.time()
            coding_help = get_coding_help(task_id)
            print("{} get coding help cost: {} s".format(task_id, round(time.time() - n, 2)))
            if coding_help is not None:
                result[ri][0] = coding_help
        except Exception:
            pass

    def try_get_wiki_help(cause):
        try:
            n = time.time()
            wiki_help = get_wiki_help(cause)
            print("get wiki help cost: {} s".format(round(time.time() - n, 2)))
            if wiki_help is not None:
                result[ri][1] = wiki_help
        except Exception as e:
            pass

    t1 = Thread(target=try_get_coding_help, args=(task["taskId"],))
    t2 = Thread(target=try_get_wiki_help, args=(task["cause"],))
    t1.start()
    t2.start()
    t1.join()
    t2.join()


def get_help(user, phone=None, task_id=None, task_name=None, last_seconds=86400 * 7):
    tasks = get_err_task(user, phone, task_id, task_name, last_seconds)
    if len(tasks) == 0:
        text = "没有发现您最近有任务报错, 如果有其他问题, 欢迎咨询我们的人工客服"
        return text

    text = ""
    threads = []
    help_result = []
    for i in range(len(tasks)):
        # coding 工单结果; wiki 搜索结果
        help_result.append([None, None])

    for i in range(len(tasks)):
        task = tasks[i]
        thread = Thread(target=get_task_help, args=(task, i, help_result,))
        threads.append(thread)
        thread.start()

    for thread in threads:
        thread.join()

    x = 0
    for i in range(len(tasks)):
        task = tasks[i]
        error = ";".join(task.get("cause", "").split(";")[0:2])
        ai_help = task.get("reason")
        ai_prefix = None
        if ai_help is not None:
            if ":" in ai_help[0:20]:
                ai_prefix = ":"
            if "：" in ai_help[0:20]:
                ai_prefix = "："
            if ai_prefix is not None:
                ai_help = ai_help.split(ai_prefix, 2)[1]

        coding_help, wiki_help = help_result[i]
        h = coding_help
        if h is None:
            h = wiki_help
        if h is None:
            h = ai_help
        if h is None:
            continue
        if len(text) > 2000:
            break
        text += "{}. 名字为 {} 的任务, 在 {} 发生报错, 经分析, 提供给您的信息为: {}\n\n".format(
            i + 1, task["taskName"], utc2local(task["date"]), h)
        x += 1
        continue
    if x == 0:
        text = "系统分析没有成功, 请联系我们的人工客服解决问题\n".format(len(tasks))
        return text
    text = "可能原因分别如下:\n\n".format(len(tasks), x) + text
    return text


class RunnerConclusion:
    success = "success"
    failure = "failure"
    cancelled = "cancelled"
    start_failed = "start_failed"


def get_runner_id(job_id, repo=os.getenv("TRIGGER_REPO")):
    owner = os.getenv("OWNER")
    github_obj = GithubApi(owner, repo)
    runner_id, detail_url = github_obj.find_runner_id(job_id, event="")
    return runner_id, detail_url


def compare_version(version1, version2):
    """返回 version1 > version2"""
    print("version1: {}, version2: {}".format(version1, version2))
    version1 = re.findall(r"-v([0-9.]+)", version1)
    version2 = re.findall(r"-v([0-9.]+)", version2)
    if version1 is None or len(version1) == 0 or version2 is None or len(version2) == 0:
        return False
    version1 = version1[0]
    version2 = version2[0]
    # version1 和 version2 如 1.0.0
    # 判断 version1 是否大于 version2
    print("version1: {}, version2: {}".format(version1, version2))
    return version1 >= version2


def time_convert(t: str):
    utc_time = t.split('.')[0] + 'Z' if '.' in t else t
    try:
        dt = datetime.datetime.strptime(utc_time, "%Y-%m-%dT%H:%M:%SZ")
    except ValueError:
        print(utc_time, t)
        raise "sss"
    dt = dt.replace(tzinfo=datetime.timezone.utc)
    utc_plus_8 = datetime.timezone(datetime.timedelta(hours=8))
    dt = dt.astimezone(utc_plus_8)
    formatted_time = dt.strftime("%Y-%m-%d %H:%M:%S")
    return formatted_time


class Jwt:

    def __init__(self):
        self.secret_key = os.getenv("ENCRYPT_KEY")

    def encode(self, username, **kwargs):
        claims = {
            "username": username,
        }
        claims.update(kwargs)
        encoded_jwt = jwt.encode(claims, self.secret_key, algorithm='HS256')
        return encoded_jwt

    def decode(self, encoded_jwt):
        try:
            decoded_jwt = jwt.decode(encoded_jwt, self.secret_key, algorithms=['HS256'])
            return decoded_jwt
        except jwt.InvalidTokenError:
            return False


# 登陆验证装饰器
def login_verify(fn):
    @wraps(fn)
    def _wrap(*args, **kwargs):
        token = request.headers.get("Authorization")
        user_info = Jwt().decode(token)
        if not user_info or user_info.get("username") is None or user_info.get("username") != "admin":
            return jsonify({
                "msg": "权限不足",
                "code": 2,
            })
        return fn(*args, **kwargs)

    return _wrap


def all_release_branch():
    return {
        item.version: {
            "tapdata": item.tapdata,
            "tapdata_enterprise": item.tapdata_enterprise,
            "tapdata_enterprise_web": item.tapdata_enterprise_web,
            "tapdata_connectors": item.tapdata_connectors,
            "tapdata_connectors_enterprise": item.tapdata_connectors_enterprise,
            "tapdata_license": item.tapdata_license,
        }
        for item in Version.query.all()}


def get_release_version(version: str) -> Version:
    return Version.query.filter_by(version=version).first()


def remove_release_version(version: str) -> int:
    return Version.query.filter_by(version=version).delete()


def rename_release_version(version: str, new_version: str) -> int:
    return Version.query.filter_by(version=version).update({"version": new_version})


def get_tag_by_workflow_id(workflow_id):
    run_info = api.GithubApi('tapdata', 'tapdata-application').get_runner_job_info(workflow_id)
    for job in run_info["jobs"]:
        for step in job["steps"]:
            if isinstance(step["name"], str) and step["name"].lower().startswith("tag="):
                return step["name"].lower().replace("tag=", "").replace(";", "")
    return ""


TEXT_CONVERT = {
    "source_db_type": "源节点",
    "target": "目标节点",
    "processing": "处理节点",
    "table_fields": "字段数",
    "sync_type": "同步类型",
    "row_num": "行数",
    "metrics": "指标",
    "initial_sync_qps_compare": "全量",
    "cdc_qps_compare": "增量"
}

sam = "ou_f29f30714e57211bcc230eba39c407b1"
leon = "ou_35609f620a43ddecc208a4d7f85d1fd1"
jarad = "ou_562c37b89f53e858eb5f5fbcad1f97c5"
jerry = "ou_677b941f814ae3c4235d046a1f8f5478"
berry = "ou_b05a61325c928af129539a4474c881b2"
peng = "ou_b998848146bfcda565cdcd8b8f92c439"
xufei = "ou_789b17fedcd7bb6184ddf9c68b321203"
martin = "ou_c7a662fadc8e06198a39ac7d329b8109"
jackin = "ou_9402d9587684b4d7c5cfc1085e2617e6"


# sam = leon = jarad = jerry = peng = xufei = martin = jackin = berry


def get_lark_header():
    base_url = "https://open.feishu.cn/open-apis"
    headers = {"content-type": "application/json; charset=utf-8"}
    app_id = "cli_a26d15bc2bf99013"
    app_secret = "6JYz2K0sCV0rMmI6OkMkyhSgnaBZY7PA"
    url = base_url + "/auth/v3/tenant_access_token/internal"
    data = {
        "app_secret": app_secret,
        "app_id": app_id
    }
    for i in range(3):
        response = requests.post(url, headers=headers, json=data, timeout=10)
        if response.json()['code'] == 0:
            token = response.json()['tenant_access_token']
            headers.update({
                "Authorization": f"Bearer {token}"
            })
        else:
            continue
    return headers


def get_current_timestamp():
    timestamp_seconds = time.time()

    # 转换为毫秒级的时间戳
    timestamp_milliseconds = int(timestamp_seconds * 1000)
    return timestamp_milliseconds


def get_active_sprint():
    token = "ATATT3xFfGF01Xlg_FWptPLRRUwZ0szNctX257VXj7dTU-SwLLcp4FZquz_16mrJ9TXnkbI2dfyzPhtpH8icSQmKmH5IAXzSLhM4leHJ3FUevuzcqcAmT9Gpm5VRAcnBQDuldD82fosVmaW0Q2qgmCiY9gDIbhSKX6zFEgYEMpaeZlJdGihb-ns=847D503E"
    url1 = "https://tapdata.atlassian.net/rest/agile/1.0/"
    from requests.auth import HTTPBasicAuth
    auth3 = HTTPBasicAuth("<EMAIL>", token)
    jira_retry = 3
    jira_timeout = 10
    url = "{}/board/4/sprint?state=active".format(url1)
    for i in range(jira_retry):
        try:
            response = requests.get(
                url,
                auth=auth3,
                timeout=jira_timeout
            )
            return response.json().get("values")[0]["name"].split()[-1]
        except Exception as e:
            continue
    return 0


def insert_release_info(db, app, release_version):
    """
    插入当前迭代分支关系数据

    :param db               数据库实例
    :param app              当前application实例
    :param release_version  当前版本，如3.6.0
    """
    version = Version(
        version=f"release-v{release_version}",
        tapdata=f"release-v{release_version}",
        tapdata_enterprise=f"release-v{release_version}",
        tapdata_enterprise_web=f"release-v{release_version}",
        tapdata_connectors=f"main",
        tapdata_connectors_enterprise=f"main",
        tapdata_license=f"main"
    )
    db.session.add(version)
    db.session.commit()
    app.logger.info(f"已插入当前迭代分支关系数据，当前版本: {version.version}")
    app.logger.info(f"当前迭代数据: version: {version.version}, tapdata: {version.tapdata}, "
                    f"tapdata_enterprise: {version.tapdata_enterprise}, "
                    f"tapdata_enterprise_web: {version.tapdata_enterprise_web}, "
                    f"tapdata_connectors: {version.tapdata_connectors}, "
                    f"tapdata_connectors_enterprise: {version.tapdata_connectors_enterprise}, "
                    f"tapdata_license: {version.tapdata_license}")


def insert_build_template(db, app, name: str, tapdata: str, tapdata_enterprise: str,
                          tapdata_enterprise_web: str, tapdata_connectors: str = "main",
                          tapdata_connectors_enterprise: str = "main", tapdata_license: str = "main"):
    version = Version(
        version=name,
        tapdata=tapdata,
        tapdata_enterprise=tapdata_enterprise,
        tapdata_enterprise_web=tapdata_enterprise_web,
        tapdata_connectors=tapdata_connectors,
        tapdata_connectors_enterprise=tapdata_connectors_enterprise,
        tapdata_license=tapdata_license
    )

    db.session.add(version)
    db.session.commit()
    app.logger.info(f"保存构建模版，当前版本: {version}")


def get_current_release():
    """
    获取当前迭代使用的分支版本号
    """
    return session.query(Version).order_by(desc(Version.version)).first().version


def serialize_model(model_obj, **kwargs):
    """
    将模型对象序列化为 JSON 字符串

    :param model_obj: SQLAlchemy 模型对象
    :return: JSON 字符串
    """
    if model_obj is None:
        return json.dumps(None)

    # 如果是列表，递归处理每个元素
    if isinstance(model_obj, list):
        return json.dumps([_model_to_dict(item) for item in model_obj], **kwargs)

    # 单个对象
    return json.dumps(_model_to_dict(model_obj), **kwargs)


def _model_to_dict(model_obj):
    """将模型对象转换为字典"""
    if model_obj is None:
        return None

    # 检查是否为 SQLAlchemy 模型
    if hasattr(model_obj, '__table__'):
        return {c.name: getattr(model_obj, c.name) for c in model_obj.__table__.columns}

    # 其他类型，尝试转换为字典
    if hasattr(model_obj, '__dict__'):
        return {k: v for k, v in model_obj.__dict__.items() if not k.startswith('_')}

    # 无法处理的类型
    return str(model_obj)


def clean_kwargs(**kwargs):
    """移除所有以下划线开头的参数，返回新字典"""
    return {k: v for k, v in kwargs.items() if not k.startswith('_')}
