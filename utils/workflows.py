import json
import traceback

from celery import group, chain, signature
from celery.exceptions import Chord<PERSON>rror

from model.task import Task
from model.version import Version
from utils.tasks import send_text_to_feishu, trigger_workflow_and_wait, artifact_build_result_send, update_op_env, \
    env_update_result_send, finish_feishu_task, scale_deployment, check_passed_for_cloud_env, open_cloud_gray_env, \
    sync_agent_version, release_agent_version, handle_opensource_build_error, handle_cloud_update_error
from utils import common
from lib.api import send_text


def merge_to_develop(sprint: str, task: Task, task_id: str):
    """
    合并到develop分支，触发OP测试环境的更新

    :param sprint: 当前迭代号
    :param task: 任务
    :param task_id: 任务ID
    """
    if not sprint:
        return "当前没有活跃的迭代", False

    # 构建OP版本的配置
    build_config = {
        "FRONTEND_BRANCH": "develop",
        "OPENSOURCE_BRANCH": "develop",
        "ENTERPRISE_BRANCH": "develop",
        "CONNECTORS_BRANCH": "develop#develop",
        "LISENSE_BRANCH": "main",
        "TAG_NAME": ""
    }

    params = {
        "BUILD_CONFIG": json.dumps(build_config),
        "IS_TAR": False,
        "FRONTEND_MODE": "production",
        "TAPDATA_PLATFORM_VERSION": "Linux",
        "JAVA_VERSION": "java17",
        "RUN_TEST": False,
        "CONNECTORS-OPTIONS": "all"
    }

    workflow = group(
        send_text_to_feishu.s(
            text=f"检测到任务：sprint #{sprint}, release-v{task.release_version} Release Task 中,依赖事项 {task.summary} 已经完成,现在开始使用 develop 分支更新OP测试环境,在更新完成后, 会发送结果与对应的访问地址到群内。"),
        chain(
            trigger_workflow_and_wait.s("tapdata", "tapdata-application", "build-tapdata-op.yaml",
                                        "main", **params),
            artifact_build_result_send.s(),
            update_op_env.s(),
            env_update_result_send.s(),
            finish_feishu_task.s(task_id),
            send_text_to_feishu.s(text=f"""任务：sprint #{sprint}, release-v{task.release_version} Release Task 使用develop分支更新OP测试环境已完成
OP测试环境：http://192.168.1.184:3030""")
        ),
    )
    workflow.delay()



def merge_to_release(sprint: str, task: Task, task_id: str, version: Version):
    """
    合并到release分支，触发OP测试环境、云版灰度环境和开源的打包和更新

    :param sprint: 当前迭代号
    :param task: 任务
    :param task_id: 任务ID
    :param version: 版本映射
    """
    try:
        # 构建OP版本的配置
        op_build_config = {
            "FRONTEND_BRANCH": version.tapdata_enterprise_web,
            "OPENSOURCE_BRANCH": version.tapdata,
            "ENTERPRISE_BRANCH": version.tapdata_enterprise,
            "CONNECTORS_BRANCH": f"{version.tapdata_connectors}#{version.tapdata_connectors_enterprise}",
            "LISENSE_BRANCH": version.tapdata_license,
            "TAG_NAME": ""
        }
        op_params = {
            "BUILD_CONFIG": json.dumps(op_build_config),
            "IS_TAR": False,
            "FRONTEND_MODE": "production",
            "TAPDATA_PLATFORM_VERSION": "Linux",
            "JAVA_VERSION": "java17",
            "RUN_TEST": False,
            "CONNECTORS-OPTIONS": "all"
        }

        cloud_branch = {
            "FRONTEND_BRANCH": version.tapdata_enterprise_web,
            "OPENSOURCE_BRANCH": version.tapdata,
            "ENTERPRISE_BRANCH": version.tapdata_enterprise,
            "DFS_BRANCH": version.version,
            "BUILD-FRONTEND": True,
            "BUILD-TCM": False,
            "BUILD-TM-JAVA": True,
            "BUILD-AGENT": True,
            "BUILD-TICKET": False,
            "ENV": "gcp-gray",
        }

        # 创建各个任务链，但不立即执行
        # 1. 更新OP测试环境
        op_chain = chain(
            trigger_workflow_and_wait.s("tapdata", "tapdata-application", "build-tapdata-op.yaml",
                                        "main", **op_params),  # 更新OP测试环境
            artifact_build_result_send.s(),
            update_op_env.s(),
            env_update_result_send.s(),
        )

        # 2. 构建开源版
        opensource_chain = chain(
            trigger_workflow_and_wait.s("tapdata", "tapdata", "build.yml", version.tapdata,
                                        frontend_branch=version.tapdata_enterprise_web,
                                        connectors_branch=version.tapdata_connectors
                                        ),  # 构建开源版
            artifact_build_result_send.s(next_step_text="无", repo="tapdata"),
        )
        # 添加错误处理
        opensource_chain.link_error(handle_opensource_build_error.s())

        # 3. 更新云版灰度环境
        # 首先缩容
        scale_group_task = group(
            scale_deployment.s("console-prod3", "cloud-gray"),
            scale_deployment.s("tm-prod3", "cloud-gray"),
        )

        # 然后更新
        cloud_update_chain = chain(
            trigger_workflow_and_wait.s("tapdata", "tapdata-application", "cloud-upgrade.yaml",
                                        "main", **{"PARAMS_AS_JSON": json.dumps(cloud_branch)}),
            # 更新云版灰度环境
            artifact_build_result_send.s(next_step_text="更新云版灰度环境"),
            group(
                check_passed_for_cloud_env.s("console-prod3", "cloud-gray"),
                check_passed_for_cloud_env.s("tm-prod3", "cloud-gray"),
            ),
            open_cloud_gray_env.s(env_name="cloud-net"),
            release_agent_version.s(),
        )
        # 添加错误处理
        cloud_update_chain.link_error(handle_cloud_update_error.s())

        # 组合所有任务
        # 使用group将多个任务并行执行
        parallel_tasks = group(
            op_chain,
            send_text_to_feishu.s(
                text=f"检测到任务：sprint #{sprint}, release-v{task.release_version} Release Task 中,依赖事项 {task.summary} 已经完成,现在开始使用 release-v{task.release_version} 分支更新OP测试环境并打包开源版,在更新完成后,会发送结果与对应的访问地址到群内。"),
            opensource_chain,
            # group(  # 暂时禁用云版灰度环境更新
            #     scale_group_task,
            #     cloud_update_chain,
            # ),
        )

        # 最后的任务链
        workflow = chain(
            parallel_tasks,
            finish_feishu_task.s(task_id),
            send_text_to_feishu.s(
                text=f"""任务：sprint #{sprint}, release-v{task.release_version} Release Task 使用 release-v{task.release_version} 分支更新OP测试环境并打包开源版已完成
OP测试环境：http://192.168.1.184:3030""")
        )

        # 执行任务链
        workflow.delay()

    except Exception as e:
        # 处理整体异常
        print(f"Error in merge_to_release: {e}")
        print(traceback.format_exc())
        message = common.field_list_message_card(
            header="❗️任务执行异常",
            失败详情=f"merge_to_release 执行异常: {str(e)}",
            处理方式="请手动检查任务状态",
            color="red",
        )
        # 从utils.tasks中导入group_id变量
        from utils.tasks import group_id
        send_text("message_card", group_id, message.to_dict(), "group")
