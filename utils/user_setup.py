"""
USER 状态设置
"""

from utils.connector import redis_cli


class UserSetup:

    @classmethod
    def set_user_worker(cls, open_id, worker_name):
        if worker_name == "default":
            worker_name = ""
        redis_cli.hset("use_worker", open_id, worker_name)

    @classmethod
    def get_user_worker(cls, open_id):
        user_worker = redis_cli.hget("use_worker", open_id)
        return "" if user_worker is None else user_worker.decode("utf-8")

    @classmethod
    def send_job_into_test_queue(cls, worker_name, key):
        redis_cli.rpush(f"job_{worker_name}", key)
