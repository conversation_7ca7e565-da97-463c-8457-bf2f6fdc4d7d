"""
初始化服务

@author: <PERSON>
@data: 2022.08.18
"""
import os
from importlib import import_module

from celery.schedules import crontab
from flask import Flask
from dotenv import load_dotenv, find_dotenv
from flask_sqlalchemy import SQLAlchemy
from celery import Celery
from sqlalchemy.orm import scoped_session, sessionmaker

load_dotenv(find_dotenv())

from handler import init_handle


def init_db():
    model_path = os.path.sep.join([os.path.dirname(__file__), "..", "model"])
    files = os.listdir(model_path)
    if os.path.exists(os.path.sep.join([os.path.dirname(__file__), "..", "model", "__init__.py"])):
        files.remove("__init__.py")
    if os.path.exists(os.path.sep.join([os.path.dirname(__file__), "..", "model", "__pycache__"])):
        files.remove("__pycache__")
    for file in files:
        module = file.replace(".py", "")
        try:
            pkg = import_module(f"model.{module}")
        except ModuleNotFoundError:
            continue
        getattr(pkg, f"{module.capitalize()}")()


app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = os.getenv("SQLALCHEMY_DATABASE_URI")
app.config['SQLALCHEMY_POOL_RECYCLE'] = 3600  # 多少秒后自动回收连接, 1小时
app.config['SQLALCHEMY_CELERY_TIMEZONE'] = "Asia/Shanghai"  # 设置时区
app.config['SQLALCHEMY_CELERY_ENABLE_UTC'] = False  # 设置时区
app.config['SQLALCHEMY_POOL_SIZE'] = 10  # 连接池大小
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

db = SQLAlchemy(app)
init_db()
db.create_all()

celery = Celery(app.name)
celery.config_from_object('utils.celery_config')


# 使用线程本地session
session = scoped_session(sessionmaker(bind=db.engine))

# init handler
init_handle()
