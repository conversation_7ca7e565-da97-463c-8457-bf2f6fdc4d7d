"""
缓存控制

@author: <PERSON>
@date: 2022.09.02
"""
from __future__ import annotations

import os
from datetime import datetime, timedelta

from utils.connector import redis_cli
from utils.init import app


class Session:

    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(Session, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        self.redis_cli = redis_cli
        self.expire_duration_time = int(os.getenv("EXPIRE_TIME"))

    def _expire_handler(self, open_id: str, expire_time: float):
        """
        1、检测当前name下的key是否过期
        2、过期 - 删除name下所有key 不过期 - 继续增加key
        :param expire_time: expire time, type is timestamp
        :param open_id: feishu user openid
        :return:
        """
        now = datetime.timestamp(datetime.now())
        print(now, expire_time)
        if now > expire_time:
            app.logger.info(f"当前key过期，正在删除key, name: {open_id}")
            keys = self.redis_cli.hkeys(open_id)
            for key in keys:
                self.redis_cli.hdel(open_id, key)
            app.logger.info(f"删除key完成，name: {open_id}")

    def add_session(self, open_id, key, value, clear_cache=True):
        """
        :param clear_cache: clear all cache in redis
        :param open_id: open_id
        :param key: key
        :param value: value that will store into redis
        :return:
        """
        # expire_time = self.redis_cli.hget(open_id, "expire_time")
        # if expire_time and clear_cache:
        #     expire_time = float(expire_time.decode("utf8"))
        #     self._expire_handler(open_id, expire_time)
        self.redis_cli.hmset(open_id, {
            key: str(value),
            "expire_time": str(datetime.timestamp(datetime.now() + timedelta(seconds=self.expire_duration_time)))
        })

    def add_session_with_map(self, open_id, value: dict, clear_cache=True):
        # expire_time = self.redis_cli.hget(open_id, "expire_time")
        # if expire_time and clear_cache:
        #     expire_time = int(expire_time)
        #     self._expire_handler(open_id, expire_time)
        value.update({
            "expire_time": datetime.timestamp(datetime.now())
        })
        self.redis_cli.hmset(open_id, value)

    def find(self, open_id, *keys):
        """
        find all keys if keys not provide, return a list if len(keys) > 0.
        :param open_id:
        :param keys:
        :return:
        """
        if len(keys) == 0:
            ret = self._find_all_session(open_id)
            ret = {k.decode("utf8"): v.decode("utf8") for k, v in ret.items()}
        elif len(keys) == 1:
            ret = self._find_session(open_id, keys[0])
            ret = ret if ret is None else ret.decode("utf8")
        else:
            ret = self._find_many_key_session(open_id, *keys)
            resp = []
            for r in ret:
                if r is None:
                    resp.append(None)
                else:
                    resp.append(r.decode("utf8"))
            ret = resp
        return ret

    def _find_session(self, open_id, key) -> bytes:
        return self.redis_cli.hget(open_id, key)

    def _find_all_session(self, open_id):
        return self.redis_cli.hgetall(open_id)

    def _find_many_key_session(self, open_id, *keys):
        return self.redis_cli.hmget(open_id, keys)
