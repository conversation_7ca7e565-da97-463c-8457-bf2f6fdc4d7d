from utils import message_card


class BaseInput:
    name: str = ""
    required: bool = False
    placeholder: str = "请输入"
    input_type: str = "text" # multiline_text
    label: str = ""

    def make_input(self):
        """
        输入框卡片
        """
        kwargs = {
            "name": self.label,
            "label": self.label,
            "placeholder": self.placeholder,
            "required": self.required,
            "input_type": self.input_type,
        }
        return message_card.make_input_div(div_name=self.label, **kwargs)


class NameOfBuild(BaseInput):
    name = "name_of_build"
    required = False
    placeholder = "当需要保存本次构建参数时请输入构建名称，否则请留空"
    label = "构建名称"
