"""
获取分支、镜像、k8s仓库信息

@author: <PERSON>
@date: 2022.08.16
"""
import json
import os
import random
import re
import subprocess
import time
import traceback
from urllib.parse import quote_plus, unquote_plus
import aiohttp
import asyncio
import pickle
import base64
from datetime import datetime, timedelta, timezone

import kubernetes
import requests
from kubernetes import client, config
from github import Github

from utils.connector import redis_cli
from utils import datastruct, exception, common
from utils.init import app
from utils.shell import Shell

token = os.getenv("TOKEN")


class AioRequest:

    def __init__(self, urls: list, headers=None):
        self.urls = urls
        self.headers = {} if headers is None else headers

    async def get(self, session, url, **kwargs):
        async with session.get(url, headers=self.headers, **kwargs) as response:
            return await response.json()

    async def post(self, session, url, **kwargs):
        async with session.post(url, headers=self.headers, **kwargs) as response:
            return await response.json()

    async def fetch_multi(self, method, session, urls, **kwargs):
        func = getattr(self, method)
        tasks = []
        for url in urls:
            task = asyncio.create_task(func(session, url, **kwargs))
            tasks.append(task)
        # gather: 搜集所有future对象，并等待返回
        results = await asyncio.gather(*tasks)
        return results

    async def request(self, method, **kwargs):
        async with aiohttp.ClientSession() as session:
            datas = await self.fetch_multi(method, session, self.urls, **kwargs)
            return datas


class RequestSession(requests.Session):

    def __init__(self, base_url):
        self.base_url = base_url
        super(RequestSession, self).__init__()

    def _get_cache(self, cache_key):
        """获取缓存"""
        ret = redis_cli.get(cache_key)
        if ret:
            return pickle.loads(ret)
        return False

    def _set_cache(self, cache_key, resp: requests.Response):
        """缓存数据"""
        res = pickle.dumps(resp)
        ret = redis_cli.setex(cache_key, 10, res)
        if not ret:
            app.logger.warn("redis缓存数据失败 %s" % resp)
        return ret

    def request(self, method, url, cache_key="", **kwargs):
        if cache_key:
            ret = self._get_cache(cache_key)
            if ret:
                return ret
            res = super(RequestSession, self).request(method, url, **kwargs)
            self._set_cache(cache_key, res)
        res = super(RequestSession, self).request(method, url, **kwargs)
        return res

    def prepare_request(self, request: requests.Request) -> requests.PreparedRequest:
        request.url = self.base_url + request.url
        return super(RequestSession, self).prepare_request(request)


class GithubApi:
    # TODO: 将api方式统一改成graph_url的方式
    graph_url = "https://api.github.com/graphql"
    base_url = "https://api.github.com"
    github_content_url = "https://raw.githubusercontent.com"

    def __init__(self, owner, repo):
        self.owner = owner
        self.repo = repo
        self.g = Github(os.getenv("TOKEN"))
        self.repoGithub = self.g.get_repo(f"{owner}/{repo}")
        self.req = RequestSession(self.base_url)
        self.graph_req = requests.Session()
        self.github_content_req = RequestSession(self.github_content_url)
        self.headers = {
            "Authorization": f"Bearer {token}"
        }
        self.req.headers = self.headers
        self.graph_req.headers = self.headers
        self.github_content_req.headers = self.headers

    def _get_artifacts_list(self, run_id, target_artifacts=None) -> list:
        target_artifacts = [] if target_artifacts is None else target_artifacts
        url = f"/repos/{self.owner}/{self.repo}/actions/runs/{run_id}/artifacts"
        artifacts = []
        ret = self.req.get(url)
        print(json.dumps(ret.json(), indent=4))
        if 200 <= ret.status_code < 400:
            for artifact in ret.json()["artifacts"]:
                if len(target_artifacts) and artifact["name"] in target_artifacts:
                    artifacts.append({"name": artifact["name"], "url": artifact["archive_download_url"]})
                elif len(target_artifacts) == 0:
                    artifacts.append({"name": artifact["name"], "url": artifact["archive_download_url"]})
            return artifacts
        app.logger.info(f"get workflow artifacts list failed, json is: {ret.json()}")
        return artifacts

    def download_file(self,
                      github_path,
                      target_name="".join(random.sample('zyxwvutsrqponmlkjihgfedcba', 8)),
                      tmp_dir="/tmp"
                      ):
        """
        1. 格式化目标文件路径
        2. 下载文件到目标文件路径

        :param github_path: 文件在github的路径
        :param target_name: 目标文件名
        :param tmp_dir: 临时存储目录
        :return: 返回目标文件路径
        """
        target_path = f"{tmp_dir}/{target_name}.sh"
        res = self.github_content_req.get(github_path)
        if 200 > res.status_code or res.status_code >= 300:
            print(res.text)
            return None
        with open(target_path, "wb") as f:
            f.write(res.content)
        return target_path

    def create_pr(self, source_branch, target_branch):
        """
        创建pr

        :param source_branch: 源分支
        :param target_branch: 目标分支
        """
        url = f"/repos/{self.owner}/{self.repo}/pulls"
        data = {
            "title": f"Merge {source_branch} to {target_branch}",
            "head": source_branch,
            "base": target_branch,
        }
        res = self.req.post(url, json=data)
        if 200 <= res.status_code < 300:
            return True, None
        app.logger.warn(f"create pr failed, json is: {res.json()}")
        return False, res

    def download_by_curl(self, run_id, tmp_dir=None, target_artifacts=None):
        tmp_dir = tmp_dir if tmp_dir is not None else "".join(random.sample('zyxwvutsrqponmlkjihgfedcba', 8))
        artifacts = self._get_artifacts_list(run_id, target_artifacts)
        app.logger.info("artifacts: ", artifacts)
        app.logger.info("run_id: ", run_id)
        result = {}
        for artifact in artifacts:
            path = f'/tmp/{tmp_dir}'
            app.logger.info(subprocess.Popen(f"mkdir -p {path}", shell=True, stdout=subprocess.PIPE).stdout.read())
            result[artifact["name"]] = f'{path}/{artifact["name"]}.zip'
            command = f"""
            curl -L \
              -H "Accept: application/vnd.github+json" \
              -H "Authorization: Bearer ****************************************" \
              --output {path}/{artifact["name"]}.zip \
              {artifact["url"]}
            """
            app.logger.info(subprocess.Popen(command, shell=True, stdout=subprocess.PIPE).stdout.read())
        app.logger.info("result: ", result)
        return result

    def cancel_workflow_run(self, run_id):
        url = f"/repos/{self.owner}/{self.repo}/actions/runs/{run_id}/cancel"
        ret = self.req.post(url)
        if 200 <= ret.status_code < 300:
            return True
        app.logger.info(f"cancel job failed, json is: {ret.json()}")
        return False

    def _branch(self, after, last_commit_day=30):
        query = r"""{
          repository(owner: "%s", name: "%s") {
            refs(refPrefix: "refs/heads/", first: 100, after: "%s") {
              pageInfo {
                endCursor
                hasNextPage
              }
              nodes {
                name
                id
                target {
                  ... on Commit {
                    committedDate
                  }
                }
              }
            }
          }
        }""" % (self.owner, self.repo, after)
        payload = {
            "query": query
        }
        res = self.graph_req.post(self.graph_url, json=payload)
        refs = res.json()["data"]["repository"].get("refs")
        if refs is None:
            return []
        branches = []
        date_30_days_ago = datetime.now(tz=timezone.utc) - timedelta(days=last_commit_day)
        for branch in refs["nodes"]:
            # 如果是以"release-v"开头，则直接添加
            if re.match(r'^release-v[0-9]+.[0-9]+.[0-9]+$', branch["name"]) or branch["name"] in ["main", "develop"]:
                branches.append(branch["name"])
                continue
            committed_date_naive = datetime.strptime(branch['target']['committedDate'], "%Y-%m-%dT%H:%M:%SZ")
            committed_date = committed_date_naive.replace(tzinfo=timezone.utc)
            # 获取30天内活跃的分支
            if committed_date < date_30_days_ago:
                continue
            branches.append(branch["name"])
        has_next_page = refs["pageInfo"]["hasNextPage"]
        end_cursor = refs["pageInfo"]["endCursor"]
        if not has_next_page:
            return branches
        return branches + self._branch(end_cursor)

    def _get_active_branch(self):
        # 30天内活跃的分支
        date_30_days_ago = datetime.now(tz=timezone.utc) - timedelta(days=30)
        branches = []
        for branch in self.repoGithub.get_branches():
            # 获取分支的最后一次提交
            last_commit = branch.commit
            # 如果最后一次提交的时间在30天之内
            if last_commit.commit.committer.date > date_30_days_ago:
                # 打印分支名
                branches.append(branch.name)
        return branches

    def branch(self, last_commit_day=30):
        """获取分支名称"""
        return self._branch("", last_commit_day=last_commit_day)
        # return self._get_active_branch()

    def image_tag(self):
        """获取镜像tag列表"""
        url = f"/orgs/{self.owner}/packages"
        cache_key = self.base_url + url
        res = self.req.get(url, params={"package_type": "container", "visibility": "private"}, cache_key=cache_key)
        images = []
        data = {
            "package_type": "container",
            "visibility": "private",
        }
        urls = []
        for image in res.json():
            image_url_safe = quote_plus(image["name"])
            url = self.base_url + f"/orgs/tapdata/packages/container/{image_url_safe}/versions"
            urls.append(url)
        images_version = asyncio.run(AioRequest(urls, headers=self.headers).request("get", params=data))
        for image_version in images_version:
            tags = []
            image_name = ""
            for tag in image_version:
                tag_list = tag["metadata"]["container"]["tags"]
                create_at = tag["created_at"]
                update_at = tag["updated_at"]
                image_name = re.findall(
                    r"https://api.github.com/orgs/" + f"{self.owner}" + r"/packages/container/([\s\S]*)/versions/*",
                    tag["url"]
                )
                image_name = unquote_plus(image_name[0])
                for t in tag_list:
                    tag_info = dict()
                    tag_info["tag"] = t
                    tag_info["create_at"] = create_at
                    tag_info["update_at"] = update_at
                    tags.append(tag_info)
            images.append({
                image_name: tags
            })
        return images

    def _list_runner(self, event="", status="", workflow_id=None, **kwargs):
        params = {}
        if event:
            params.update({
                "event": event,
            })
        if status:
            params.update({
                "status": status,
            })
        params.update(kwargs)
        if workflow_id is None:
            url = f"/repos/{self.owner}/{self.repo}/actions/runs"
        else:
            url = f"/repos/{self.owner}/{self.repo}/actions/workflows/{workflow_id}/runs"
        res = self.req.get(url, params=params)
        print(f"res is: {res}, text: {res.json()}, url: {url}")
        return res.json()["workflow_runs"]

    def list_runner_in_progress(self):
        event = "repository_dispatch"
        status = "in_progress"
        return self._list_runner(status=status, event=event)

    def list_runner(self, status="", event="repository_dispatch"):
        status = status
        return self._list_runner(event=event, status=status)

    def list_workflow_dispatch_runner(self, branch, created, workflow_id=None):
        """return runner ids"""
        params = {
            "branch": branch,
            "status": "in_progress",
            "event": "workflow_dispatch",
            "created": created
        }
        if workflow_id is not None:
            params.update({"workflow_id": workflow_id})
        ids = []
        for workflow in self._list_runner(**params):
            ids.append(workflow["id"])
        return ids

    def _get_run_info(self, run_id):
        url = f"/repos/{self.owner}/{self.repo}/actions/runs/{run_id}"
        res = self.req.get(url)
        return res.json()

    def get_workflow_logs(self, job_id):
        url = f"/repos/{self.owner}/{self.repo}/actions/jobs/{job_id}/logs"
        res = self.req.get(url)
        return res.text

    def get_job_info(self, run_id):
        url = f"/repos/{self.owner}/{self.repo}/actions/runs/{run_id}/jobs"
        print(run_id)
        res = self.req.get(url)
        print(f"res is: {res}, text: {res.json()}, url: {res.url}", f"headers: {res.request.headers}")
        return res.json()["jobs"]

    def get_failed_job_in_sub_workflow(self, p_run_id):
        """
        获取子流程失败的Job名称

        1. 获取主流程的Job
        2. 获取子流程的Job的工作流的URL
        3. 获取子流程工作流失败的Job name
        """
        # 获取主流程的Job
        jobs = self.get_job_info(p_run_id)
        for job in jobs:
            if job.get("conclusion") == "failure":
                job_id = job.get("id")
                # 获取工作流的信息
                job_logs = self.get_workflow_logs(job_id)
                pattern = r"The workflow logs can be found at (https://github\.com/tapdata/tapdata-application/actions/runs/\d+)"
                matches = re.findall(pattern, job_logs)
                if matches:
                    sub_workflow_url = matches[0]
                    sub_workflow_id = sub_workflow_url.split("/")[-1]
                    print(f"sub_workflow_id is: {sub_workflow_id}")
                    # 获取自流程失败的Job
                    failed_jobs = GithubApi("tapdata", "tapdata-application").get_failed_job_by_workflow_id(sub_workflow_id)
                    return failed_jobs
                break
        return []

    def get_failed_job_by_workflow_id(self, workflow_id):
        url = f"/repos/{self.owner}/{self.repo}/actions/runs/{workflow_id}/jobs"
        response = self.req.get(url)
        jobs = response.json()["jobs"]
        failed_jobs = []
        for job in jobs:
            if job["conclusion"] == "failure" and job["status"] == "completed":
                failed_jobs.append(job["name"])
        return failed_jobs

    def _find_runner_id(self, cicd_job_id, event="repository_dispatch", status=""):
        runs = self.list_runner(status=status, event=event)
        if not runs:
            runs = self.list_runner(status="queued")
        for run in runs:
            job_info = self.get_job_info(run["id"])
            for job in job_info:
                for step in job["steps"]:
                    if step["name"] == cicd_job_id:
                        return job["run_id"], job["html_url"]
        return None, None

    def find_runner_id(self, cicd_job_id, is_watch=True, event="repository_dispatch", status=""):
        # 检查策略：
        # 0-10 min：每分钟检查一次
        # 10-60 min：每5分钟检查一次
        for t in range(20):
            try:
                run_id, html_url = self._find_runner_id(cicd_job_id, event=event, status=status)
                app.logger.info("waiting for start, %s times/20 times" % (t + 1))
                if not run_id or not html_url:
                    if not is_watch:
                        return None, None
                    if t < 10:
                        time.sleep(60)
                    else:
                        time.sleep(300)
                    continue
                return run_id, html_url
            except Exception as e:
                if not is_watch:
                    return None, None
                app.logger.warn("%s" % traceback.format_exc())
                if t < 10:
                    time.sleep(60)
                else:
                    time.sleep(300)
                continue
        return None, None

    def runner_branch_info_cloud(self, run_id, image_repo=os.getenv("IMAGE_REPO")):
        ret = self.get_job_info(run_id)
        result = {
            "tm_java_tag": None,
            "tapdata-agent_tag": None,
            "agent_tag": None,
            "console_tag": None,
            "tcm_tag": None,
            "cloud_tag": None,
        }
        for job in ret:
            for step in job["steps"]:
                if step["name"].startswith("Dfs_tag="):
                    name = step["name"].split(";")
                    tm_java_tag = name[1].replace("OPENSOURCE_TAG=", f"{image_repo}/dfs-tm-java:")
                    agent_tag = name[4].replace(
                        "CLOUD_TAG=",
                        "tapdata-docker.pkg.coding.net/dfs/flow-engine/dfs-flow-engine:"
                    )
                    tapdata_agent_tag = name[2].replace("ENTERPRISE_TAG=", "")
                    console_tag = name[3].replace("FRONTEND_TAG=", f"{image_repo}/dfs-console:")
                    tcm_tag = name[4].replace("CLOUD_TAG=", f"{image_repo}/dfs-tcm:")
                    cloud_tag = name[4].replace("CLOUD_TAG=", "")
                    result = {
                        "tm_java_tag": tm_java_tag,
                        "tapdata-agent_tag": tapdata_agent_tag,
                        "agent_tag": agent_tag,
                        "console_tag": console_tag,
                        "tcm_tag": tcm_tag,
                        "cloud_tag": cloud_tag,
                    }
        display_tapdata_agent = True
        display_agent = True
        for job in ret:
            if job["name"] == "Build-TCM-Cloud" and job["conclusion"] == "skipped":
                result["tcm_tag"] = None
            print(job["name"], job["conclusion"])
            if job["name"] == "Build-Iengine-Cloud" and job["conclusion"] == "skipped":
                display_agent = False
                result["agent_tag"] = None
            if job["name"] == "Build-TM-Cloud" and job["conclusion"] == "skipped":
                result["tm_java_tag"] = None
            if job["name"] == "Build-Frontend-Cloud" and job["conclusion"] == "skipped":
                result["console_tag"] = None
            if job["name"] == "Build-Tapdata-Agent" and job["conclusion"] == "skipped":
                result["tapdata-agent_tag"] = None
                display_tapdata_agent = False
            if not display_tapdata_agent and not display_agent:
                result["cloud_tag"] = None
        print(result.values())
        return result.values()

    def runner_branch_info_enterprise(self, run_id, image_repo=os.getenv("IMAGE_REPO")):
        ret = self.get_job_info(run_id)
        for job in ret:
            for step in job["steps"]:
                if step["name"].startswith("Tag="):
                    name = step["name"].split(";")
                    tag = name[0].replace("Tag=", "")
                    current_branch = name[1].replace("CURRENT_BRANCH=", "")
                    package_image_tag = f"{image_repo}/{current_branch}:{tag}"
                    opensource_branch = name[2].replace("OPENSOURCE_BRANCH=", "")
                    frontend_branch = name[3].replace("FRONTEND_BRANCH=", "")
                    return package_image_tag, current_branch, opensource_branch, frontend_branch, tag

    def get_runner_info(self, run_id) -> dict:
        url = f"/repos/{self.owner}/{self.repo}/actions/runs/{run_id}"
        res = self.req.get(url)
        return res.json()

    def get_runner_job_info(self, run_id) -> dict:
        url = f"/repos/{self.owner}/{self.repo}/actions/runs/{run_id}/jobs"
        res = self.req.get(url)
        return res.json()

    def trigger_by_workflow_dispatch(self, workflow_id, ref, **kwargs):
        """
        通过workflow_dispatch的方式触发流程
        :param workflow_id: workflow id
        :param ref: 触发的主分支
        :param kwargs: 参数列表
        :return:
        """
        app.logger.info(kwargs)
        app.logger.info(f"https://api.github.com/repos/{self.owner}/{self.repo}/actions/workflows/{workflow_id}/dispatches")
        res = requests.post(
            f"https://api.github.com/repos/{self.owner}/{self.repo}/actions/workflows/{workflow_id}/dispatches",
            headers={
                "Accept": "application/vnd.github+json",
                "Authorization": f"Bearer {os.getenv('TOKEN')}",
                "X-GitHub-Api-Version": "2022-11-28",
            },
            json={
                "ref": ref,
                "inputs": common.clean_kwargs(**kwargs),
            }
        )
        if 200 <= res.status_code < 300:
            return True
        else:
            app.logger.warn(res.json())
            return False

    def workflow_dispatch_trigger(self, workflow_id, ref,
                                  **kwargs):
        # 2分钟前的时间，转换为UTC时间后，格式为 >2024-03-22T06:44:00Z
        two_minutes_ago = datetime.utcnow() - timedelta(minutes=2)
        two_minutes_ago = two_minutes_ago.strftime(">%Y-%m-%dT%H:%M:%SZ")
        # 1. 查看2分钟前触发的流程ID列表
        old_ids = self.list_workflow_dispatch_runner(ref, two_minutes_ago)
        # 2. 通过workflow_dispatch触发流程
        if not self.trigger_by_workflow_dispatch(workflow_id, ref, **kwargs):
            return False
        # 3. 查看2分钟前触发的流程ID列表，对比新的流程ID
        new_ids = old_ids
        while new_ids == old_ids:
            new_ids = self.list_workflow_dispatch_runner(ref, two_minutes_ago)
            time.sleep(10)
        new_ids = list(set(new_ids) - set(old_ids))
        # 如果触发多个流程，只取第一个
        if len(new_ids) > 1:
            app.logger.warn(f"触发了多个流程: {new_ids}")
        new_id = new_ids[0]
        return new_id

    def workflow_dispatch_trigger_and_wait(self, workflow_id, ref,
                                           send_create_job_result=None,
                                           send_detail_info=None,
                                           **kwargs):
        """
        # 1. 查看2分钟前触发的流程ID列表
        # 2. 通过workflow_dispatch触发流程
        # 3. 查看2分钟前触发的流程ID列表，对比新的流程ID
        # 4. 查看新的流程ID的状态，如果成功则返回，如果失败则返回失败

        send_create_job_result: 发送创建任务的结果
        send_detail_info: 发送任务链接

        """
        # 2分钟前的时间，转换为UTC时间后，格式为 >2024-03-22T06:44:00Z
        two_minutes_ago = datetime.utcnow() - timedelta(minutes=2)
        two_minutes_ago = two_minutes_ago.strftime(">%Y-%m-%dT%H:%M:%SZ")
        # 1. 查看2分钟前触发的流程ID列表
        old_ids = self.list_workflow_dispatch_runner(ref, two_minutes_ago)
        # 2. 通过workflow_dispatch触发流程
        if not self.trigger_by_workflow_dispatch(workflow_id, ref, **kwargs):
            if send_create_job_result is not None:
                send_create_job_result(False, **kwargs)
            return "", False
        if send_create_job_result is not None:
            send_create_job_result(True, **kwargs)
        # 3. 查看2分钟前触发的流程ID列表，对比新的流程ID
        new_ids = old_ids
        while new_ids == old_ids:
            new_ids = self.list_workflow_dispatch_runner(ref, two_minutes_ago, workflow_id=workflow_id)
            time.sleep(10)
        new_ids = list(set(new_ids) - set(old_ids))
        # 4. 查看新的流程ID的状态，如果成功则返回，如果失败则返回失败
        # 如果触发多个流程，只取第一个
        if len(new_ids) > 1:
            app.logger.warn(f"触发了多个流程: {new_ids}")
        new_id = new_ids[0]
        # hook 主要用来发送流程链接消息
        if send_detail_info is not None:
            detail_url = self._get_run_info(new_id).get("html_url")
            send_detail_info(new_id, detail_url)
        # 监听流程是否执行结束
        conclusion = None
        status = ""
        while conclusion is None and status != "completed":
            run_info = self.get_runner_info(new_id)
            conclusion, status = run_info.get("conclusion"), run_info.get("status")
            time.sleep(10)
        # 监听流程执行结果
        if conclusion == "success" and status == "completed":
            return new_id, True
        else:
            return new_id, False

    def trigger_sync_agent_version(self, dfs_branch, agent_version):
        """触发Agent镜像分发
        :param dfs_branch: 分支名称
        :param agent_version: Agent版本
        """
        url = f"/repos/{self.owner}/{self.repo}/dispatches"
        data = {
            "event_type": "feishu trigger",
            "client_payload": {
                "agent_version": agent_version,
                "dfs_branch": dfs_branch,
            }
        }
        res = self.req.post(url, headers=self.headers, json=data)
        if 200 <= res.status_code < 300:
            return True
        else:
            app.logger.warn("%s" % res.text)
            return False

    def trigger(self,
                branches,
                cicd_job_id,
                enterprise_or_cloud="enterprise",
                **kwargs
                ):
        """触发
        :param branches: 分支名称, 规则为 FRONTEND_BRANCH=前端分支;CURRENT_BRANCH=企业版分支;OPENSOURCE_BRANCH=开源版分支;DRS_BRANCH=云版分支
        :param enterprise_or_cloud: 企业版/云版/两者都打包 -> enterprise/cloud/both
        :param cicd_job_id: 随机uuid字符串，用于确定github action的run id
        """

        def _connectors(conns):
            return base64.b64encode(conns.encode("utf-8")).decode("utf-8")

        owner = os.getenv("OWNER")
        url = f"/repos/{owner}/{self.repo}/dispatches"
        data = {
            "event_type": "feishu trigger",
            "client_payload": {
                "branches": branches,
                "id": cicd_job_id,
                "enterprise_or_cloud": enterprise_or_cloud,
            }
        }

        if kwargs.get("connectors"):
            connectors = kwargs.pop("connectors")
            connectors = _connectors(connectors) if connectors else ""
            data["client_payload"].update({
                "connectors": connectors,
            })

        data["client_payload"].update(kwargs)
        print(json.dumps(data["client_payload"], indent=4))
        if len(data["client_payload"]) > 10:
            app.logger.warn("参数过多，可能会导致github action运行失败")
            raise Exception("参数过多，可能会导致github action运行失败")
        res = self.req.post(url, headers=self.headers, json=data)
        if 200 <= res.status_code < 300:
            return True
        else:
            app.logger.warn("%s" % res.text)
            return False

    def get_connectors(self, path, *repos, except_path=None, ref='master'):
        """
        获取连接器列表
        :param ref: 分支
        :param except_path: 排除某些文件名/目录
        :param path: 路径
        :param repos: 仓库列表
        :return:
        """
        if not isinstance(except_path, list):
            except_path = []
        urls = []
        for repo in repos:
            url = f"https://api.github.com/repos/{self.owner}/{repo}/contents/{path}"
            urls.append(url)
        ret_list = asyncio.run(AioRequest(urls, headers=self.headers).request("get", params={"ref": ref}))
        connectors = set()
        for ret in ret_list:
            for connector_info in ret:
                if connector_info["name"] in except_path:
                    continue
                connectors.add(connector_info["name"])
        return list(connectors)

    def runner_info(self, run_id):
        ret = self.get_job_info(run_id)
        resp = {}
        for job in ret:
            for step in job["steps"]:
                if ";" in step["name"] and "tag" in step["name"].lower():
                    for field in step["name"].split(";"):
                        if len(field) == 0:
                            continue
                        key, value = field.split("=")
                        if key.lower() == "tag":
                            resp["Dfs_tag"] = value
                        resp[key] = value
        return resp

    class RunnerConclusion:
        success = "success"
        failure = "failure"
        cancelled = "cancelled"
        start_failed = "start_failed"
        in_progress = "in_progress"

    def _get_runner_info(self, runner_id):
        res = self.get_runner_info(runner_id)
        status = self.RunnerConclusion.in_progress
        if res["status"] == "completed":
            if res.get("conclusion", None) == self.RunnerConclusion.success:
                status = self.RunnerConclusion.success
            elif res.get("conclusion", None) == self.RunnerConclusion.cancelled:
                status = self.RunnerConclusion.cancelled
            elif res.get("conclusion", None) == self.RunnerConclusion.failure:
                status = self.RunnerConclusion.failure
        return self.runner_info(runner_id), status

    def job_status(self, job_id, runner_url_fn=None, event="repository_dispatch"):
        """
        1. 根据job_id获取runner_id
        2. 发送任务已经运行的通知
        3. 监听runner运行
        :param event: 事件类型 repository_dispatch / workflow_dispatch
        :param job_id: 任务ID
        :param runner_url_fn: 回调函数，一般用来发送任务链接
        :return:
        """
        runner_id, detail_url = self.find_runner_id(job_id, event=event)
        if runner_id is None or detail_url is None:  # 获取不到任务信息，任务创建失败
            return self.RunnerConclusion.start_failed, False
        else:
            if runner_url_fn is not None:
                runner_url_fn(runner_id, detail_url)
        resp, status = {}, False
        for t in range(20):
            app.logger.info("waiting for completed, %s times / %s times" % (t + 1, 20))
            resp, status = self._get_runner_info(runner_id)
            app.logger.info(f"[{job_id}]workflow status is {status}, resp is {resp}")
            if status != self.RunnerConclusion.in_progress:
                break
            if t < 10:
                time.sleep(60)
            else:
                time.sleep(300)
        return resp, status

    def list_pr(self, repo, state="open", source_branch=None, target_branch=None):
        """
        列出pr
        :param repo: 仓库名
        :param state: 状态, open/closed
        :param source_branch: 源分支
        :param target_branch: 目标分支
        """
        url = f"/repos/{self.owner}/{repo}/pulls"
        params = {
            "state": state,
        }
        if source_branch:
            params.update({
                "head": f"{self.owner}:{source_branch}",
            })
        if target_branch:
            params.update({
                "base": target_branch,
            })
        res = self.req.get(url, params=params)
        return res.json()


class K8sApi:

    def __init__(self, namespace):
        self.namespace = namespace
        self._init_configuration()
        self.v1 = client.AppsV1Api()
        self.api_instance = client.CoreV1Api()

    def _init_configuration(self):
        if os.getenv("FLASK_ENV") == "development":
            # context = "docker-desktop"
            context = "admin@local"
        elif os.getenv("FLASK_ENV") == "production":
            context = "admin@local"
        else:
            raise exception.ModeError
        config.load_kube_config("~/.kube/config", context=context)

    def list_stateful_set(self) -> [str]:
        svc = self.list_svc()
        search_dict = {}
        for s in svc.items:
            try:
                search_dict[s.spec.selector["app"]] = s.spec.ports
            except KeyError:
                continue
        stateful_sets = []
        for item in self.v1.list_namespaced_stateful_set(self.namespace).items:
            if search_dict.get(item.metadata.name) is not None:
                ports = []
                for p in search_dict.get(item.metadata.name):
                    ports.append(p.node_port)
                ports = ",".join([str(i) for i in ports])
            else:
                ports = ""
            stateful_sets.append((item.metadata.name, ports))
        return stateful_sets

    def list_svc(self):
        svc = self.api_instance.list_namespaced_service(self.namespace)
        return svc

    def list_deployment_with_svc(self) -> [str]:
        """列出deployments"""
        svc = self.list_svc()
        search_dict = {}
        for s in svc.items:
            try:
                search_dict[s.spec.selector["app"]] = s.spec.ports
            except KeyError:
                continue
        deployments = []
        for item in self.v1.list_namespaced_deployment(self.namespace).items:
            if search_dict.get(item.metadata.name) is not None:
                ports = []
                for p in search_dict.get(item.metadata.name):
                    ports.append(p.node_port)
                ports = ",".join([str(i) for i in ports])
            else:
                ports = ""
            deployments.append((item.metadata.name, ports))
        return deployments

    def env(self):
        """获取环境列表"""
        deployments = self.v1.list_namespaced_deployment(self.namespace)
        env_names = []
        for deployment in deployments.items:
            env_names.append(deployment.metadata.name)
        return env_names

    def svc(self, env_name, container_port=3030):
        services = self.api_instance.list_namespaced_service(self.namespace)
        for service in services.items:
            if env_name == service.spec.selector.get("io.kompose.service") or \
                    env_name == service.spec.selector.get("app"):
                if len(service.spec.ports) == 1:
                    return service.spec.ports[0].node_port
                for port in service.spec.ports:
                    if str(port.target_port) == str(container_port):
                        return port.node_port
        return False

    def deployment_status(self, env_name, tag):
        try:
            deployment = self.v1.read_namespaced_deployment(env_name, self.namespace)
        except kubernetes.client.exceptions.ApiException as e:
            app.logger.warn(e)
            if e.status == 404:
                return False, False
            else:
                raise exception.K8sError
        # 不可用的pod为0
        if (deployment.status.unavailable_replicas is None or deployment.status.unavailable_replicas == 0) and \
                deployment.status.available_replicas is not None and deployment.status.available_replicas >= 1:
            status = False
            for container in deployment.spec.template.spec.containers:
                if container.image == tag:
                    status = True
                    break
            return status, True
        else:
            return False, True

    def check_pod_event(self, namespace, env_name) -> dict:
        pods = []
        for item in self.api_instance.list_namespaced_pod(namespace).items:
            if env_name in item.metadata.name:
                pods.append(item.metadata.name)
        result = {}
        for pod in pods:
            pod_events = self.api_instance.list_namespaced_event(namespace, field_selector=f'involvedObject.name={pod}')
            info = {}
            for pod_event in pod_events.items:
                info["message"] = pod_event.message
                info["reason"] = pod_event.reason
                start_time = pod_event.event_time or pod_event.first_timestamp
                start_time = start_time + timedelta(hours=8)
                info["start_time"] = start_time.strftime("%Y-%m-%d %H:%M:%S")
                info["type"] = pod_event.type
                info["create_time"] = pod_event.metadata.creation_timestamp
                result[pod] = info
        return result

    def update_env(self, branch, tag, env_name, is_cloud=False):
        """更新环境"""
        tag = f"{branch}:{tag}"
        try:
            deployment_obj = self.v1.read_namespaced_deployment(env_name, self.namespace)
            deployment_obj.spec.template.spec.containers[0].image = tag
            self.v1.patch_namespaced_deployment(env_name, self.namespace, deployment_obj)
            return env_name
        except client.exceptions.ApiException as e:
            app.logger.error(traceback.format_exc())
            return None

    def create_env(self, branch, tag, name, **kwargs):
        """创建环境"""
        namespace = os.getenv("NAMESPACE")
        url = f"/kapis/openpitrix.io/v1/workspaces/{namespace}/namespaces/{namespace}/applications"
        conf = datastruct.KubernetesAppConfig(branch, tag, **kwargs)
        data = {
            "app_id": os.getenv("K8S_APP_ID"),
            "name": name,
            "version_id": os.getenv("APP_VERSION"),
            "conf": conf.to_str()
        }
        res = self.req.post(url, json=data)
        if 200 <= res.status_code < 300:
            if res.json()["message"] == "success":
                return name
            else:
                return False
        app.logger.warn("create env failed, response body is %s" % res.text)
        return False


class Coding:
    base_url = os.getenv("CODING_HOST")
    token = os.getenv("CODING_TOKEN")
    project_map = {
        "tapdata": 342870,
        "dfs": 8438504,
    }

    def __init__(self, project_name):
        self.req = RequestSession(self.base_url)
        self.req.headers.update({
            "Authorization": f"token {self.token}"
        })
        self.project_name = project_name

    def describe_issue_list_with_page(self, page_number=1, **kwargs):
        action = "DescribeIssueListWithPage"
        url = f"/open-api?Action={action}"
        payload = {
            "Action": action,
            "ProjectName": self.project_name,
            "IssueType": "ALL",
            "PageNumber": page_number,
            "PageSize": 500,
            "Conditions": [{
                "Key": "PRIORITY",
                "value": "3"
            }, {
                "Key": "STATUS_TYPE",
                "value": "PROCESSING,TODO"
            }]
        }
        payload.update(kwargs)
        res = self.req.post(url, json=payload)
        if 200 <= res.status_code < 300:
            issue_list = res.json()["Response"]["Data"]["List"]
            total_count, page_count = res.json()["Response"]["Data"]["TotalCount"], 500
            # 如果存在多页，则递归调用获取所有的issue
            if total_count // page_count > 1:
                issue_list += self.describe_issue_list_with_page(page_number=total_count // page_count + 1)
            return issue_list
        return []

    def describe_package_list(self, repo: str, page_number=1, **kwargs):
        """
        获取仓库包列表
        :param project: 项目名称
        :param repo: 仓库名称
        :param page_number: 页码
        :param kwargs:
        :return:
        """
        if self.project_name not in self.project_map.keys():
            raise Exception("不支持当前的project")

        action = "DescribeArtifactVersionList"
        url = f"/open-api?Action={action}"
        payload = {
            "Action": "DescribeArtifactPackageList",
            "ProjectId": self.project_map[self.project_name],
            "PageNumber": page_number,
            "PageSize": 10,
            "Repository": repo,
            "Package": ""
        }
        payload.update(kwargs)
        res = self.req.post(url, json=payload)
        if 200 <= res.status_code < 300:
            package_list = res.json()["Response"]["Data"]["InstanceSet"]
            total_count, page_count = res.json()["Response"]["Data"]["TotalCount"], 500
            if total_count // page_count > 1:
                package_list += self.describe_package_list(repo, page_number=total_count // page_count + 1)
            return package_list
        return []

    def describe_package_version(self, repo: str, package: str, page_number=1, **kwargs):
        action = "DescribeArtifactVersionList"
        url = f"/open-api?Action={action}"
        payload = {
            "Action": "DescribeArtifactVersionList",
            "ProjectId": self.project_map[self.project_name],
            "PageNumber": page_number,
            "PageSize": 100,
            "Repository": repo,
            "Package": package
        }
        payload.update(kwargs)
        res = self.req.post(url, json=payload)
        if 200 <= res.status_code < 300:
            version_list = res.json()["Response"]["Data"]["InstanceSet"]
            total_count, page_count = res.json()["Response"]["Data"]["TotalCount"], 100
            if total_count <= (page_count * page_number + 1):
                version_list += self.describe_package_version(repo, package,
                                                              page_number=total_count // page_count + 1)
            return version_list
        return []

    def formatted_package_version(self, repo: str, package: str, page_number=1, **kwargs):
        """格式化tag版本列表，按照时间倒序排列，返回tag版本列表"""
        package_version_list = self.describe_package_version(repo, package, page_number, **kwargs)
        package_map = {}
        for package_version in package_version_list:
            package_map[package_version["CreatedAt"]] = package_version["Version"]
        sorted_timestamp = sorted(package_map.keys())
        version_list = []
        for s in sorted_timestamp[::-1]:
            version_list.append(package_map[s])
        return version_list

    def describe_team_members(self):
        """
        TODO: 拿到权限后再解析数据列表
        :return:
        """
        action = "DescribeTeamMembers"
        url = f"/open-api?Action={action}"
        payload = {
            "Action": "DescribeTeamMembers",
            "PageNumber": 1,
            "PageSize": 500
        }


def run_command(func):
    def _wrap(*args, **kwargs):
        command = func(*args, **kwargs)
        out, err, ret_code = Shell().execute(command, shell=True)
        out = None if out is None else out.decode("utf8")
        err = None if err is None else err.decode("utf8")
        return out, err, ret_code

    return _wrap


class KubernetesCommand:
    _kubeconfig_path = {
        "qingcloud": "/conf/qingcloud/kubeconfig",
        "huaweiCloud": "/conf/huaweiCloud/kubeconfig",
        "gcp": "/conf/gcp/kubeconfig",
        "alicloud": "/conf/alicloud/kubeconfig",
    }

    def __init__(self, cloud_type="gcp"):
        self._cloud_type = cloud_type
        self._kubectl = f"kubectl --kubeconfig {self._kubeconfig}"

    @property
    def _kubeconfig(self):
        pre_path = "." if os.getenv("FLASK_ENV") == "development" else ""
        try:
            return f"{pre_path}{self._kubeconfig_path[self._cloud_type]}"
        except KeyError:
            raise Exception("不存在该云的kubeconfig文件")

    @run_command
    def rollout_status(self, deployment, namespace, timeout=2):
        """检查滚动更新的状态"""
        command = f"{self._kubectl} rollout status deployment/{deployment} -n {namespace} --timeout={timeout}m"
        return command

    @run_command
    def get_deployment_image(self, deployment, namespace):
        """获取deployment当前指定的镜像"""
        command = f"{self._kubectl} get deployment {deployment} -n {namespace} " \
                  "-o=jsonpath='{.spec.template.spec.containers[0].image}'"
        return command

    @run_command
    def scale_deployment(self, deployment, namespace, replicas):
        command = f"kubectl scale deployment {deployment} --replicas={replicas} -n {namespace}"
        return command

    @run_command
    def get_namespace(self):
        """获取namespace"""
        command = f"{self._kubectl} get namespace | grep -v NAME | awk '{{print $1}}'"
        return command

    @run_command
    def get_deployment_name(self, namespace):
        """获取deployment的名称"""
        command = f"{self._kubectl} get deployment -n {namespace} | grep -v NAME | awk '{{print $1}}'"
        return command

    @run_command
    def get_service_name(self, namespace):
        """获取service的名称"""
        command = f"{self._kubectl} get service -n {namespace} | grep -v NAME | awk '{{print $1}}'"
        return command

    @run_command
    def get_service(self, namespace):
        """获取service的信息"""
        command = f"{self._kubectl} get service -n {namespace}"
        return command

    @run_command
    def get_lb_ip(self, namespace, svc_name):
        """获取lb的ip"""
        command = f"{self._kubectl} get service {svc_name} -n {namespace} -o=jsonpath='{{.status.loadBalancer.ingress[0].ip}}'"
        return command

    @run_command
    def get_svc_port(self, namespace, svc_name, target_port):
        """获取service的端口，适用于GCP"""
        command = f"{self._kubectl} get service {svc_name} -n {namespace} -o=jsonpath='{{.spec.ports[?(@.targetPort=={target_port})].port}}'"
        return command

    @run_command
    def get_service_ip(self, deployment, namespace):
        """获取deployment对应的pod的IP 适用于OP版"""
        command = f"{self._kubectl} get service {deployment} -n {namespace} " \
                  "-o jsonpath='{.spec.clusterIP}'"
        return command

    @run_command
    def get_service_eip(self, deployment, namespace):
        """获取deployment对应的pod的EIP 适用于青云OP版"""
        command = f"{self._kubectl} get service {deployment} -n {namespace} " \
                  """-o jsonpath='{.status.loadBalancer.ingress}' | jq '.[] | select(.ip | startswith("139.198")).ip'"""
        return command

    @run_command
    def get_service_port(self, deployment, namespace):
        """获取deployment对应的pod的端口 适用于OP版"""
        command = f"{self._kubectl} get service {deployment} -n {namespace} " \
                  "-o jsonpath='{.spec.ports[?(@.targetPort==3030)].port}'"
        return command

    @run_command
    def get_pods_ips(self, deployment, namespace):
        """获取deployment对应的pod的IP"""
        command = f"{self._kubectl} get pods -n {namespace} -l app={deployment} " \
                  "-o jsonpath='{.items[*].status.podIP}'"
        return command


class GithubV2Api(GithubApi):
    pass


class AlistFileserver:
    host = "*************:5244"
    uri = {
        "auth": "/api/auth/login",
        "list_fs": "/api/fs/list",
    }

    def __init__(self, username, password):
        self.username = username
        self.password = password
        self.token = self._auth()

    def _auth(self):
        url = f"http://{self.host}{self.uri['auth']}"
        res = requests.post(url, json={
            "username": self.username,
            "password": self.password,
        })
        try:
            res_data = res.json()
            if res_data["code"] == 200:
                return res_data["data"]["token"]
            else:
                app.logger.error(res.json())
                raise "Auth to fileserver failed."
        except Exception as e:
            app.logger.error(e)
            raise "Auth to fileserver failed."

    def list_path(self, path, page=1):
        """
        列出指定路径下的文件
        
        Args:
            path: 要列出的路径
            page: 当前页码,默认为1
            
        Returns:
            list: 文件列表
        
        Raises:
            RuntimeError: 当访问文件服务器失败时抛出
        """
        req_body = {
            "path": path,
            "page": page,
            "per_page": 100,
            "refresh": False,
        }
        url = f"http://{self.host}{self.uri['list_fs']}"
        
        try:
            res = requests.post(url, headers={"Authorization": self.token}, json=req_body)
            res_json = res.json()
            
            if res_json["code"] == 200:
                total = res_json["data"]["total"]
                files = res_json["data"]["content"]
                
                # 如果还有下一页,递归获取
                if total > page * 100:
                    try:
                        next_page_files = self.list_path(path, page + 1)
                        if next_page_files:
                            files.extend(next_page_files)
                    except Exception as e:
                        app.logger.error(f"获取下一页失败: {str(e)}")
                return files
                
            app.logger.error(f"访问路径 {path} 失败: {res_json}")
            return []
            
        except Exception as e:
            app.logger.error(f"访问文件服务器失败: {str(e)}")
            raise RuntimeError("访问文件服务器失败") from e


if __name__ == '__main__':
    from dotenv import load_dotenv, find_dotenv

    load_dotenv(find_dotenv(".env"))
    alist = AlistFileserver("admin", "Gotapd8!")
    res = alist.list_path("/gz")
    print(len(res))
