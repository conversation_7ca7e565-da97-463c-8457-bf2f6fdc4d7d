"""Github Action相关的工具类"""

from utils.api import GithubApi


class GithubAction:
    name = "feishu trigger"
    job_id = None
    Tag = None
    Status = None
    Info = None


class GithubActionFactory:
    """Github Action工厂类"""

    def __init__(self,
                 github_action: GithubAction,
                 sleep_time=30,
                 job_watch_fn=None,
                 job_info_fn=None,
                 ):
        self.owner, self.repo = os.getenv("OWNER"), os.getenv("TRIGGER_REPO")
        self.github_action = github_action
        self.sleep_time = sleep_time
        self.job_watch_fn = job_watch_fn
        self.job_info_fn = job_info_fn

    def default_job_watch(self):
        github_obj = GithubApi(owner, repo)
        runner_info, status = github_obj.job_status(self.github_action.job_id)
        if status and status not in [github_obj.RunnerConclusion.failure, github_obj.RunnerConclusion.cancelled]:
            return True, runner_info, status
        else:
            return False, runner_info, status

    def job_info_export(self):
        """获取job信息"""
        while True:
            if self.job_watch_fn(self.github_action.job_id):
                break
            time.sleep(self.sleep_time)
        return self.job_info_fn(self.github_action.job_id)
