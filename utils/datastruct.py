"""
some struct

@author: <PERSON>
@date: 2022.08.31
"""
import os


class KubernetesAppConfig:

    def __init__(self,
                 branch,
                 tag,
                 replica_count=1,
                 image_repo=os.getenv("DEFAULT_IMAGE_REPO")
                 ):
        """
        k8s 应用配置
        :param replica_count: 副本数
        :param branch: 分支
        :param tag: 容器tag
        :param image_repo: 镜像
        """
        self.image_repo = image_repo
        self.tag = tag
        self.branch = branch
        self.replica_count = replica_count

    def to_str(self):
        return """# Default values for tapdaas.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: %s

image:
  registry: %s
  repository: %s
  tag: %s
  pullPolicy: IfNotPresent

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext: {}
  # fsGroup: 2000

securityContext: {}
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

service:
  type: ClusterIP
  port: 80

resources: {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after resources:.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}""" % (
            str(self.replica_count),
            self.image_repo,
            self.branch,
            self.tag,
        )


class FeishuUserInfo:

    github_map_feishu = {
        "xbsura": ["gf9b5g97", "Berry Xiao(肖贝贝)"],
        "11000100111010101100111": ["83f9g4b5", "Gavin Xiao(肖家海)"],
        "32073955": ["d5c7691b", "Steven Shang(尚俊彦)"],
        "cn-xufei": ["dgagf6dd", "Feynman, 许飞"],
        "dobybros": ["2439724e", "Aplomb, 陈卓"],
        "dreamcoin1998": ["4gbb7gd2", "Jerry Gao(高俊斌)"],
        "FannieGirl": ["3gcc4517", "Fannie, 郑芳"],
        "HarsenLin": ["abc2bfb7", "Harsen, 林浩生"],
        "jackin-code": ["6c1aba4a", "Jackin, 黄佳钦"],
        "jarad0628": ["4f8fb847", "Jarad Geng(耿杰)"],
        "jiuyetx": ["9be9ggff", "Jacques Liu(刘佳鑫)"],
        "matthewxuda": ["99eb81be", "David, 徐大伟"],
        "MiracleYoung": ["d339cfb7", "Arthur Yang, 杨庆麟"],
        "ningmeng777": ["5b4a5274", "Lemon Liu(刘龙飞)"],
        "openlg": ["9a4gbdg4", "Leon, 李汝飞"],
        "philbert1978": ["eg3de2e6", "Philbert Pang(庞博)"],
        "ply0011": ["g8f61ed5", "Sam Pan(潘浪宇)"],
        "SoloJu": ["g7adbg82", "Will Zhu(朱玮)"],
        "StormKennen": ["a242dfd3", "Kennen, 梁智开"],
        "Tapdata-Jackie": ["g8f7e6b6", "Jackie Li(李志超)"],
        "test-lily": ["4eda2fbg", "Lily Teng(腾莉莉)"],
        "tjworks": ["97135422", "TJ Tang(唐建法)"],
        "zed1201": ["6b454b53", "Zed, 舒富"],
        "zerohyuan": ["3df65fa8", "Harvey, 凌河源"]
    }
    if os.getenv("FLASK_ENV") == "development":
        github_map_feishu.update({
            "dreamcoin1998": ["12ed962g", "Jerry Gao(高俊斌)"],
        })

    @classmethod
    def get_info_by_github_name(cls, github_name) -> (str, str, str):
        """
        通过github_name获取用户信息, 返回github用户名，飞书userid，飞书用户名
        :param github_name: github用户名
        :return:
        """
        if github_name not in cls.github_map_feishu.keys():
            return None, None, None
        return github_name, cls.github_map_feishu.get(github_name)[0], cls.github_map_feishu.get(github_name)[1]

    @classmethod
    def get_info_by_feishu_name(cls, feishu_name) -> (str, str, str):
        """
        通过feishu_name获取用户信息，返回github用户名，飞书userid，飞书用户名
        :param feishu_name: 飞书用户明称
        :return:
        """
        for k, v in cls.github_map_feishu.items():
            if feishu_name in v[1]:
                return k, v[0], v[1]
        return None, None, None

    @classmethod
    def get_info_by_feishu_userid(cls, feishu_userid):
        """
        通过feishu_userid获取用户信息，返回github用户名，飞书userid，飞书用户名
        :param feishu_userid: 飞书userid
        :return:
        """
        for k, v in cls.github_map_feishu.items():
            if feishu_userid == v[0]:
                return k, v[0], v[1]
        return None, None, None

    @classmethod
    def get_user_info(cls, q) -> (str, str, str):
        """
        获取用户信息，返回github用户名，飞书userid，飞书用户名
        :param q: github用户名 / 飞书userid / 飞书用户名
        :return:
        """
        github_name, feishu_userid, feishu_name = cls.get_info_by_github_name(q)
        if github_name and feishu_name and feishu_userid:
            return github_name, feishu_userid, feishu_name
        github_name, feishu_userid, feishu_name = cls.get_info_by_feishu_userid(q)
        if github_name and feishu_name and feishu_userid:
            return github_name, feishu_userid, feishu_name
        github_name, feishu_userid, feishu_name = cls.get_info_by_feishu_name(q)
        if github_name and feishu_name and feishu_userid:
            return github_name, feishu_userid, feishu_name
        return None, None, None
