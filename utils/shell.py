import subprocess

from .init import app
from . import exception


class Shell:
    """运行shell脚本"""

    @classmethod
    def execute(cls,  command, shell=True, stdout=subprocess.PIPE):
        """运行shell脚本 获取标准输出，错误，返回值"""
        # 打印日志
        app.logger.info(f"run shell command, command is {command}")
        # 运行命令
        process = subprocess.Popen(command, shell=shell, stdout=stdout)
        out, err = process.communicate()
        return_code = process.returncode
        # 解析return_code
        try:
            error_message = cls._return_code(return_code)
        except exception.BaseCustomException as e:
            error_message = e.message
        # 运行结果输出到日志
        app.logger.info(f"run shell command end, "
                        f"command is: {command}, "
                        f"output is: {out}, "
                        f"err is: {err}, "
                        f"return_code is: {return_code}, "
                        f"error_message is: {error_message}"
                        )
        return out, err, return_code

    @classmethod
    def run(cls, command, shell=True, stdout=subprocess.PIPE):
        """运行shell脚本 获取返回值"""
        # 打印日志
        app.logger.info(f"run shell command, command is {command}")
        # 运行命令
        process = subprocess.Popen(command, shell=shell, stdout=stdout)
        out, err = process.communicate()
        return_code = process.returncode
        # 解析return_code
        try:
            error_message = cls._return_code(return_code)
        except exception.BaseCustomException as e:
            error_message = e.message
        # 运行结果输出到日志
        app.logger.info(f"run shell command end, "
                        f"command is: {command}, "
                        f"output is: {out}, "
                        f"err is: {err}, "
                        f"return_code is: {return_code}, "
                        f"error_message is: {error_message}"
                        )
        return return_code

    @classmethod
    def _return_code(cls, return_code: int):
        """处理返回值"""
        if return_code == 0:
            return 0
        elif return_code == 1:
            raise exception.ShellCommandReturn1Error
        elif return_code == 2:
            raise exception.ShellCommandReturn2Error
        elif return_code == 126:
            raise exception.ShellCommandReturn126Error
        elif return_code == 127:
            raise exception.ShellCommandReturn127Error
        elif return_code == 128:
            raise exception.ShellCommandReturn128Error
        elif return_code == 130:
            raise exception.ShellCommandReturn130Error
        else:
            raise exception.ShellCommandErrorUnknown

