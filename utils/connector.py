"""
数据库连接器
"""
import os

import redis
import pymongo


class RedisConnector:

    _instance = None

    def __new__(cls, *args, **kwargs):
        if cls._instance is None:
            cls._instance = super(RedisConnector, cls).__new__(cls)
        return cls._instance

    def __init__(self, redis_connect_str):
        if redis_connect_str.startswith('redis://'):
            pool = redis.ConnectionPool.from_url(redis_connect_str, health_check_interval=10)
        else:
            host, port = redis_connect_str.split(":")
            pool = redis.ConnectionPool(host=host, port=int(port), health_check_interval=10)
        self._redis_cli = redis.Redis(connection_pool=pool)

    @property
    def redis_cli(self) -> redis.Redis:
        return self._redis_cli


class MongoConnector:
    """
    MongoDB连接器
    """
    def __init__(self, mongo_uri):
        """实例化mongodb连接器"""
        self._mongo_uri = mongo_uri
        # 实例化mongodb连接器实例
        self._mongo_cli = pymongo.MongoClient(self._mongo_uri)

    @property
    def mongo_cli(self) -> pymongo.MongoClient:
        """获取mongodb连接器实例"""
        return self._mongo_cli

    def insert_one(self, db_name, collection_name, data):
        """插入一条数据"""
        db = self._mongo_cli[db_name]
        collection = db[collection_name]
        collection.insert_one(data)

    def update_one(self, db_name: str, collection_name: str, filter: dict, update: dict):
        db = self._mongo_cli[db_name]
        collection = db[collection_name]
        collection.update_one(filter, update)

    def get(self, db_name, collection_name, query):
        """获取一条数据"""
        db = self._mongo_cli[db_name]
        collection = db[collection_name]
        return collection.find_one(query)

    def find_one(self, db_name, collection_name, filter):
        db = self._mongo_cli[db_name]
        collection = db[collection_name]
        return collection.find_one(filter)

    def get_many(self, db_name, collection_name, query):
        """获取所有数据"""
        db = self._mongo_cli[db_name]
        collection = db[collection_name]
        return collection.find(query)


redis_uri = os.getenv("REDIS")
redis_cli = RedisConnector(redis_uri).redis_cli
