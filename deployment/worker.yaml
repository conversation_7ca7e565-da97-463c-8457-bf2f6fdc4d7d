kind: Deployment
apiVersion: apps/v1
metadata:
  name: feishu-robot-worker
  namespace: cicd
  labels:
    app: feishu-robot-worker
spec:
  replicas: 1
  selector:
    matchLabels:
      app: feishu-robot-worker
  template:
    metadata:
      labels:
        app: feishu-robot-worker
    spec:
      imagePullSecrets:
        - name: dockerhub-qingcloud-credentials
      volumes:
        - name: feishu-robot-env
          configMap:
            name: feishu-robot-env
            items:
              - key: env
                path: .env
            defaultMode: 420
        - name: feishu-robot-gunicorn-config
          configMap:
            name: feishu-robot-gunicorn-config
            items:
              - key: gunicorn.conf.py
                path: gunicorn.conf.py
            defaultMode: 420
        - name: supervisor-config
          configMap:
            name: supervisor-config
            items:
              - key: worker.conf
                path: worker.conf
              - key: celery.conf
                path: celery.conf
              - key: celery-beat.conf
                path: celery-beat.conf
      containers:
        - name: feishu-robot-worker
          image: registry.cn-hongkong.aliyuncs.com/tapdata-inter/feishu-robot:--version--
          command:
            - bash
          args:
            - /home/<USER>/bin/docker-entrypoint.sh
          env:
            - name: WORKER_THREAD_COUNT
              value: '10'
            - name: ROLE
              value: WORKER
            - name: ALIYUN_KEY_ID
              value: LTAI5tRs7Bi4t4ngvH94uX17
            - name: ALIYUN_KEY_SECRET
              value: ******************************
            - name: AK
              value: 5IWVYC6OA687WXVJDPQD
            - name: END_POINT
              value: 'https://cci.ap-southeast-1.myhuaweicloud.com'
            - name: SK
              value: FtmEKNH4nFVRbkqCoF8Z4Akyj47CB9b7a3KDqkB0
          resources:
            limits:
              cpu: "500m"
              memory: 1Gi
              ephemeral-storage: 10Gi
            requests:
              cpu: "500m"
              memory: 1Gi
              ephemeral-storage: 10Gi
          volumeMounts:
            - name: feishu-robot-env
              mountPath: /home/<USER>/.env
              subPath: .env
            - name: feishu-robot-gunicorn-config
              mountPath: /home/<USER>/conf/gunicorn.conf.py
              subPath: gunicorn.conf.py
            - name: supervisor-config
              mountPath: /etc/supervisor/conf.d/
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: IfNotPresent
