---
apiVersion: v1
kind: ConfigMap
metadata:
  name: redis-conf
  namespace: cicd
data:
  conf: notify-keyspace-events Ex
  init: |-
    # 修改时区
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

    # 初始化redis.conf
    redis-server /usr/local/etc/redis/redis.conf
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: feishu-redis
  name: feishu-redis
  namespace: cicd
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: feishu-redis
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: feishu-redis
    spec:
      containers:
      - args:
        - /root/init.sh
        command:
        - /bin/bash
        image: redis
        imagePullPolicy: IfNotPresent
        name: feishu-redis
        ports:
        - containerPort: 6379
          name: http-0
          protocol: TCP
        resources:
          limits:
            cpu: 250m
            ephemeral-storage: 1Gi
            memory: 512Mi
          requests:
            cpu: 250m
            ephemeral-storage: 1Gi
            memory: 512Mi
        volumeMounts:
          - mountPath: /usr/local/etc/redis/redis.conf
            name: redis-conf
            subPath: redis.conf
          - mountPath: /root/init.sh
            name: redis-init
            readOnly: true
            subPath: init.sh
      restartPolicy: Always
      volumes:
      - configMap:
          defaultMode: 420
          items:
          - key: conf
            path: redis.conf
          name: redis-conf
        name: redis-conf
      - configMap:
          defaultMode: 420
          items:
          - key: init
            path: init.sh
          name: redis-conf
        name: redis-init
---
apiVersion: v1
kind: Service
metadata:
  labels:
    app: feishu-redis
  name: feishu-redis
  namespace: cicd
spec:
  allocateLoadBalancerNodePorts: true
  clusterIP: *************
  clusterIPs:
  - *************
  externalTrafficPolicy: Cluster
  internalTrafficPolicy: Cluster
  ipFamilyPolicy: SingleStack
  ports:
  - name: redis
    nodePort: 30679
    port: 30679
    protocol: TCP
    targetPort: 6379
  selector:
    app: feishu-redis
  sessionAffinity: None
  type: LoadBalancer