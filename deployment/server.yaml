kind: ConfigMap
apiVersion: v1
metadata:
  name: feishu-robot-env
  namespace: cicd
  labels:
    app: feishu-robot-env
data:
  env: |-
    # 飞书机器人密钥信息
    APP_ID=cli_a26d15bc2bf99013
    APP_SECRET=6JYz2K0sCV0rMmI6OkMkyhSgnaBZY7PA
    VERIFICATION_TOKEN=nF7nskio0eSn4wr4WBZjDg1eOZkfDvL5
    ENCRYPT_KEY=2UdnmBtJbuOCdygpMrM4lemvRE5Anm7M
    LARK_HOST=https://open.feishu.cn

    # Github
    TOKEN=****************************************
    OWNER=tapdata
    TRIGGER_REPO=tapdata-enterprise
    IMAGE_REPO=ghcr.io/tapdata

    # K8s 信息
    KUBESPHERE_USERNAME=admin
    KUBESPHERE_PASSWORD=Gotapd8!
    CLIENT_ID=kubesphere
    CLIENT_SECRET=kubesphere
    KUBESPHERE_BASE_URL=http://ks-apiserver.kubesphere-system
    NAMESPACE=dev
    NAMESPACE_CLOUD={"dev": "cloud-dev"}
    APP_NAME=tapdata
    APP_NAME_PRE=tapdata
    DEFAULT_IMAGE_REPO=ghcr.io/tapdata
    WORKSPACE=enterprise-dev
    K8S_APP_ID=app-1kqlvp8zp43kk3
    APP_VERSION=appv-j4y4r06j7v211n
    K8S_TOKEN=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************

    # Redis地址
    REDIS=10.43.169.120:30679
    CELERY_BROKER_URL=redis://default:Gotapd8!@10.43.169.120:30679/1
    CELERY_RESULT_BACKEND=redis://default:Gotapd8!@10.43.169.120:30679/2

    # 执行器并发数，可以同时执行多少任务
    CONCURRENT_COUNT=40
    # 定时任务执行器并发数，可以同时执行多少定时任务
    CRON_JOB_CONCURRENT_COUNT=5

    # 数据库名称
    MONGO_DATABASE=feishu_robot
    MONGO_URI=mongodb://root:Gotapd8!@*************:37017/feishu_robot?authSource=admin
    MONGO_SERVER=*************

    # 会话过期时间
    EXPIRE_TIME=120


    # 日志文件路径
    LOG_FILE_PATH=server.log

    FLASK_ENV=production

    # 飞书机器人名称
    FEISHU_ROBOT_NAME=打包构建小助手

    # 定时任务
    CRONJOB=[('Expire_issue @01 11 * * *', 'oc_75ad0cc0590b805483c5d156d047fc2a', 'group', '8eda5275-6c20-40b9-9de6-63fb93ddc042'),('更新一下飞书VIP用户任务表 @*/10 * * * *', 'ou_b05a61325c928af129539a4474c881b2', 'user', '8eda5275-6c20-40b9-9de6-63fb93ddc053'),('更新一下飞书用户任务表 @01 05 * * *', 'ou_b05a61325c928af129539a4474c881b2', 'user', '8eda5275-6c20-40b9-9de6-63fb93ddc053')]

    # Coding信息
    CODING_HOST=https://tapdata.coding.net
    CODING_TOKEN=f1fc0de7b844ff74cb692abf295fe45cecc9ded6
    CODING_PROJECT_NAME=tapdata

    # Mysql数据库信息
    SQLALCHEMY_DATABASE_URI=mysql+pymysql://root:Gotapd8!@*************:3306/feishu_robot?charset=utf8mb4
    SQLALCHEMY_TRACK_MODIFICATIONS=

    WEBHOOK_SECRET=HJXCJsyUEXIYir6JJwKRLXDat+HyjPTv1jBFmDnyMSM=

    # 云版环境tm链接，用来pdk注册数据源
    cloud_env_map={"cloud-dev": {"url": "http://*************:30104/console/tm/", "a": "c7179108bbd341a6af8afacaa63b563348c68f19695d48869b5eda725739c281", "ak": "XDgqGr4MY2w4W0IkK4UlaPMM5JbsvNuu", "sk": "7gNjiJy8vnuHjR27Gel9hnrkc7fPcwSj"}, "cloud-test": {"url": "http://************:30100/console/tm/", "a": "b36ca4e1e8a040609fd1f345e8e5b1e7cf27ec7fb4c349d29fdba298a9c78d67", "ak": "VEkQeQUbimtEWweBpLCxKhnKoDvVFOzV", "sk": "NSp4pvPCyTJOKPADMP4jBpARgQfBSOcl"}, "cloud-uat": {"url": "http://***********:30100/console/tm/", "ak": "gmA3K4lt3gG6VyXkwAfyLyYBubeUcucU", "sk": "ylKkPytK7R9834e7BPVbizGrDKroKSzS", "a": "da949282212f4fd7b11796cf2599f9a822a7826ee3e04ca9ac7e75e897fa0b9c"}, "cloud-prod": {"url": "https://cloud.tapdata.net/console/v3/tm", "a": "da949282212f4fd7b11796cf2599f9a822a7826ee3e04ca9ac7e75e897fa0b9c", "ak": "tCnHIpwY1KdPHFhFgiIMtkdXzU95dCdY", "sk": "nfM7BI8y30n42IkNrZWrO4IU4tHBxRAi"}, "huawei": {"url": "https://cloud.tapdata.io/tm/", "a": "da949282212f4fd7b11796cf2599f9a822a7826ee3e04ca9ac7e75e897fa0b9c", "ak": "NxgVcIL4sZGK2kLH9TZBqbb2XGvElEc3", "sk": "ilTuHCMOPwkZIt5Ub10zz4ub9BeGikmC"}, "gcp-prod": {"url": "http://cloud.tapdata.io/console/tm/", "ak": "tCnHIpwY1KdPHFhFgiIMtkdXzU95dCdY", "sk": "nfM7BI8y30n42IkNrZWrO4IU4tHBxRAi", "a": "da949282212f4fd7b11796cf2599f9a822a7826ee3e04ca9ac7e75e897fa0b9c"}}

    # 告警通知群id
    ALARM_GROUP_ID=oc_ff2a7d9a20c09cdb241fee0c250f5882

    WORKER_NAME=
---
kind: ConfigMap
apiVersion: v1
metadata:
  name: feishu-robot-gunicorn-config
  namespace: cicd
  labels:
    app: feishu-robot-gunicorn-config
data:
  gunicorn.conf.py: |-
    workers = 1  # 定义同时开启的处理请求的进程数量，根据网站流量适当调整
    threads = 1
    worker_class = "gevent"  # 采用gevent库，支持异步处理请求，提高吞吐量
    bind = "0.0.0.0:8000"
    accesslog = '/home/<USER>/gunicorn.access.log'
    access_log_format = '%(t)s %(s)s %(r)s %(q)s %(t)s %(p)s'
    errorlog = '/home/<USER>/gunicorn.error.log'
    loglevel = 'info'
    debug = True
    capture_output = True
    logconfig_dict = {
        'version': 1,
        'disable_existing_loggers': False,
        'formatters': {
            'console': {
                'format': '%(asctime)s - %(levelname)s - %(filename)s - %(funcName)s - %(lineno)s - %(message)s',
            },
        },
        'handlers': {
            'console': {
                'class': 'logging.StreamHandler',
                'formatter': 'console',
            },
        },
        'loggers': {
            'gunicorn': { # this was what I was missing, I kept using django and not seeing any server logs
                'level': 'INFO',
                'handlers': ['console'],
                'propagate': True,
            },
        },

    }
---
kind: ConfigMap
apiVersion: v1
metadata:
  name: supervisor-config
  namespace: cicd
  labels:
    app: supervisor-config
data:
  cronjob.conf: |-
    [program:cronjob]
    autorestart=True
    autostart=True
    redirect_stderr=True
    command=bash bin/start.sh CRONJOB
    user=root
    directory=/home/<USER>/
    stdout_logfile_maxbytes = 20MB
    stdout_logfile_backups = 20
    stdout_logfile = /home/<USER>/log/cronjob.log
  demo.conf: |-
    [program:demo]
    autorestart=True
    autostart=True
    redirect_stderr=True
    command=python scripts/demo.py
    user=root
    directory=/home/<USER>/
    stdout_logfile_maxbytes = 20MB
    stdout_logfile_backups = 20
    stdout_logfile = /home/<USER>/log/demo.log
  nginx.conf: |-
    [program:nginx]
    autorestart=True
    autostart=True
    redirect_stderr=True
    command=nginx -g "daemon off;"
    user=root
    directory=/home/<USER>/
    stdout_logfile_maxbytes = 20MB
    stdout_logfile_backups = 20
    stdout_logfile = /home/<USER>/log/nginx.log
  server.conf: |-
    [program:server]
    autorestart=True
    autostart=True
    redirect_stderr=True
    command=bash bin/start.sh SERVER
    user=root
    directory=/home/<USER>/
    stdout_logfile_maxbytes = 20MB
    stdout_logfile_backups = 20
    stdout_logfile = /home/<USER>/log/server.log
  worker.conf: |-
    [program:worker]
    autorestart=True
    autostart=True
    redirect_stderr=True
    command=bash bin/start.sh WORKER
    user=root
    directory=/home/<USER>/
    stdout_logfile_maxbytes = 20MB
    stdout_logfile_backups = 20
    stdout_logfile = /home/<USER>/log/worker.log
  celery.conf: |-
    [program:celery]
    autorestart=True
    autostart=True
    redirect_stderr=True
    command=celery -A server.celery worker -l info
    user=root
    directory=/home/<USER>/
    stdout_logfile_maxbytes = 20MB
    stdout_logfile_backups = 20
    stdout_logfile = /home/<USER>/log/celery.log
  celery-beat.conf: |-
    [program:celery-beat]
    autorestart=True
    autostart=True
    redirect_stderr=True
    command=celery -A server.celery beat -l info
    user=root
    directory=/home/<USER>/
    stdout_logfile_maxbytes = 20MB
    stdout_logfile_backups = 20
    stdout_logfile = /home/<USER>/log/celery-beat.log
---
kind: Deployment
apiVersion: apps/v1
metadata:
  name: feishu-robot-server
  namespace: cicd
  labels:
    app: feishu-robot-server
spec:
  replicas: 1
  selector:
    matchLabels:
      app: feishu-robot-server
  template:
    metadata:
      labels:
        app: feishu-robot-server
    spec:
      volumes:
        - name: feishu-robot-env
          configMap:
            name: feishu-robot-env
            items:
              - key: env
                path: .env
            defaultMode: 420
        - name: feishu-robot-gunicorn-config
          configMap:
            name: feishu-robot-gunicorn-config
            items:
            - key: gunicorn.conf.py
              path: gunicorn.conf.py
            defaultMode: 420
        - name: supervisor-config
          configMap:
            name: supervisor-config
            items:
              - key: cronjob.conf
                path: cronjob.conf
              - key: demo.conf
                path: demo.conf
              - key: nginx.conf
                path: nginx.conf
              - key: server.conf
                path: server.conf
      containers:
      - name: feishu-robot-server
        image: registry.cn-hongkong.aliyuncs.com/tapdata-inter/feishu-robot:--version--
        ports:
        - containerPort: 8000
          name: http
          protocol: TCP
        - containerPort: 80
          name: nginx
          protocol: TCP
        command:
          - bash
        args:
          - /home/<USER>/bin/docker-entrypoint.sh
        env:
          - name: ROLE
            value: ALL
          - name: ALIYUN_KEY_ID
            value: LTAI5tRs7Bi4t4ngvH94uX17
          - name: ALIYUN_KEY_SECRET
            value: ******************************
          - name: AK
            value: 5IWVYC6OA687WXVJDPQD
          - name: END_POINT
            value: 'https://cci.ap-southeast-1.myhuaweicloud.com'
          - name: SK
            value: FtmEKNH4nFVRbkqCoF8Z4Akyj47CB9b7a3KDqkB0
        resources:
          limits:
            cpu: "1"
            memory: 2Gi
            ephemeral-storage: 10Gi
          requests:
            cpu: "1"
            memory: 2Gi
            ephemeral-storage: 10Gi
        livenessProbe:
          httpGet:
            path: /healthy
            port: 8000
            scheme: HTTP
          initialDelaySeconds: 30
          timeoutSeconds: 1
          periodSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /healthy
            port: 8000
            scheme: HTTP
          initialDelaySeconds: 30
          timeoutSeconds: 1
          periodSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        startupProbe:
          httpGet:
            path: /healthy
            port: 8000
            scheme: HTTP
          initialDelaySeconds: 30
          timeoutSeconds: 1
          periodSeconds: 10
          successThreshold: 1
          failureThreshold: 3
        volumeMounts:
          - name: feishu-robot-env
            mountPath: /home/<USER>/.env
            subPath: .env
          - name: feishu-robot-gunicorn-config
            mountPath: /home/<USER>/conf/gunicorn.conf.py
            subPath: gunicorn.conf.py
          - name: supervisor-config
            mountPath: /etc/supervisor/conf.d/
---
kind: Service
apiVersion: v1
metadata:
  name: feishu-robot-server
  namespace: cicd
  labels:
    app: feishu-robot-server
spec:
  externalTrafficPolicy: Cluster
  ports:
    - port: 30009
      nodePort: 30009
      targetPort: 8000
      protocol: TCP
      name: feishu-robot-backend
    - port: 30008
      nodePort: 30008
      targetPort: 80
      protocol: TCP
      name: nginx
  selector:
    app: feishu-robot-server
  type: NodePort