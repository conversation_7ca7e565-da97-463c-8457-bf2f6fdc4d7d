FROM node:16.20.2 as build-stage

WORKDIR /app/frontend
COPY ./frontend/package*.json ./
RUN npm install
COPY ./frontend/ ./
RUN npm run build

FROM python:3.8

WORKDIR /home/<USER>
COPY --from=build-stage /app/frontend/build /var/www
COPY conf/root/ /root/
COPY conf/usr/local/bin/ /usr/local/bin/
COPY conf/qingcloud/ /conf/qingcloud/
COPY conf/gcp/ /conf/gcp/
COPY conf/huaweiCloud/ /conf/huaweiCloud/
COPY conf/alicloud/ /conf/alicloud/

#If we add the requirements and install dependencies first, docker can use cache if requirements don't change
ADD . /home/<USER>
RUN apt update && \
    apt-get install -y ca-certificates curl gnupg jq && \
    curl -LO "https://dl.k8s.io/release/v1.19.0/bin/linux/amd64/kubectl" && \
    curl -LO "https://dl.k8s.io/v1.19.0/bin/linux/amd64/kubectl.sha256" && \
    echo "$(cat kubectl.sha256) kubectl" | sha256sum --check && \
    install -o root -g root -m 0755 kubectl /usr/local/bin/kubectl && \
    chmod +x /usr/local/bin/cci-iam-authenticator  && \
    pip install poetry && \
    poetry export -f requirements.txt --output requirements.txt --without-hashes && \
    mkdir -p log/server/ && \
    pip install --no-cache-dir -r requirements.txt && \
    chmod 0600 /home/<USER>/conf/public.pem && \
    echo "StrictHostKeyChecking no" >> /etc/ssh/ssh_config && \
    install -m 0755 -d /etc/apt/keyrings && \
    curl -fsSL https://download.docker.com/linux/debian/gpg | gpg --dearmor -o /etc/apt/keyrings/docker.gpg && \
    chmod a+r /etc/apt/keyrings/docker.gpg && \
    echo \
    "deb [arch="$(dpkg --print-architecture)" signed-by=/etc/apt/keyrings/docker.gpg] https://download.docker.com/linux/debian \
    "$(. /etc/os-release && echo "$VERSION_CODENAME")" stable" | \
    tee /etc/apt/sources.list.d/docker.list > /dev/null && \
    apt-get update && \
    apt-get install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin && \
    chmod +x /usr/local/bin/ossutil && \
    cp /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
    chmod +x /home/<USER>/bin/start.sh
# 安装gke-gcloud-auth-plugin
RUN echo "deb [signed-by=/usr/share/keyrings/cloud.google.gpg] https://packages.cloud.google.com/apt cloud-sdk main" | tee -a /etc/apt/sources.list.d/google-cloud-sdk.list && \
    curl -s https://packages.cloud.google.com/apt/doc/apt-key.gpg | apt-key --keyring /usr/share/keyrings/cloud.google.gpg add - && \
    apt-get update && apt-get install -y google-cloud-cli && \
    apt-get install -y google-cloud-cli-gke-gcloud-auth-plugin

RUN apt-get install -y nginx && \
    rm -rf /etc/nginx/sites-enabled/default

# 安装supervisor用于自动拉起进程
RUN apt-get install -y supervisor
COPY conf/etc/supervisor/supervisord.conf /etc/supervisor/supervisord.conf
RUN chmod +x bin/docker-entrypoint.sh

# 将 Nginx 配置文件复制到容器中
COPY nginx.conf /etc/nginx/conf.d/robot.conf

EXPOSE 8000
EXPOSE 80
