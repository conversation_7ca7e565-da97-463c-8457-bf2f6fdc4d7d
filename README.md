# 飞书机器人

[TOC]

## 说明

Tapdata 开源版/企业版/云版CICD流程触发入口。

## 如何启动

### 如何测试

1. 修改.env文件
```python
WORKER_NAME=[worker_name]  # 标志worker的名称，用于区分不同的worker
REDIS=139.198.105.8:30218
```

2. 启动worker
```shell
python worker.py
```

3. 在小助手设置
```shell
setup use_worker=[worker_name]  # 这个跟上面的worker_name一样
```

4. 调试完，在小助手清除设置
```shell
setup use_worker=  # 为空
```

### 本地启动

1. 启动服务端，启动终端
```shell
python bin/start.py SERVER 或
python server.py
```

2. 启动Worker进程，切换到另一个终端
```shell
python bin/start.py WORKER 或
python worker.py
```

3. 启动定时任务，切换到另一个终端（可选）
```shell
python bin/start.py CRONJOB 或
python cronjob.py
```

## 模块结构

![docs/images/架构图简单说明.png](img.png)

## 文件结构

- handler: 命令处理文件，一个命令对应一个文件
- lib: 飞书机器人sdk
- model: 数据库定义
- templates: k8s各种模版，用来启动任务
- utils: 各种工具
  - api: Api请求，Github/K8s/Coding
  - common: 通用工具类
  - connector: 连接器，负责redis连接
  - datastruct: 特定的数据结构
  - exception: 自定义错误
  - handler: 命令处理基类
  - init: 初始化flask app
  - message_card: 消息卡片
  - schedule: 调度器
  - session: 缓存

### 调度器

调度器，主要有以下功能：

- 初始化工作线程，启动定时任务
- 注册任务到redis
- 优雅退出处理

### Worker线程

Worker线程：

- 处理任务逻辑
- 更新任务状态
- 命令语义化解析

## 开发

### 添加任务

根目录下，执行以下命令可以在handler自动生成代码模版，只需要在模版中填写代码即可
```shell
python bin/add_command.py [命令]
```

### Handler

具体处理命令的逻辑。位于`utils/handler.py`文件`Handler`类，主要包含以下几个关键的属性：

- check_template: 命令合法化检查模版
- command: 定义命令
- help: 帮助信息
- message_type: 返回信息类型，`text`为文本类型，`message_card`为消息卡片类型
- except_cache: 是否将命令加入缓存，加快命令响应速度
- output: 管道支持，将output指定的输出传递到下一条命令的输入
- magic_arg: 获取如`build namespace=dev`格式的命令，magic_arg获取到namespace的值

`Handler`类需要实现`handle`方法，用来返回`message_type`所指定的输出类型，即向飞书指定用户响应/发送文本或消息卡片。

### 定时任务

添加定时任务需要修改根目录下修改.env文件中CRONJOB这一项：

```
CRONJOB=[('listVisual issue 59 @0 13 * * *', 'oc_ce0758634ae9abbfdc963f99ef8a650b', 'group', '8eda5585-6c20-40b9-9de6-63fb93ddc042'), ('云版自动更新 @0 04 * * *', 'ou_abe8ff4c52861bd9312a52fede1b54c7', 'p2p', 'cbe3fdc0-bde2-481a-9ce9-5d6e26c21b61')]
```

元组的四个项分别是`命令`，`发送的群号/UID`，`group/user`，`id`:

- 命令格式：以@作为标志将命令与cron语句分开，cron语句支持以下两种格式
  - 1：linux crontab模式，如 "* * * * *"
  - 2：以定时任务加载完毕为基准时间，"1m" 为每分钟，"1h"为每小时，"1s"为每秒钟，"1d"为每天
- 发送的群号/用户ID：将要把信息发送给哪个群/哪个人
- group/user：对应前一项，群即为group，用户ID即为user
- id：此ID会存入数据库，由此查询执行记录和历史
