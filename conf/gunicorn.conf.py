workers = 2    # 定义同时开启的处理请求的进程数量，根据网站流量适当调整
threads = 5
worker_class = "gevent"   # 采用gevent库，支持异步处理请求，提高吞吐量
bind = "0.0.0.0:8000"
accesslog = '/home/<USER>/gunicorn.access.log'
access_log_format = '%(t)s %(s)s %(r)s %(q)s %(t)s %(p)s'
errorlog = '/home/<USER>/gunicorn.error.log'
loglevel = 'info'
debug = True
capture_output = True
logconfig_dict = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'console': {
            'format': '%(asctime)s - %(levelname)s - %(filename)s - %(funcName)s - %(lineno)s - %(message)s',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'console',
        },
    },
    'loggers': {
        'gunicorn': { # this was what I was missing, I kept using django and not seeing any server logs
            'level': 'INFO',
            'handlers': ['console'],
            'propagate': True,
        },
    },

}
