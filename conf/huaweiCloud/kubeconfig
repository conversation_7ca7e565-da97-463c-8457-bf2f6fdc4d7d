apiVersion: v1
clusters:
- cluster:
    insecure-skip-tls-verify: true
    server: https://cci.ap-southeast-1.myhuaweicloud.com
  name: cci-cluster-ap-southeast-1
contexts:
- context:
    cluster: cci-cluster-ap-southeast-1
    user: cci-user-ap-southeast-1-5IWVYC6OA687WXVJDPQD
  name: cci-context-ap-southeast-1-5IWVYC6OA687WXVJDPQD
current-context: cci-context-ap-southeast-1-5IWVYC6OA687WXVJDPQD
kind: Config
preferences: {}
users:
- name: cci-user-ap-southeast-1-5IWVYC6OA687WXVJDPQD
  user:
    exec:
      apiVersion: client.authentication.k8s.io/v1beta1
      args:
      - token
      - --iam-endpoint=https://iam.myhuaweicloud.com
      - --insecure-skip-tls-verify=true
      - --cache=true
      - --token-only=false
      - --project-name=ap-southeast-1
      - --ak=5IWVYC6OA687WXVJDPQD
      - --sk=FtmEKNH4nFVRbkqCoF8Z4Akyj47CB9b7a3KDqkB0
      command: cci-iam-authenticator
      env: []