#!/bin/bash
# 监听k8s的deployment是否成功更新

# 默认值
deployment_name=""
namespace="default"
max_attempts=24

sleep 30

# 解析命令行参数
while getopts ":d:n:" opt; do
  case $opt in
    d)
      deployment_name="$OPTARG"
      ;;
    n)
      namespace="$OPTARG"
      ;;
    \?)
      echo "Invalid option: -$OPTARG" >&2
      exit 1
      ;;
    :)
      echo "Option -$OPTARG requires an argument." >&2
      exit 1
      ;;
  esac
done

if [ -z "$deployment_name" ]; then
  echo "Usage: $0 -d <deployment_name> [-n <namespace>]"
  exit 1
fi

attempt=1
while [ $attempt -le $max_attempts ]; do
  echo "Attempt $attempt of $max_attempts: Checking deployment status..."

  replicas=$(kubectl get deployment $deployment_name -n $namespace -o jsonpath='{.spec.replicas}')
  updated_replicas=$(kubectl get deployment $deployment_name -n $namespace -o jsonpath='{.status.updatedReplicas}')
  available_replicas=$(kubectl get deployment $deployment_name -n $namespace -o jsonpath='{.status.availableReplicas}')

  if [ "$replicas" == "$updated_replicas" ] && [ "$replicas" == "$available_replicas" ]; then
    echo "All replicas have been updated and are available."
    exit 0
  else
    sleep 5
    ((attempt++))
  fi
done

echo "Not all replicas have been updated and are available."
exit 1
