#!/usr/bin/env bash

WORK_DIR=$(pwd)
SAE_HOME=$(cd "$(dirname "$0")/" && pwd)

ALIYUN_CMD="aliyun"
PROFILE="grayscale"
VERSION=
COMPONENTS_PARM="tcm,tm,tm-java,console"

function check_env() {
  which aliyun > /dev/null
  if [[ $? -ne 0 ]]; then
    echo "aliyun cli not install"
    wget https://github.com/aliyun/aliyun-cli/releases/download/v3.0.165/aliyun-cli-linux-3.0.165-amd64.tgz
    tar -xvf aliyun-cli-linux-3.0.165-amd64.tgz -C /usr/local/bin/
    rm -rf aliyun-cli-linux-3.0.165-amd64.tgz
    ALIYUN_CMD="/usr/local/bin/aliyun"
    $ALIYUN_CMD configure set --profile config.json --region cn-beijing --language zh --access-key-id LTAI5tRs7Bi4t4ngvH94uX17 --access-key-secret ******************************
  fi
}

check_env

function usage() {
  echo "Usage:"
  echo "  upgrade.sh [-p profile] [-v version] [-P product] [-c components] [-u true/false] [-d true/false]"
  echo ""
  echo "Description:"
  echo "    -p profile: the runtime environment. Can be one of the following: prod, grayscale, hw_prod, hw_gray."
  echo "    -v version: build version, default is the tag name of current git branch."
  echo "    -c components: build on demand: tcm, tm, tm-java, console; use commas to separate multiple component name, example -m tcm,tm; default for build all."
  echo ""
  exit 0
}

while getopts 'p:v:c:h' OPT; do
  case $OPT in
  p) PROFILE=$(echo "$OPTARG" | tr "[A-Z]" "[a-z]") ;;
  v) VERSION="$OPTARG" ;;
  c) COMPONENTS_PARM="$OPTARG" ;;
  h) usage ;;
  ?) usage ;;
  esac
done

PROFILE="${PROFILE:=grayscale}"
COMPONENTS_PARM="${COMPONENTS_PARM:='tcm,tm,tm-java,console'}"

if [[ "$VERSION" == "" ]]; then
  echo "Version can't be empty."
  exit 1
fi

IFS=" " read -r -a COMPONENTS <<<"$(echo "$COMPONENTS_PARM" | tr -d ' ' | tr ',' ' ')"

cat <<_END_
Worker directory:              $WORK_DIR
SAE home directory:            $SAE_HOME
Aliyun CLI:                    $ALIYUN_CMD

Version:                       $VERSION
Profile:                       $PROFILE
Components:                    $COMPONENTS_PARM

_END_

# tcm,tm,tm-java,console
APPS=("GATEWAY-Prod:2f574993-7fab-41aa-93df-6bc93ab0cd1d"

      "CONSOLE-Prod3-grayscale:a69a23a8-a6ef-4a4e-a88b-a3d147fddb61"
      "TCM-Prod3-grayscale:d846d71d-7db6-4867-84ac-78b4929d7438"
      "TM-Java-Prod3-grayscale:f45d4f61-ee03-4502-944d-b0da6bd8f357"
      "TM-Prod3-grayscale:ee7ceee1-2245-402e-a219-39eab152431a"

      "CONSOLE-Prod3:04140766-2bc6-45a5-b55e-fefbee168328"
      "TM-Java-Prod3:e116ee21-2689-48a9-83fa-87767ef8d522"
      "TM-Prod3:db8b74f3-c4fa-4f39-859b-6ccbc166d5f7"
      "TCM-Prod3:b69a2111-1470-42c2-a791-e2746a77a55a"

      "TEST-prod:e31c2f9e-5a81-4539-8bee-39eb82ade884")

APP_NAME_MAPPING=("tcm:TCM"
                  "tm:TM"
                  "tm-java:TM-Java"
                  "console:CONSOLE"
                  "gateway:GATEWAY")

# 安装华为云CCI支持的kubeconfig
function init_hw_cloud_cci() {
  # install kubeconfig
  which kubectl > /dev/null
  if [[ $? -ne 0 ]]; then
    echo "kubectl not install"
    exit 1
  fi
  # switch kubeconfig to cci
  kubectl config view | grep cci-context-ap-southeast-1-5IWVYC6OA687WXVJDPQD > /dev/null
  if [[ $? -ne 0 ]]; then
    chmod +x /usr/local/bin/cci-iam-authenticator
    cci-iam-authenticator generate-kubeconfig --cci-endpoint=$END_POINT --sk=$SK --ak=$AK
  fi
}

function appId() {
  # example:
  #  _appId=$(appId "TM-Java-Prod")
  #  echo "$_appId"
  APP_NAME="$1"

  for APP in "${APP_NAME_MAPPING[@]}" ; do
      KEY="${APP%%:*}"
      VALUE="${APP##*:}"

      if [[ "$KEY" == "$APP_NAME" ]]; then
        APP_NAME="$VALUE"
      fi
      #printf "%s likes to %s.\n" "$KEY" "$VALUE"
  done

  FULL_APP_NAME="$APP_NAME"
  if [[ "$PROFILE" == "prod" ]]; then
    FULL_APP_NAME="$APP_NAME-Prod3"
  elif [[ "$PROFILE" == "grayscale" ]]; then
    FULL_APP_NAME="$APP_NAME-Prod3-grayscale"
  fi

  #echo "$APP_NAME"

  for APP in "${APPS[@]}" ; do
      KEY="${APP%%:*}"
      VALUE="${APP##*:}"

      if [[ "$KEY" == "$FULL_APP_NAME" || "$KEY" == "$APP_NAME" ]]; then
        echo "$VALUE"
        exit 0
      fi
      #printf "%s likes to %s.\n" "$KEY" "$VALUE"
  done

}

function upgrade_aliyun() {
  for COMPONENT in "${COMPONENTS[@]}"; do
    _APP_ID="$(appId "$COMPONENT")"

    if [[ "$_APP_ID" != "" ]]; then

      echo "Upgrade component $COMPONENT ($_APP_ID)"
      IMAGE="registry-vpc.cn-beijing.aliyuncs.com/tapdata/dfs-$COMPONENT:$VERSION"
      ARGS=""
      if [[ "$COMPONENT" == "tm" && $PROFILE == "prod" ]]; then
        COMMAND="java"
        ARGS="['-jar','-server','-Xms6g','-Xmx14g','-Xmn4g','-XX:CompressedClassSpaceSize=1024m','-XX:MetaspaceSize=512m','-XX:MaxMetaspaceSize=1024m','-XX:+UseConcMarkSweepGC','-XX:+CMSScavengeBeforeRemark','-XX:+HeapDumpOnOutOfMemoryError','-XX:HeapDumpPath=/opt/tm/logs/','"/opt/tm/lib/tm-$VERSION.jar"','--spring.config.additional-location=file:./conf/','--logging.config=file:./conf/logback.xml']"
      elif [[ "$COMPONENT" == "tm" && $PROFILE == "grayscale" ]]; then
        COMMAND="java"
        ARGS="['-jar','-server','-Xms4g','-Xmx8g','-Xmn2g','-XX:CompressedClassSpaceSize=1024m','-XX:MetaspaceSize=512m','-XX:MaxMetaspaceSize=1024m','-XX:+UseConcMarkSweepGC','-XX:+CMSScavengeBeforeRemark','-XX:+HeapDumpOnOutOfMemoryError','-XX:HeapDumpPath=/opt/tm/logs/','"/opt/tm/lib/tm-$VERSION.jar"','--spring.config.additional-location=file:./conf/','--logging.config=file:./conf/logback.xml']"
      fi
      if [[ "$COMPONENT" == "tm" ]]; then
        IMAGE="registry-vpc.cn-beijing.aliyuncs.com/tapdata/dfs-$COMPONENT-java:$VERSION"
        "$ALIYUN_CMD" sae DeployApplication --AppId="$_APP_ID" --ImageUrl="$IMAGE" --Command="$COMMAND" --CommandArgs="$ARGS"
      else
        "$ALIYUN_CMD" sae DeployApplication --AppId="$_APP_ID" --ImageUrl="$IMAGE"
      fi
    fi
  done
}

function upgrade_huawei_cloud() {
  # get namespace
  if [[ $PROFILE == "hw_prod" ]]; then
    namespace="prod"
  else
    namespace="gray"
  fi
  for COMPONENT in "${COMPONENTS[@]}"; do
    echo "Upgrade component $COMPONENT"
    if [[ $COMPONENT == "tm" ]]; then
      IMAGE="swr.ap-southeast-1.myhuaweicloud.com/tapdata/dfs-$COMPONENT-java:$VERSION"
      COMMAND='["java","-jar","-server","-Xms3g","-Xmx7g","-Xmn2g","-XX:CompressedClassSpaceSize=1024m","-XX:MetaspaceSize=512m","-XX:MaxMetaspaceSize=1024m","-XX:+UseConcMarkSweepGC","-XX:+CMSScavengeBeforeRemark","-XX:+HeapDumpOnOutOfMemoryError","-XX:HeapDumpPath=/opt/tm/logs/","/opt/tm/lib/tm-'$VERSION'.jar","--spring.config.additional-location=file:./conf/","--logging.config=file:./conf/logback.xml"]'
      kubectl patch deployment tm-prod3 -n gray -p '{"spec": {"template": {"spec": {"containers": [{"name": "tm-prod3", "image": "'"${IMAGE}"'", "command": '"${COMMAND}"'}]}}}}}'
    else
      IMAGE="swr.ap-southeast-1.myhuaweicloud.com/tapdata/dfs-$COMPONENT:$VERSION"
      kubectl set image deployment $COMPONENT-prod3 $COMPONENT-prod3=$IMAGE -n $namespace
    fi
  done
}

function upgrade() {
  if [[ $PROFILE == "hw_prod" || $PROFILE == "hw_gray" ]]; then
    init_hw_cloud_cci
    upgrade_huawei_cloud
  else
    upgrade_aliyun
  fi
}

upgrade