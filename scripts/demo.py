import pymysql
import pymongo
import random
import time

# MySQL数据库连接配置
db_host = '*************'
db_user = 'root'
db_password = 'Gotapd8!'
db_name = 'DEMO'

# MongoDB数据库连接配置
mongo_uri = '*******************************************************'

# 连接MySQL数据库
conn = pymysql.connect(host=db_host, port=3306, user=db_user, password=db_password, database=db_name)
cursor = conn.cursor()

# 连接MongoDB数据库
mongo_client = pymongo.MongoClient(mongo_uri)
mongo_db = mongo_client['DEMO']

# 持续写入和更新数据
while True:
    # 如果当前时间是23:59:00,重新创建INSURANCE和SALES表
    if time.strftime('%H:%M:%S', time.localtime(time.time())) == '17:23:00':
        # 清空源表
        cursor.execute('DROP TABLE IF EXISTS INSURANCE')
        cursor.execute('DROP TABLE IF EXISTS SALES')
        cursor.execute('''
        CREATE TABLE INSURANCE (
        id INT AUTO_INCREMENT PRIMARY KEY ,
        policy_number VARCHAR(255),
        customer_name VARCHAR(255),
        insurance_type VARCHAR(255)
        );
        ''')
        cursor.execute('''
        CREATE TABLE SALES (
        id INT AUTO_INCREMENT PRIMARY KEY ,
        product_name VARCHAR(255),
        price DECIMAL(10, 2),
        salesperson_name VARCHAR(255)
        );
        ''')
        print("源表INSURANCE和SALES已经重新创建！")
        # 清空目标表
        mongo_db['INSURANCE'].drop()
        mongo_db['SALES'].drop()
        print("目标表INSURANCE和SALES已经重新创建！")
    # 生成随机数据
    policy_number = 'POL' + str(random.randint(1, 1000))
    customer_name = 'Customer' + str(random.randint(1, 1000))
    insurance_type = random.choice(['Health', 'Auto', 'Life'])

    product_name = 'Product' + random.choice([' A', ' B', ' C'])
    price = round(random.uniform(50.0, 500.0), 2)
    salesperson_name = 'Salesperson' + str(random.randint(1, 100))

    # 执行插入数据的SQL语句
    insert_insurance_sql = f"INSERT INTO INSURANCE (policy_number, customer_name, insurance_type) VALUES ('{policy_number}', '{customer_name}', '{insurance_type}')"
    insert_sales_sql = f"INSERT INTO SALES (product_name, price, salesperson_name) VALUES ('{product_name}', {price}, '{salesperson_name}')"

    try:
        # 执行SQL语句
        cursor.execute(insert_insurance_sql)
        cursor.execute(insert_sales_sql)

        # 提交事务
        conn.commit()

        print("数据插入成功！")

    except Exception as e:
        # 发生错误时回滚
        conn.rollback()
        print(f"数据插入失败: {e}")

    # 休眠1秒，模拟持续写入和更新
    time.sleep(1)

# 关闭数据库连接
conn.close()
