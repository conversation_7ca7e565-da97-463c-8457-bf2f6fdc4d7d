import React from "react";
import '../../../../App.css';
import './index.css';
import {Card, ConfigProvider, message, Steps} from "antd";
import {LoadingOutlined} from "@ant-design/icons";
import instance from "../../../../request";

class PackageResult extends React.Component {

  state = {
    selectedPackage: [],
    packageColumns: [
      {
        title: '版本号',
        dataIndex: 'version',
        key: 'version',
        width: 10,
      },
      {
        title: '大小',
        dataIndex: 'size',
        key: 'size',
        width: 5,
      },
      {
        title: '包名',
        dataIndex: 'name',
        key: 'name',
        width: 65,
      },
      {
        title: '创建时间',
        dataIndex: 'created',
        key: 'created',
        width: 15,
      }
    ],
    selectedConnectors: [],
    connectorsColumns: [{
      title: '连接器名称',
      dataIndex: 'name',
      key: 'name',
    },{
      title: '大小',
      dataIndex: 'size',
      key: 'size',
    },{
      title: '创建时间',
      dataIndex: 'created',
      key: 'created'
    }],
    workflowId: "",
    shouldContinue: true,
    steps: [],
    downloadLink: "",
    downloadText: "",
    mongoConfig: null,
    isLite: false,
  }

  checkStatus() {
    if (!this.state.shouldContinue || this.state.workflowId === "") {
      return;
    }

    return instance.get("/api/artifact/result?workflow_id=" + this.state.workflowId).then(r => {
      if (r.data.code === 0) {
        console.log(r.data.data.jobs[r.data.data.jobs.length - 1])
        if (r.data.data.jobs[r.data.data.jobs.length - 1].status === "completed") {
          this.setState({
            shouldContinue: false
          })
          const packageName = this.state.selectedPackage.name
          console.log("构建完成，下载链接：http://58.251.34.123:5244/artifacts/" + packageName)
          if (r.data.data.jobs[r.data.data.jobs.length - 1].conclusion === "success") {
            const baseUrl = this.state.isLite ? "http://58.251.34.123:5244/lite/" : "http://58.251.34.123:5244/artifacts/"
            this.setState({
              downloadText: "构建完成，下载链接：",
              downloadLink: baseUrl + packageName
            })
          }
        }
        console.log("运行")
        const steps = []
        for (let i=0; i < r.data.data.jobs[r.data.data.jobs.length - 1].steps.length; i++) {
          console.log(r.data.data.jobs[r.data.data.jobs.length - 1].steps[i])
          switch (r.data.data.jobs[r.data.data.jobs.length - 1].steps[i].status) {
            case "in_progress": {
              steps.push({
                title: r.data.data.jobs[r.data.data.jobs.length - 1].steps[i].name,
                icon: <LoadingOutlined/>,
                status: "process",
              })
              break
            }
            case "completed": {
              if (r.data.data.jobs[r.data.data.jobs.length - 1].steps[i].conclusion === "failure" || r.data.data.jobs[r.data.data.jobs.length - 1].steps[i].conclusion === 'cancelled' || r.data.data.jobs[r.data.data.jobs.length - 1].steps[i].conclusion === 'skipped') {
                steps.push({
                  title: r.data.data.jobs[r.data.data.jobs.length - 1].steps[i].name,
                  status: "error",
                })
              } else {
                steps.push({
                  title: r.data.data.jobs[r.data.data.jobs.length - 1].steps[i].name,
                  status: "finish",
                })
              }
              break
            }
            case "pending": {
              steps.push({
                title: r.data.data.jobs[r.data.data.jobs.length - 1].steps[i].name,
                status: "wait",
              })
              break
            }
            default: {
              steps.push({
                title: r.data.data.jobs[r.data.data.jobs.length - 1].steps[i].name,
                status: "error",
              })
              break
            }
          }
        }
        this.setState({
          steps: steps
        })
        console.log(steps)
      } else {
        console.log("失败")
        this.setState({
          shouldContinue: false
        })
      }

      // Use setTimeout to avoid stack overflow and to delay the next check
      setTimeout(this.checkStatus.bind(this), 10000);  // Check status again after 10 seconds
    })
  }

  componentDidMount() {
    const search = window.location.search;
    const params = new URLSearchParams(search);
    const workflowId = params.get("workflow_id") || ""
    const packageInfo = JSON.parse(params.get("info") || JSON.stringify([]))
    const connectors = JSON.parse(params.get("connectors") || JSON.stringify([]))
    const mongoConfig = JSON.parse(params.get("mongo_config") || JSON.stringify({ includeMongoDB: false }))
    const isLite = params.get("is_lite") === "true"
    if (workflowId === "") {
      message.error('workflow_id is not null')
    } else {
      this.setState({
        workflowId: workflowId,
        selectedPackage: packageInfo[0],
        selectedConnectors: connectors,
        mongoConfig: mongoConfig,
        isLite: isLite,
      }, () => {
        this.checkStatus.bind(this)()
      })
    }
  }

  render() {

    const selectedConnectors = this.state.selectedConnectors.map((item) => {
      return <p>{item}</p>
    })

    return (
      <>
        <header className="App-header2" >制品包信息</header>
        <div className={"Connector-Table-Container"}>
          <ConfigProvider
            theme={{
              components: {
                Table: {
                  colorBgContainer: "#282c34",
                  headerColor: "white",
                  colorText: "white",
                  rowSelectedBg: "#282c34",
                  rowSelectedHoverBg: "#282c34",
                },
                Card: {
                  actionsBg: "#282c34",
                  headerBg: "#282c34",
                  colorBgContainer: "#282c34",
                  colorText: "white",
                  extraColor: "white",
                }
              },
            }}
          >
            <Card title="包信息"
                  bordered={true}
                  size={"small"}
                  className={"Connector-Card"}
            >
              <p>包&#12288;&#12288;名：{this.state.selectedPackage ? this.state.selectedPackage.name : ""}</p>
              <p>大&#12288;&#12288;小：{this.state.selectedPackage ? this.state.selectedPackage.size : ""}</p>
              <p>版&#12288;&#12288;本：{this.state.selectedPackage ? this.state.selectedPackage.version : ""}</p>
              <p>创建时间：{this.state.selectedPackage ? this.state.selectedPackage.created : ""}</p>
              {this.state.mongoConfig && this.state.mongoConfig.includeMongoDB && (
                <>
                  <p>MongoDB版本：{this.state.mongoConfig.mongoVersion}</p>
                  <p>MongoDB架构：{this.state.mongoConfig.mongoArch}</p>
                  <p>操作系统类型：{this.state.mongoConfig.osType}</p>
                </>
              )}
            </Card>
            <Card title="连接器信息"
                  bordered={true}
                  size={"small"}
                  className={"Connector-Card"}
            >
              {selectedConnectors}
            </Card>
          </ConfigProvider>
        </div>
        <header className="App-header2" >制品包构建信息</header>
        <div className="Steps">
          <ConfigProvider
            theme={{
              components: {
                Steps: {
                  navArrowColor: "white",
                  colorTextDescription: "white",
                  colorTextDisabled: "white",
                },
              },
            }}
          >
            <Steps
              direction="vertical"
              size="small"
              current={2}
              items={this.state.steps}
            />
          </ConfigProvider>
        </div>
        <a href={this.state.downloadLink} style={{color: "white"}}>{this.state.downloadText}{this.state.downloadLink}</a>
      </>
    )
  }
}

export default PackageResult;