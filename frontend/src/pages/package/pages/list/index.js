import React from "react";
import {But<PERSON>, ConfigProvider, Table} from "antd";
import '../../../../App.css'
import instance from "../../../../request";

class PackageList extends React.Component {
  state = {
    dataSource: [],
    columns: [
      {
        title: '版本号',
        dataIndex: 'version',
        key: 'version',
        width: 10,
      },
      {
        title: '大小',
        dataIndex: 'size',
        key: 'size',
        width: 5,
      },
      {
        title: '包名',
        dataIndex: 'name',
        key: 'name',
        width: 65,
      },
      {
        title: '创建时间',
        dataIndex: 'created',
        key: 'created',
        width: 15,
        defaultSortOrder: 'descend',
        sorter: (a, b) => new Date(a.created) - new Date(b.created),
      },
      {
        title: '获取包',
        width: 5,
        render: (text, record, index) => {
          return (
            <div>
              <Button href={"/package/generate?info=" + JSON.stringify(text)} type="link">生成制品包</Button>
              <Button href={"/package/lite?info=" + JSON.stringify(text)} type="link">生成Lite版本</Button>
              <Button href={record.downloadLink} type="link">下载</Button>
            </div>
          )
        }
      }
    ],
  }

  componentDidMount() {
    instance.get('/api/artifacts').then(r => {
      this.setState({dataSource: r.data.data})
    })
  }

  render() {

    return (
      <>
        <header className="App-header2" >制品包列表</header>
        <ConfigProvider
          theme={{
            components: {
              Table: {
                colorBgContainer: "#282c34",
                headerColor: "white",
                colorText: "white",
              },
            },
          }}
        >
          <Table className="App-Table"
                 bordered
                 dataSource={this.state.dataSource}
                 columns={this.state.columns}
                 pagination={{position: ["topRight"]}}
          />
        </ConfigProvider>
      </>
    );
  }
}

export default PackageList;