import React from "react";
import {Card, ConfigProvider, Select, Space, Table, Switch} from "antd";
import {CheckOutlined, LoadingOutlined, PlayCircleOutlined} from "@ant-design/icons";
import instance from "../../../../request";
import '../../../../App.css'

class PackageLite extends React.Component {
  state = {
    selectedPackage: [],
    osType: 'ubuntu-20.04',
    includeJdkAndMongodb: true,
    packageColumns: [
      {
        title: '版本号',
        dataIndex: 'version',
        key: 'version',
        width: 10,
      },
      {
        title: '大小',
        dataIndex: 'size',
        key: 'size',
        width: 5,
      },
      {
        title: '包名',
        dataIndex: 'name',
        key: 'name',
        width: 65,
      },
      {
        title: '创建时间',
        dataIndex: 'created',
        key: 'created',
        width: 15,
      }
    ],
    cardAction: {
      icon: <PlayCircleOutlined />,
      text: "点击开始构建",
    },
    taskId: ""
  }

  componentDidMount() {
    const search = window.location.search;
    const params = new URLSearchParams(search);
    const packageInfo = JSON.parse(params.get("info") || JSON.stringify({}))
    this.setState({
      selectedPackage: [packageInfo]
    })
  }

  handleOSTypeChange = (value) => {
    this.setState({ osType: value });
  }

  fetchWorkflowId() {
    instance.post('/api/artifact/worker/result', {
      task_id: this.state.taskId
    }).then(r => {
      if (r.data.data !== null) {
        this.setState({
          cardAction: {
            icon: <CheckOutlined />,
            text: "流程已运行"
          }
        }, () => {
          setTimeout(() => {
            window.location.href = '/package/result?workflow_id=' + r.data.data + 
              "&info=" + JSON.stringify(this.state.selectedPackage) + 
              "&is_lite=true";
          }, 2000)
        })
      } else {
        setTimeout(() => {
          this.fetchWorkflowId.bind(this)()
        }, 10000)
      }
    })
  }

  handlePackage() {
    let packageName = this.state.selectedPackage[0].name.replaceAll(".tar.gz", "")
    
    this.setState({
      cardAction: {
        icon: <LoadingOutlined />,
        text: "等待触发流程运行",
      }
    })

    instance.post('/api/artifact/lite', {
      os: this.state.osType,
      package_name: packageName,
      include_jdk_and_mongodb: this.state.includeJdkAndMongodb,
    }).then(r => {
      this.setState({
        taskId: r.data.data
      }, () => {
        this.fetchWorkflowId.bind(this)()
      })
    })
  }

  render() {
    const CardActions = [
      <div style={{color: "white"}} onClick={this.handlePackage.bind(this)}>
        <text style={{ fontWeight: "700" }}>{this.state.cardAction.text}</text>&nbsp;
        {this.state.cardAction.icon}
      </div>
    ]

    return (
      <>
        <header className="App-header2" >选中的制品包</header>
        <ConfigProvider
          theme={{
            components: {
              Table: {
                colorBgContainer: "#282c34",
                headerColor: "white",
                colorText: "white",
              },
            },
          }}
        >
          <Table className="App-Table"
               bordered
               dataSource={this.state.selectedPackage}
               columns={this.state.packageColumns}
               pagination={false}
          />
        </ConfigProvider>
        <header className="App-header2">操作系统配置</header>
        <div style={{ padding: '20px', backgroundColor: '#282c34', color: 'white' }}>
          <Space direction="vertical">
            <Space>
              <span>操作系统类型：</span>
              <Select
                value={this.state.osType}
                onChange={this.handleOSTypeChange}
                style={{ width: 200 }}
                options={[
                  { value: 'ubuntu-20.04', label: 'linux ubuntu-20.04' },
                  { value: 'ubuntu-22.04', label: 'linux ubuntu-22.04' },
                  { value: 'centos-7', label: 'linux centos-7' },
                  { value: 'windows', label: 'windows' }
                ]}
              />
            </Space>
            <Space>
              <span>包含JDK和MongoDB：</span>
              <Switch
                checked={this.state.includeJdkAndMongodb}
                onChange={(checked) => this.setState({ includeJdkAndMongodb: checked })}
                defaultChecked
              />
            </Space>
          </Space>
        </div>
        <div className="Connector-Table-Container" style={{ display: 'flex', justifyContent: 'center' }}>
          <ConfigProvider
            theme={{
              components: {
                Card: {
                  actionsBg: "#282c34",
                  headerBg: "#282c34",
                  colorBgContainer: "#282c34",
                  colorText: "white",
                  extraColor: "white",
                }
              },
            }}
          >
            <Card
              bordered={true}
              size={"small"}
              className={"Connector-Card"}
              actions={CardActions}
            >
              <p>包名：{this.state.selectedPackage[0]?.name}</p>
              <p>大小：{this.state.selectedPackage[0]?.size}</p>
              <p>版本：{this.state.selectedPackage[0]?.version}</p>
              <p>创建时间：{this.state.selectedPackage[0]?.created}</p>
            </Card>
          </ConfigProvider>
        </div>
      </>
    )
  }
}

export default PackageLite;
