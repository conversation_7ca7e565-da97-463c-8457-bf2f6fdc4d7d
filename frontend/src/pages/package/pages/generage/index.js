import React from "react";
import {Card, ConfigProvider, Table, Switch, Select, Space} from "antd";
import {CheckOutlined, LoadingOutlined, PlayCircleOutlined} from "@ant-design/icons";
import instance from "../../../../request";
import '../../../../App.css'

class PackageGenerate extends React.Component {

  state = {
    selectedPackage: [],
    includeMongoDB: false,
    osType: 'ubuntu-20.04',
    // mongoArch: 'x86_64',
    packageColumns: [
      {
        title: '版本号',
        dataIndex: 'version',
        key: 'version',
        width: 10,
      },
      {
        title: '大小',
        dataIndex: 'size',
        key: 'size',
        width: 5,
      },
      {
        title: '包名',
        dataIndex: 'name',
        key: 'name',
        width: 65,
      },
      {
        title: '创建时间',
        dataIndex: 'created',
        key: 'created',
        width: 15,
      }
    ],
    dataSource: [],
    columns: [{
      title: '连接器名称',
      dataIndex: 'name',
      key: 'name',
    },{
      title: '大小',
      dataIndex: 'size',
      key: 'size',
    },{
      title: '创建时间',
      dataIndex: 'created',
      key: 'created'
    }],
    selectedRows: [],
    cardAction: {
      icon: <PlayCircleOutlined />,
      text: "点击开始构建",
    },
    taskId: ""
  }

  componentDidMount() {
    const search = window.location.search; // 获取 URL 中的查询字符串，如 "?foo=bar"
    const params = new URLSearchParams(search); // 使用 URLSearchParams 解析查询字符串
    const packageInfo = JSON.parse(params.get("info") || JSON.stringify({}))
    this.setState({
      selectedPackage: [JSON.parse(params.get("info") || JSON.stringify({}))]
    })
    let version = packageInfo.name.replaceAll(".tar.gz", "")
    instance.get('/api/artifacts/connectors?version=' + version).then(r => {
      this.setState({dataSource: r.data.data})
    })
  }

  handleSelectedRowChanged(selectedRows) {
    this.setState({
      selectedRows: selectedRows
    })
  }

  fetchWorkflowId(connectors_list) {
    instance.post('/api/artifact/worker/result', {
      task_id: this.state.taskId
    }).then(r => {
      if (r.data.data !== null) {
        this.setState({
          cardAction: {
            icon: <CheckOutlined />,
            text: "流程已运行"
          }
        }, () => {
          setTimeout(() => {
            const mongoConfig = this.state.includeMongoDB ? {
              includeMongoDB: true,
              mongoArch: this.state.mongoArch,
              osType: this.state.osType,
              mongoVersion: "6.0"
            } : { includeMongoDB: false };
            window.location.href = '/package/result?workflow_id=' + r.data.data + 
              "&info=" + JSON.stringify(this.state.selectedPackage) + 
              "&connectors=" + JSON.stringify(connectors_list) + 
              "&mongo_config=" + JSON.stringify(mongoConfig);
          }, 2000)
        })
      } else {
        setTimeout(() => {
          this.fetchWorkflowId.bind(this)(connectors_list)
        }, 10000)
      }
    })
  }

  handleMongoDBChange = (checked) => {
    this.setState({ includeMongoDB: checked });
  }

  handleOSTypeChange = (value) => {
    this.setState({ osType: value });
  }

  handleMongoArchChange = (value) => {
    this.setState({ mongoArch: value });
  }

  handlePackage() {
    console.log(this.state.selectedRows, this.state.selectedPackage)
    let connectors_list = this.state.selectedRows.map(item => {
      return item.name
    })
    let connectorsList = window.btoa(connectors_list.join("#"))
    let packageName = this.state.selectedPackage[0].name.replaceAll(".tar.gz", "")
    this.setState({
      cardAction: {
        icon: <LoadingOutlined />,
        text: "等待触发流程运行",
      }
    })
    instance.post('/api/artifact/trigger', {
      connectors_list: connectorsList,
      package_name: packageName,
      include_mongodb: this.state.includeMongoDB,
      os: this.state.osType,
      // mongo_arch: this.state.includeMongoDB ? this.state.mongoArch : undefined,
      // mongo_version: this.state.includeMongoDB ? "6.0" : undefined,
    }).then(r => {
      this.setState({
        taskId: r.data.data
      }, () => {
        this.fetchWorkflowId.bind(this)(connectors_list)
      })
    })
  }

  render() {

    const rowSelection = {
      onChange: (selectedRowKeys, selectedRows) => this.handleSelectedRowChanged(selectedRows)
    }

    const selectedCardItems = this.state.selectedRows.map((item) => {
      return <p>{item.name}</p>
    })

    const CardActions = this.state.selectedRows.length > 0 ? [
      <div style={{color: "white"}} onClick={this.handlePackage.bind(this)}>
        <text style={{ fontWeight: "700" }}>{this.state.cardAction.text}</text>&nbsp;
        {this.state.cardAction.icon}
      </div>
    ] : []

    return (
      <>
        <header className="App-header2" >选中的制品包</header>
        <ConfigProvider
          theme={{
            components: {
              Table: {
                colorBgContainer: "#282c34",
                headerColor: "white",
                colorText: "white",
              },
            },
          }}
        >
          <Table className="App-Table"
               bordered
               dataSource={this.state.selectedPackage}
               columns={this.state.packageColumns}
               pagination={false}
          />
        </ConfigProvider>
        <header className="App-header2">MongoDB配置</header>
        <div style={{ padding: '20px', backgroundColor: '#282c34', color: 'white' }}>
          <Space direction="vertical">
            <div>
              <Space>
                <span>是否内含MongoDB数据库：</span>
                <Switch 
                  checked={this.state.includeMongoDB}
                  onChange={this.handleMongoDBChange}
                />
              </Space>
            </div>
            {this.state.includeMongoDB && (
              <div>
                <Space direction="vertical">
                  <Space>
                    <span>操作系统类型：</span>
                    <Select
                      value={this.state.osType}
                      onChange={this.handleOSTypeChange}
                      style={{ width: 200 }}
                      options={[
                        { value: 'ubuntu-20.04', label: 'linux ubuntu-20.04' },
                        { value: 'ubuntu-22.04', label: 'linux ubuntu-22.04' },
                        { value: 'centos-7', label: 'linux centos-7' },
                        { value: 'windows', label: 'windows' }
                      ]}
                    />
                  </Space>
                  {/* <Space>
                    <span>MongoDB架构类型：</span>
                    <Select
                      value={this.state.mongoArch}
                      onChange={this.handleMongoArchChange}
                      style={{ width: 120 }}
                      options={[
                        { value: 'x86_64', label: 'x86_64' },
                        { value: 'arm64', label: 'ARM64' }
                      ]}
                    />
                  </Space> */}
                </Space>
              </div>
            )}
          </Space>
        </div>
        <header className="App-header2">连接器选择</header>

        <div className="Connector-Table-Container">
          <ConfigProvider
            theme={{
              components: {
                Table: {
                  colorBgContainer: "#282c34",
                  headerColor: "white",
                  colorText: "white",
                  rowSelectedBg: "#282c34",
                  rowSelectedHoverBg: "#282c34",
                },
                Card: {
                  actionsBg: "#282c34",
                  headerBg: "#282c34",
                  colorBgContainer: "#282c34",
                  colorText: "white",
                  extraColor: "white",
                }
              },
            }}
          >
            <Table className="App-Table Connector-Table"
                   bordered
                   rowSelection={{
                     type: "checkbox",
                     ...rowSelection,
                   }}
                   dataSource={this.state.dataSource}
                   columns={this.state.columns}
                   pagination={false}
                   rowKey={"name"}
            />
            <Card title="已选择的连接器"
                  bordered={true}
                  size={"small"}
                  className={"Connector-Card"}
                  actions={CardActions}
            >
              {selectedCardItems}
            </Card>
          </ConfigProvider>
        </div>
      </>
    )
  }
}

export default PackageGenerate;