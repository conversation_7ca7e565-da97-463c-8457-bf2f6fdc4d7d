import React from "react";
import '../../../../App.css';
import './index.css';
import {Button, ConfigProvider, Input, message, Space} from "antd";
import instance from "../../../../request";


class PackageLogin extends React.Component {

  state = {
    username: "",
    password: "",
  }

  inputUsername(e) {
    this.setState({
      username: e.target.value
    })
  }

  inputPassword(e) {
    this.setState({
      password: e.target.value
    })
  }

  login() {
    instance.post('/api/artifact/login', {
      username: this.state.username,
      password: this.state.password,
    }).then(r => {
      if (r.data.code === 1) {
        message.error(r.data.msg)
      } else {
        message.success("登陆成功")
        localStorage.setItem("token", r.data.data)
        window.location.href = '/package/list'
      }
    })
  }

  render() {
    return (
      <>
        <ConfigProvider
          theme={{
            components: {
              Input: {
                activeBg: "#282c34",
                colorBgContainerDisabled: "#282c34",
                colorBgContainer: "#282c34",
                activeBorderColor: "#ffffff",
                hoverBg: "#282c34",
                colorTextPlaceholder: "rgba(255,255,255,0.49)",
                colorTextDescription: "#ffffff",
                colorTextDisabled: "#ffffff",
                colorText: "#ffffff",
              },
            },
          }}
        >
          <header className="App-header2" >未登录，请登录</header>
          <Space direction="vertical">
              <Space direction="horizontal">
                <div className="text">用户名：</div>
                <Input placeholder="username" onChange={this.inputUsername.bind(this)} value={this.state.username} />
              </Space>
              <Space direction="horizontal">
                <div className="text">密码：</div>
                <Input.Password placeholder="password" onChange={this.inputPassword.bind(this)} value={this.state.password} />
              </Space>
              <Button block disabled={ this.state.username === "" || this.state.password === ""}
                      onClick={this.login.bind(this)}
                      style={{color: "white", backgroundColor: "#282c34"}}>
                点击登陆
              </Button>
          </Space>
        </ConfigProvider>
      </>
    )
  }
}

export default PackageLogin;