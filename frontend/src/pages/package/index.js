import React from "react";
import {Button} from "antd";
import { Outlet } from 'react-router-dom';
import '../../App.css'
import instance from "../../request";

class Package extends React.Component {
  state = {
    dataSource: [],
    columns: [
      {
        title: '版本号',
        dataIndex: 'version',
        key: 'version',
        width: 10,
      },
      {
        title: '大小',
        dataIndex: 'size',
        key: 'size',
        width: 5,
      },
      {
        title: '包名',
        dataIndex: 'name',
        key: 'name',
        width: 65,
      },
      {
        title: '创建时间',
        dataIndex: 'created',
        key: 'created',
        width: 15,
        defaultSortOrder: 'descend',
        sorter: (a, b) => new Date(a.created) - new Date(b.created),
      },
      {
        title: '获取包',
        width: 5,
        render: (text, record, index) => {
          return (
            <div>
              <Button href={"/package/generate?info=" + JSON.stringify(text)} type="link">生成制品包</Button>
              <Button href={record.downloadLink} type="link">完整包</Button>
            </div>
          )
        }
      }
    ],
  }

  componentDidMount() {
    instance.get('/api/artifacts').then(r => {
      this.setState({dataSource: r.data.data})
    })
  }

  render() {

    return (
      <>
        <header className="App-header">制品包生成</header>
        <div style={{color: "white"}}>不知道如何操作？请点击 -> <a style={{ color: "white" }} href="https://tapdata.feishu.cn/docx/GJxkdLX66o1ngsxZ1mLcQBDonLf?from=from_copylink">帮助文档</a></div>
        <Outlet />
      </>
    );
  }
}

export default Package;