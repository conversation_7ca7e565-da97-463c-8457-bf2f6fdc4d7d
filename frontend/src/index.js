import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import PackageGenerate from './pages/package/pages/generage';
import {createBrowserRouter, RouterProvider, createRoutesFromElements, Route} from "react-router-dom";
import PackageList from "./pages/package/pages/list";
import PackageResult from "./pages/package/pages/result";
import PackageLogin from "./pages/package/pages/login";
import Package from "./pages/package";
import PackageLite from "./pages/package/pages/lite";
// import Build from "./pages/build";
// import BuildResult from "./pages/build/pages/result";
// import BuildOption from "./pages/build/pages/option";


const router = createBrowserRouter(
  createRoutesFromElements(
    <Route path="/" element={<App/>}>
      <Route path="/package" element={<Package/>}>
        <Route path="list" element={<PackageList/>}/>
        <Route path="generate" element={<PackageGenerate/>}/>
        <Route path="result" element={<PackageResult/>}/>
        <Route path="login" element={<PackageLogin/>}/>
        <Route path="lite" element={<PackageLite/>}/>
      </Route>
      {/*<Route path="/build" element={<Build />}>*/}
      {/*  <Route path="option" element={<BuildOption />}/>*/}
      {/*  <Route path="result" element={<BuildResult />}/>*/}
      {/*</Route>*/}
    </Route>
  )
);

ReactDOM.createRoot(document.getElementById("root")).render(
  <React.StrictMode>
    <RouterProvider router={router} />
  </React.StrictMode>
);
