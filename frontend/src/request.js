import axios from "axios";
import {message} from "antd";

const instance = axios.create({
  baseURL: process.env.VUE_APP_BASE_API,
})

// 添加请求拦截器
instance.interceptors.request.use(function (config) {
  // 从 localStorage 中获取 token
  const token = localStorage.getItem('token');

  // 如果存在 token，则添加到请求头
  if (token) {
    config.headers.Authorization = token;
  }

  return config;
}, function (error) {
  // 对请求错误做些什么
  return Promise.reject(error);
});

// 添加响应拦截器
instance.interceptors.response.use(function (response) {
  // 如果 response.data.code === 2，那么跳转到登录页面
  if (response.data.code === 2 && !window.location.href.includes('/package/login')) {
    message.error("未登录，权限不足", 2, () => {
      console.log("跳转")
      window.location.href = '/package/login/';
    })
  }

  return response;
}, function (error) {
  // 对响应错误做点什么
  return Promise.reject(error);
});

export default instance;