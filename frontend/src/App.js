import './App.css';
import React from "react";
import { Outlet } from 'react-router-dom';
import {Image, Layout, Menu} from 'antd';
const { Header } = Layout;


class App extends React.Component {

  state = {
    mapItems: [{
      text: "获取安装包",
      src: "/package/list"
    // },{
    //   text: "构建制品包",
    //   src: "/build/option"
    // },{
    //   text: "部署",
    //   src: "/"
    }]
  }

  switchTo(value) {
    console.log(value)
    for(let i=0; i<this.state.mapItems.length; i++) {
      if (this.state.mapItems[i].text === value.key) {
        console.log(this.state.mapItems[i].src)
        window.location.href = this.state.mapItems[i].src
      }
    }
  }

  render() {

    const items = this.state.mapItems.map((item) => ({
      key: item.text,
      label: `${item.text}`,
    }));

    return (
      <>
        <Layout>
          <Header
            style={{
              display: 'flex',
              alignItems: 'center',
              width: "100%",
              color: "black",
              top: "0",
              position: "fixed",
              zIndex: 10000
            }}
          >
            <Image className={"logo"} src={"https://cloud.tapdata.net/console/v3/img/logo.eef94eb2.svg"}/>
            <Menu
              theme="dark"
              mode="horizontal"
              defaultSelectedKeys={['2']}
              items={items}
              onClick={this.switchTo.bind(this)}
              style={{
                flex: 1,
                minWidth: 0,
              }}
            />
          </Header>
        </Layout>
        <div style={{height: "64px"}}/>
        <div className="Background">
          <Outlet />
          <footer/>
        </div>
      </>
    );
  }
}

export default App;
