.Background {
  text-align: center;
  display: flex;
  background-color: #282c34;
  min-height: 100vh;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

header {
  color: white;
}

.App-header {
  margin: calc(5px + 1.5vmin);
  font-size: calc(10px + 2vmin);
}

.App-header2 {
  font-size: calc(8px + 1.5vmin);
  margin: calc(3px + 1.5vmin);
}

.App-header3 {
  font-size: calc(5px + 1.5vmin);
  margin: calc(2px + 1.5vmin);
}

.App-Table {
  width: 80%;
  color: white;
}

.Connector-Table-Container {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 80%;
}

.Connector-Table {
  width: 65%
}

.Connector-Card {
  width: 32%;
  color: white;
  /*min-height: 50vh;*/
  height: auto;
  line-height: 1;
  display: flex;
  flex-direction: column;
}

.App-link {
  color: #61dafb;
}

@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.ant-pagination-item {
  border:1px solid #336ECD;
  background-color: transparent;
    color: white;
  a{
      color: white !important;
  }
}

.ant-pagination-item-active{
  border:1px solid #336ECD;
  background-color: transparent;
    color: #FFC600;
  a{
      color: black !important;
  }

}

.ant-pagination-prev .ant-pagination-item-link{
  border:1px solid #336ECD;
  background-color: transparent;
    color: white;
  a{
      color: white !important;
  }
  button{
    color: white !important;
  }
  span {
    color: white;
  }
}

.ant-pagination-next .ant-pagination-item-link{
  border:1px solid #336ECD;
  background-color: transparent;
    color: white;
  a{
      color: #FFC600 !important;
  }
  span {
    color: white;
  }
}

.ant-pagination-item:hover{
  border:1px solid #336ECD;
   background-color: transparent;
  a{
      color: #00FFF7;
  }
}
.ant-pagination-item-link:hover{
  border-color: #00FFF7;
  span{
      color: #00FFF7;
  }
}
.ant-select:not(.ant-select-disabled):hover .ant-select-selector{
  border-color: #00FFF7;
}
.ant-pagination-options-quick-jumper input:hover{
  border-color: #00FFF7;
}
.ant-select-focused.ant-select-single:not(.ant-select-customize-input) .ant-select-selector{
  border-color: #00FFF7;
}

.ant-pagination-options-quick-jumper {
  color: white;
  input {
     border:1px solid #336ECD;
    background-color: transparent;
    color: #FFC600;
  }
}

.ant-pagination-jump-next .ant-pagination-item-container {
  color: white;

  span {
    color: white !important;
  }
}

.ant-card-head-title {
  color: white;
}

footer {
  height: 1.5rem;
}

.logo {
  width: 120px;
  min-width: 120px;
  height: 32px;
  border-radius: 6px;
  margin-inline-end: 24px;
  /*padding-left: 0.5rem;*/
}

.Steps {
  display: flex;
  align-items: center;
  justify-content: center;
  color: white !important;
  .ant-steps-item-custom {
    color: white;
  }
  div.ant-steps-item-title {
    color: white !important;
  }
  .ant-steps-icon svg {
    color: rgb(255, 255, 255);
  }
  .ant-steps-item-tail::after{
    background-color: white !important;
  }
  .ant-steps-item-icon {
    border-color: white !important;
    background-color: #282c34 !important;
  }
  .ant-steps-icon {
    color: white !important;
  }
}