"""
buildV2 命令

@author: <PERSON>
@date: 2023.05.08
"""

import os
import time
import traceback

from utils.api import GithubApi
from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc
from utils import option
from utils import common
from utils import exception


@CommandParser.register
class Buildop(Handler):
    command = "buildOp"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "buildOp",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "job_id": None
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        """
        1. 获取选项信息
        2. 选项信息合法化判断
        3. 格式化选项信息
        4. 触发构建
        5. 构建信息发送
        """
        # 1. 获取选项信息
        enterprise_branch = option.EnterpriseBranch(self.job_id, open_id).get_option()
        opensource_branch = option.OpenSourceBranch(self.job_id, open_id).get_option()
        frontend_branch = option.FrontendBranch(self.job_id, open_id).get_option()
        deployment = option.Deployment(self.job_id, open_id).get_option()
        connectors = option.OpConnectors(self.job_id, open_id).get_option()
        build_full = option.BuildFull(self.job_id, open_id).get_option()
        is_package_tar = option.IsPackageTar(self.job_id, open_id).get_option()
        is_docker_save = option.IsDockerSave(self.job_id, open_id).get_option()
        frontend_mode = option.FrontendMode(self.job_id, open_id).get_option()
        # 2. 选项信息合法化判断
        if enterprise_branch is None or opensource_branch is None or frontend_branch is None:
            return mc.make_error_branch().to_dict(), False
        # 3. 格式化选项信息
        branches = f"{frontend_branch}%{enterprise_branch}%{opensource_branch}"  # 分支信息
        # 判断如果版本 > 3.5.2 则使用 workflow_dispatch 触发构建
        # 否则使用 repository_dispatch 触发构建
        if common.compare_version(frontend_branch, "develop-v3.5.2") or \
                common.compare_version(enterprise_branch, "develop-v3.5.2") or \
                common.compare_version(opensource_branch, "develop-v3.5.2"):
            owner = os.getenv("OWNER")
            repo = os.getenv("TRIGGER_REPO")
            github_obj = GithubApi(owner, repo)
            res = github_obj.trigger_by_workflow_dispatch(
                "build-self-hosted.yaml",
                enterprise_branch,
                FRONTEND_BRANCH=frontend_branch,
                OPENSOURCE_BRANCH=opensource_branch,
                BUILD_FULL=build_full,
                DEPLOY=deployment,
                FRONTEND_MODE=frontend_mode,
                IS_TAR=is_package_tar,
                JOB_ID=self.job_id,
            )
        else:
            owner = os.getenv("OWNER")
            repo = os.getenv("TRIGGER_REPO")
            github_obj = GithubApi(owner, repo)
            res = github_obj.trigger(
                branches,
                self.job_id,
                build_full=build_full,
                deploy=deployment,
                is_package_tar=is_package_tar,
                is_docker_save=is_docker_save,
                enterprise_or_cloud="enterprise",
                frontend_mode=frontend_mode,
                connectors=connectors,
            )
        # 5. 构建信息发送
        if res:
            header = "🎉 构建任务创建成功，执行成功后会通知你哦"
            color = "green"
        else:
            header = "😭 构建任务创建失败，请联系Jerry处理"
            color = "red"
        message_card = common.field_list_message_card(
            header=header,
            color=color,
            企业版分支=enterprise_branch,
            开源版分支=opensource_branch,
            前端分支=frontend_branch,
            压缩包=is_package_tar,
            镜像压缩包=is_docker_save,
            前端mode参数=frontend_mode,
        )
        return message_card.to_dict(), res
