"""
packageAndUpdate 命令

@author: 
@date: 2022.11.02
"""
import json
import asyncio

from utils.common import get_branch_threading
from utils.handler import Handler
from utils import message_card as mc, common, option
from utils.init import app
from utils.message_card import trigger_button, MessageCard
from utils.schdule import CommandParser
from utils.session import Session


@CommandParser.register
class Packageandupdate(Handler):
    command = "打包并更新"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "打包并更新 [云版] [研发环境/测试环境/生产环境]",  # 命令示例
        "说明": "触发Github打包并更新环境",  # 填写选项和说明
    }

    image_tags = []
    envs = []
    branches = {}

    def __init__(self):
        super().__init__()
        self.magic_arg = {
            "is_cloud": False
        }

    def _make_message_card(self, header):
        header = mc.Header(f"▶️ {header}")
        config = mc.Config()
        # 创建消息卡片
        message_card = mc.MessageCard(header, config)
        return message_card

    def _cloud_panel(self, message_card, namespace=None):
        opensource_branch, enterprise_branch, frontend_branch, cloud_branch, tapflow_branch = get_branch_threading(
            'tapdata', 'tapdata-enterprise', 'tapdata-enterprise-web', 'tapdata-cloud', 'tapflow-web')
        opensource_div = option.OpenSourceBranch(self.job_id, self.open_id,
                                                 option_list=opensource_branch).make_select()
        enterprise_div = option.EnterpriseBranch(self.job_id, self.open_id, option_list=enterprise_branch) \
            .make_select()
        frontend_div = option.FrontendBranch(self.job_id, self.open_id, option_list=frontend_branch).make_select()
        cloud_div = option.CloudBranch(self.job_id, self.open_id, option_list=cloud_branch).make_select()
        tapflow_div = option.TapFlowBranch(self.job_id, self.open_id, option_list=tapflow_branch).make_select()
        # 按模块打包

        build_frontend_div = option.UpdateConsole(self.job_id, self.open_id).make_select()

        build_tm_java_div = option.UpdateTM(self.job_id, self.open_id).make_select()
        build_agent_div = option.UpdateAgent(self.job_id, self.open_id).make_select()
        build_ticket_div = option.UpdateTicket(self.job_id, self.open_id).make_select()
        build_tapflow_div = option.UpdateTapFlow(self.job_id, self.open_id).make_select()
        message_card.add_element(opensource_div, enterprise_div, frontend_div, cloud_div, tapflow_div)
        elements = [build_frontend_div, build_tm_java_div, build_agent_div, build_ticket_div, build_tapflow_div]
        message_card.add_element(*elements)

    def _new_op_panel(self, message_card):
        opensource_branch, enterprise_branch, frontend_branch, connectors_branch, connectors_enterprise_branch = \
            get_branch_threading(
                'tapdata',
                'tapdata-enterprise',
                'tapdata-enterprise-web',
                'tapdata-connectors',
                'tapdata-connectors-enterprise',
            )
        license_branch = get_branch_threading('tapdata-license', last_commit_day=365)
        opensource_div = option.OpenSourceBranch(self.job_id, self.open_id,
                                                 option_list=opensource_branch).make_select()
        enterprise_div = option.EnterpriseBranch(self.job_id, self.open_id,
                                                 option_list=enterprise_branch).make_select()
        frontend_div = option.FrontendBranch(self.job_id, self.open_id, option_list=frontend_branch).make_select()
        connectors_div = option.ConnectorsBranch(self.job_id, self.open_id, option_list=connectors_branch).make_select()
        connectors_enterprise_div = option.ConnectorsEnterpriseBranch(self.job_id, self.open_id,
                                                                      option_list=connectors_enterprise_branch).make_select()
        license_tip = mc.Div(text=mc.Text("❗️License分支向上选择最近的release分支，如果要打release-v3.5.1的包，需要选择release-v3.5.7"
                                          "的分支, 如果是打最新的包，选择main分支即可"))
        license_div = option.LicenseBranch(self.job_id, self.open_id, option_list=license_branch).make_select()
        # 是否运行测试
        run_test = option.RunTest(self.job_id, self.open_id).make_select()
        platform_div = option.PlatformOption(self.job_id, self.open_id).make_select()
        package_tar_div = option.IsPackageTar(self.job_id, self.open_id).make_select()
        frontend_mode = option.FrontendMode(self.job_id, self.open_id).make_select()
        java_version = option.JavaVersion(self.job_id, self.open_id).make_select()
        connectors_options = option.ConnectorsOptions(self.job_id, self.open_id).make_select()
        deployments = ["无"] + ["dev(3030)", "new(3031)"]
        deploy = option.Deployment(self.job_id, self.open_id, option_list=deployments).make_select(initial_option=1)
        save_template = option.SaveTemplateOptions(self.job_id, self.open_id).make_select()
        message_card.add_element(opensource_div, enterprise_div, frontend_div, connectors_div,
                                 connectors_enterprise_div, license_tip, license_div)
        include_apiserver = option.IncludeApiServer(self.job_id, self.open_id).make_select()
        message_card.add_element(package_tar_div, frontend_mode, platform_div, run_test, java_version, connectors_options, include_apiserver)
        message_card.add_element(mc.Hr())
        message_card.add_element(deploy)
        message_card.add_element(mc.Hr(), save_template)

    def _opensource_panel(self, message_card):
        opensource_branch, frontend_branch, connectors_branch = common.get_branch_threading('tapdata',
                                                                                            'tapdata-enterprise-web',
                                                                                            'tapdata-connectors')
        opensource_div = option.OpenSourceBranch(self.job_id, self.open_id,
                                                 option_list=opensource_branch).make_select()
        frontend_div = option.FrontendBranch(self.job_id, self.open_id, option_list=frontend_branch).make_select()
        connectors_div = option.ConnectorsBranch(self.job_id, self.open_id, option_list=connectors_branch) \
            .make_select()
        message_card.add_element(opensource_div, frontend_div, connectors_div)

    def _package_op_by_release_branch(self, message_card: MessageCard):
        rv = option.ReleaseVersion(self.job_id, self.open_id)
        release_version_div = rv.make_select()
        app.logger.info(f"Get Release Version:{rv.option_list}, {self.open_id}, {self.job_id}")
        package_tar_div = option.IsPackageTar(self.job_id, self.open_id).make_select()
        frontend_mode = option.FrontendMode(self.job_id, self.open_id).make_select()
        java_version = option.JavaVersion(self.job_id, self.open_id).make_select()
        include_apiserver = option.IncludeApiServer(self.job_id, self.open_id).make_select()
        connectors_options = option.ConnectorsOptions(self.job_id, self.open_id).make_select()
        run_test = option.RunTest(self.job_id, self.open_id).make_select()
        deployments = ["无"] + ["dev(3030)", "new(3031)"]
        deploy = option.Deployment(self.job_id, self.open_id, option_list=deployments).make_select(initial_option=1)
        message_card.add_element(release_version_div, package_tar_div, frontend_mode, java_version, connectors_options, run_test, include_apiserver, mc.Hr(), deploy)

    def _default_panel(self, message_card):
        opensource_branch, enterprise_branch, frontend_branch = get_branch_threading('tapdata',
                                                                                     'tapdata-enterprise',
                                                                                     'tapdata-enterprise-web')
        license_branch = get_branch_threading('tapdata-license', last_commit_day=365)
        enterprise_div = option.EnterpriseBranch(self.job_igd, self.open_id,
                                                 option_list=enterprise_branch).make_select()
        opensource_div = option.OpenSourceBranch(self.job_id, self.open_id,
                                                 option_list=opensource_branch).make_select()
        frontend_div = option.FrontendBranch(self.job_id, self.open_id, option_list=frontend_branch).make_select()
        build_full_div = option.BuildFull(self.job_id, self.open_id).make_select()
        connectors = option.OpConnectors(self.job_id, self.open_id).make_select()
        package_tar_div = option.IsPackageTar(self.job_id, self.open_id).make_select()
        frontend_mode = option.FrontendMode(self.job_id, self.open_id).make_select()
        java_version = option.JavaVersion(self.job_id, self.open_id).make_select()
        connectors_options = option.ConnectorsOptions(self.job_id, self.open_id).make_select()
        message_card.add_element(enterprise_div, opensource_div, frontend_div)
        message_card.add_element(connectors)
        message_card.add_element(build_full_div)
        message_card.add_element(package_tar_div, frontend_mode, java_version, connectors_options)
        """增加分隔符"""
        # message_card.add_element(mc.Hr())
        # message_card.add_element(deploy)

    def default_handler(self, header, panel_key, namespace=None) -> mc.MessageCard:
        """打包构建部分"""
        message_card = self._make_message_card(header)
        if panel_key == "cloud":
            self._cloud_panel(message_card, namespace=namespace)
        elif panel_key == "new_op":
            self._new_op_panel(message_card)
        elif panel_key == "opensource":
            self._opensource_panel(message_card)
        elif panel_key == "package_op_by_release_branch":
            self._package_op_by_release_branch(message_card)
        else:
            self._default_panel(message_card)
        return message_card

    def _make_cache(self, cloud_env, image_repo):
        option.CloudEnv(self.job_id, self.open_id).cache_value(cloud_env)
        option.ImageRepo(self.job_id, self.open_id).cache_value(image_repo)
        option.ImageRepo(self.job_id, self.open_id).cache_list()
        option.CloudEnv(self.job_id, self.open_id).cache_list()

    def _cache_release_version(self):
        branches = common.all_release_branch()
        app.logger.info(f"cache branches: {branches.keys()}")
        option.OpenSourceBranch(self.job_id, self.open_id, option_list=branches.get("tapdata")).cache_list()
        option.EnterpriseBranch(self.job_id, self.open_id, option_list=branches.get("tapdata_enterprise")).cache_list()
        option.FrontendBranch(self.job_id, self.open_id, option_list=branches.get("tapdata_enterprise_web")).cache_list()
        option.ConnectorsBranch(self.job_id, self.open_id, option_list=branches.get("tapdata_connectors")).cache_list()
        option.ConnectorsEnterpriseBranch(self.job_id, self.open_id,
                                          option_list=branches.get("tapdata_connectors_enterprise")) .cache_list()
        option.LicenseBranch(self.job_id, self.open_id, option_list=branches.get("tapdata_license")).cache_list()

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        panel_key = "default"
        ALI_REPO = "registry.cn-hongkong.aliyuncs.com"
        namespace = None
        if len(args) > 0 and args[0] == "云版":
            panel_key = "cloud"
            if len(args) > 1 and args[1] == "测试环境":
                header = "云版-测试环境-打包并更新"
                namespace = "gcp-test"
                cloud_type = "gcp"
                self._make_cache(namespace, ALI_REPO)
            elif len(args) > 1 and args[1] in ["Net生产环境"]:
                header = "GCP-云版-Net环境-打包并更新"
                cloud_type = "gcp"
                namespace = "gcp-net"
                self._make_cache(namespace, ALI_REPO)
            elif len(args) > 1 and args[1] in ["GCP灰度环境"]:
                header = "GCP-云版-灰度环境-打包并更新"
                cloud_type = "gcp"
                namespace = "gcp-gray"
                self._make_cache(namespace, ALI_REPO)
            else:
                header = "云版-研发环境-打包并更新"
                namespace = "gcp-dev"
                cloud_type = "gcp"
                self._make_cache(namespace, ALI_REPO)
            # 格式化按钮的command
            command = f"buildCloud job_id={self.job_id} | " \
                      f"Watchcloudupdate cloud={cloud_type} namespace={namespace} | " \
                      "clear"
            trigger_div = trigger_button(
                "开始",
                command=command
            )
        elif len(args) > 0 and args[0] == "新分支管理模式":
            header = "企业版-打包并更新"
            panel_key = "new_op"
            trigger_div = trigger_button(
                "开始",
                command=f"watchOpNew job_id={self.job_id} | clear")
        elif len(args) > 0 and args[0] == "开源版":
            header = "开源版-打包并更新"
            panel_key = "opensource"
            trigger_div = trigger_button(
                "开始",
                command=f"watchOpensource job_id={self.job_id} | clear")
        elif len(args) > 0 and args[0] == "按发布分支打包":
            header = "企业版-打包并更新"
            panel_key = "package_op_by_release_branch"
            self._cache_release_version()
            trigger_div = trigger_button(
                "开始",
                command=f"watchOpNew job_id={self.job_id} | clear")
        else:
            header = "企业版-打包并更新"
            trigger_div = trigger_button(
                "开始",
                command=f"buildOp job_id={self.job_id} | watchDeployment | clear")
        message_card = self.default_handler(header,
                                            panel_key,
                                            namespace=namespace)
        message_card.add_element(trigger_div)
        return message_card.to_dict(), True
