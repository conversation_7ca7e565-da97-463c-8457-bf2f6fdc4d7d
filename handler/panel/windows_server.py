"""
windows_server.py 命令

@author: 
@date: 2023.01.30
"""

import os
import time
import traceback

from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc


@CommandParser.register
class Windows_server(Handler):
    command = "Windows云服务器地址||青云Win机器||T1使用的Windows机器||Win云服务器地址"
    check_template = [
        # 填写参数规则
    ]
    message_type = "text"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "windows_server.py",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        text = "云 Windows 服务器, 配置 8C 8G, 远程登录信息: IP: **************, 端口为默认端口 3389, 用户名: administrator, 密码为 Gotapd8!, 请按照自己的平台选择合适的 Windows 远程桌面客户端进行登录使用"
        return text, True