"""
test_report 命令

@author: 
@date: 2023.05.24
"""

import os
import time
import pymongo
import traceback
from dotenv import load_dotenv, find_dotenv

load_dotenv(find_dotenv())

from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, option


@CommandParser.register
class Getreportvisual(Handler):

    command = "性能测试历史报告||性能测试报告||性能测试报告比对"
    check_template = [
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "test_report",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    @staticmethod
    def mongo_collection(db: str = os.getenv("MONGODB_DATABASE"),
                         collection: str = "performance_test_cron_report_persistence"):
        return pymongo.MongoClient(os.getenv("MONGODB_URI"))[db][collection]

    @property
    def _all_processor_types(self):

        return self.mongo_collection().distinct("processor_type")

    @property
    def _all_package_versions(self):

        return self.mongo_collection().distinct("package_version")

    @property
    def _all_env_versions(self):

        return self.mongo_collection().distinct("env_version")

    @property
    def _all_test_cases(self):

        return self.mongo_collection().distinct("test_case_name")

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        config = mc.Config()
        header = mc.Header("查看历史/当天性能测试报告")
        specify_environment_version = mc.make_select_div("指定环境版本(必选)",
                                                         self._all_env_versions,
                                                         "specify_environment_version",
                                                         initial_option=f"{len(self._all_env_versions)}")

        specify_test_case = mc.make_select_div("指定测试用例(必选)",
                                               self._all_test_cases,
                                               "specify_test_case",
                                               initial_option="1")

        specify_package_version = mc.make_select_div("指定包版本(必选)",
                                                     self._all_package_versions,
                                                     "specify_package_version",
                                                     initial_option=f"{len(self._all_package_version)}")

        compare_package_version = mc.make_select_div("需要对比的包版本(可选)",
                                                     self._all_package_versions,
                                                     "compare_package_version")
        # specify_environment_option,  specify_package_option, specify_case_option = \
        #     option.Option(open_id).find_option_value_in_list(specify_environment_version,
        #                                                      specify_package_version,
        #                                                      specify_test_case)
        # specify_res = list(self.mongo_client(os.getenv("MONGODB_DATABASE"))["test_report_persistence"].find({
        #     "env_version": specify_environment_option,
        #     "package_version": specify_package_option,
        #     "test_case_name": specify_case_option
        # }))[-1]
        get_report_button = mc.single_button("获取测试报告", command="get_report")
        message_card = mc.MessageCard(header, config)
        message_card.add_element(specify_environment_version,
                                 specify_test_case,
                                 specify_package_version,
                                 compare_package_version,
                                 get_report_button)
        return message_card.to_dict(), True
