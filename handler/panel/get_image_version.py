"""
get_image_version 命令

@author: 
@date: 2023.04.28
"""
import json
import os
import time
import traceback

from utils.init import app
from utils.message_card import trigger_button
from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, api, option
from utils.api import Coding
from utils import common


@CommandParser.register
class Get_image_version(Handler):
    command = "get_image_version"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "get_image_version",  # 命令示例
        "选项": "列出component所有的tag",  # 填写选项和说明
    }
    except_cache = False

    _image_repo_map = {
        "qingcloud": {
            "registry": "dockerhub.qingcloud.com",
            "component": {
                "dfs-tcm": "tapdata",
                "dfs-tm-java": "tapdata",
                "dfs-console": "tapdata",
                "dfs-gateway": "tapdata",
                "dfs-flow-engine": "tapdata",
            },
            "login": "docker login -u Jerry -p Gotapd8! dockerhub.qingcloud.com",
        },  # 青云
        "aliCloud-Beijing": {
            "registry": "registry.cn-beijing.aliyuncs.com",
            "component": {
                "dfs-tcm": "tapdata",
                "dfs-tm-java": "tapdata",
                "dfs-console": "tapdata",
                "dfs-gateway": "tapdata",
                "dfs-flow-engine": "tapdata",
            },
            "login": "docker login --username=sysadmin@1809821306098986 registry.cn-beijing.aliyuncs.com -p Gotapd8!",
        },  # 阿里云 北京
        "coding": {
            "registry": "tapdata-docker.pkg.coding.net",
            "component": {
                "dfs-flow-engine": "dfs/flow-engine",
                "dfs-tm-java": "tapdata/tapdata",
                "dfs-tcm": "tapdata/tapdata",
                "dfs-console": "tapdata/tapdata",
                "dfs-gateway": "tapdata/tapdata",
            },
            "login": "docker login -u tapdata-1668675309097 -p 7548670226e4ab592246dd0fd11ae96b3a6104b1 tapdata-docker.pkg.coding.net"
        },  # Coding
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "source_cloud": "qingcloud",
            "component": "",
        }
        self.concurrency_limit = True

    def format_package_version(self, package_version_list):
        package_map = {}
        for package_version in package_version_list:
            package_map[package_version["CreatedAt"]] = package_version["Version"]
        sorted_timestamp = sorted(package_map.keys())
        version_list = []
        for s in sorted_timestamp[::-1]:
            version_list.append(package_map[s])
        return version_list

    def _check_cloud_type(self, cloud_type):
        if not isinstance(cloud_type, str):
            raise Exception("镜像仓库名字必须为 str 类型")
        if cloud_type not in self._image_repo_map:
            raise Exception("不支持这个仓库")
        return cloud_type

    def _check_component_name(self, component):
        if not isinstance(component, str):
            raise Exception("组件名称必须为 str 类型")
        if component not in self._image_repo_map["qingcloud"]["component"]:
            app.logger.error(f"不支持该组件: {component}")
            raise Exception("不支持该组件")
        return component

    def _get_coding_tags(self, component):
        coding_component_info = self._image_repo_map["coding"]["component"][component]
        project_name, repo = coding_component_info.split("/")
        tags_version = Coding(project_name).describe_package_version(repo, component)
        return self.format_package_version(tags_version)

    def _get_image_tags(self, cloud_type, component):
        if cloud_type == "coding":
            return self._get_coding_tags(component)

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        """
        1. 找到源仓库的component对应的tag列表
            (1) 源仓库
            (2) component
            (3) tag列表
        2. 缓存tag列表
        3. 发送面板
        """
        # 1. 找到源仓库的component对应的tag列表
        source_cloud = self._check_cloud_type(self.magic_arg["source_cloud"])  # (1) 源仓库
        option.SourceCloudType(self.job_id, self.open_id).cache_value(source_cloud)
        component = self._check_component_name(self.magic_arg["component"])  # (2) component 组件
        option.Components(self.job_id, self.open_id).cache_value(component)
        # 2. 缓存tag列表
        package_version_list = self._get_image_tags(source_cloud, component)  # (3) tag列表
        image_tag_list = option.ImageVersion(self.job_id, self.open_id, option_list=package_version_list)
        # 3. 发送面板
        message_card = mc.MessageCard(header=mc.Header("从Coding同步flow-engine镜像"), config=mc.Config())
        package_selector = image_tag_list.make_select(placeholder="请选择")
        target_cloud_type = option.TargetCloudType(self.job_id, self.open_id).make_select()
        trigger_div = trigger_button(
            "开始",
            command=f"sync_image_repo job_id={self.job_id} | clear"
        )
        message_card.add_element(package_selector, target_cloud_type, trigger_div)
        print(json.dumps(message_card.to_dict(), indent=4))
        return message_card.to_dict(), True
