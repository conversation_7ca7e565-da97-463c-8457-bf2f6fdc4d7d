"""
release_agent_visual 命令

@author: 
@date: 2023.05.19
"""

import os
import time
import traceback

from utils.api import Coding
from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, option


@CommandParser.register
class Release_agent_visual(Handler):
    command = "release_agent_visual"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "release_agent_visual",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "env": "cloud-dev",
        }
        self.concurrency_limit = True

    def _agent_tags(self):
        agent_tags = Coding("dfs").describe_package_version("flow-engine", "dfs-flow-engine")
        package_map = {}
        for package_version in agent_tags:
            package_map[package_version["CreatedAt"]] = package_version["Version"]
        sorted_timestamp = sorted(package_map.keys())
        version_list = []
        for s in sorted_timestamp[::-1]:
            version_list.append(package_map[s])
        return version_list

    def _cloud_env_div(self):
        cloud_env_option = option.CloudEnv(self.job_id, self.open_id)
        cloud_env_option.cache_value(self.magic_arg["env"])
        cloud_env_option.cache_list()

    def agent_version_div(self):
        return option.ResourcePoll.make_select(self.job_id, self.open_id, self.magic_arg["env"])

    def _coding_agent_version(self):
        return Coding("dfs").formatted_package_version("flow-engine", "dfs-flow-engine")

    def get_agent_version(self):
        """根据云类型获取agent版本
        cloud-dev cloud-test cloud-uat为 self._coding_agent_version
        """
        if self.magic_arg["env"] in ["cloud-dev", "cloud-test", "cloud-uat"]:
            return self._coding_agent_version()
        else:
            # TODO: 云类型为其他时，需要调用其他接口获取agent版本
            return self._coding_agent_version()

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        self._cloud_env_div()
        agent_version = self.get_agent_version()
        agent_version_div = option.AgentVersion(self.job_id, self.open_id, option_list=agent_version).make_select()
        message = mc.MessageCard(header=mc.Header(f"发布Agent-{self.magic_arg['env']}"), config=mc.Config())
        message.add_element(agent_version_div)
        message.add_element(option.EnableGray(self.job_id, self.open_id).make_select())
        message.add_element(mc.Div(mc.Text("**选择资源池：**", tag_type=mc.TextType.lark_md)))
        for i in self.agent_version_div():
            message.add_element(i)
        release_button = mc.single_button("发布", command=f"release_agent job_id={self.job_id} | clear")
        release_action = mc.Action(layout="bisected")
        release_action.add_action(release_button)
        message.add_element(release_action)
        return message.to_dict(), True
