"""
RegisterConnector 命令

@author: 
@date: 2022.11.18
"""

import os
import time
import traceback

from utils.init import app
from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, common, option
from utils.session import Session


@CommandParser.register
class Registerconnector(Handler):
    command = "注册数据源"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "注册数据源",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }
    except_cache = False

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "env": "cloud-dev"
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        """
        1. 获取开源和闭源分支并缓存
        2. 获取数据源列表并缓存
        3. 设置仅编译connectors
        4. 发送面板
        """
        # 1. 获取开源和闭源分支
        opensource_branch, enterprise_branch = common.get_branch_threading('tapdata', 'tapdata-enterprise')
        opensource_div = option.OpenSourceBranch(self.job_id, self.open_id, option_list=opensource_branch).make_select()
        enterprise_div = option.EnterpriseBranch(self.job_id, self.open_id, option_list=enterprise_branch).make_select()
        # 2. 获取数据源列表
        connectors = common.get_connectors()
        saas_connectors = [f"saas/{c}" for c in common.get_saas_connectors()]
        connectors += saas_connectors
        connectors_div = option.Connectors(self.job_id, self.open_id, option_list=connectors).make_select()
        # 3. 设置仅编译connectors
        option.UpdateConsole(self.job_id, open_id).cache_value(t=2)
        option.UpdateTCM(self.job_id, open_id).cache_value(t=2)
        option.UpdateTM(self.job_id, open_id).cache_value(t=2)
        option.UpdateAgent(self.job_id, open_id).cache_value(t=2)
        option.UpdateConsole(self.job_id, open_id).cache_value(t=1)
        # 4. 发送面板
        env = self.magic_arg["env"]
        message_card = mc.MessageCard(mc.Header(f"数据源注册-云版-{env}"), mc.Config())
        command = f"register_connector job_id={self.job_id} env={env} | " \
                  f"watch_register | " \
                  "clear"
        trigger_button = mc.trigger_button("开始构建和注册",
                                           command
                                           )
        message_card.add_element(enterprise_div, opensource_div, mc.Hr(), connectors_div, trigger_button)
        return message_card.to_dict(), True
