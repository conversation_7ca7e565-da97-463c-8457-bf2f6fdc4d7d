"""
help 命令

@author: <PERSON>
@date: 2022.09.02
"""
import json

from utils.handler import Handler
from utils.schdule import CommandParser
from utils.message_card import MessageCard


@CommandParser.register
class Help(Handler):
    # TODO: command 这里不要调整, 在调度那里, 对这个字符串进行了引用
    command = "help||h"
    # TODO: command 这里不要调整, 在调度那里, 对这个字符串进行了引用

    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "help [命令]",  # 命令示例
        "选项": "显示帮助，help 接命令可以查看某个命令帮助",  # 填写选项和说明
    }

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        ts = [{
            "action_name": "企业版操作[已可用]",
            "buttons": [
                {"button_name": "按版本打包 [推荐]", "command": "打包并更新 按发布分支打包"},
                {"button_name": "打包并更新 版本<v3.5.4", "command": "打包并更新"},
                {"button_name": "打包并更新 版本>=v3.5.4", "command": "打包并更新 新分支管理模式"},
                {"button_name": "打包并发布 开源版", "command": "打包并更新 开源版"},
            ]
        }, {
            "action_name": "云版操作[已可用]",
            "buttons": [
                {"button_name": "更新云版研发环境", "command": "打包并更新 云版"},
                {"button_name": "更新云版测试环境", "command": "打包并更新 云版 测试环境"},
                {"button_name": "更新生产环境", "command": "打包并更新 云版 Net生产环境"},
                {"button_name": "更新灰度环境", "command": "打包并更新 云版 GCP灰度环境"},
            ]
        }, {
            "action_name": "镜像分发[已可用]",
            "buttons": [
                {"button_name": "从Coding同步Agent", "command": "sync_agent_image_panel"},
            ]
        }, {
            "action_name": "Agent发布[已可用]",
            "buttons": [
                {"button_name": "发布Agent到研发环境", "command": "release_agent_visual env=cloud-dev"},
                {"button_name": "发布Agent到测试环境", "command": "release_agent_visual env=cloud-test"},
                {"button_name": "发布Agent到NET生产/灰度环境", "command": "release_agent_visual env=cloud-net"},
            ]
        }]
        return MessageCard.from_dict("📜 操作面板", ts).to_dict(), True
