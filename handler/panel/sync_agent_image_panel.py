"""
sync_agent_image_watch 命令

@author: 
@date: 2023.08.25
"""
import json
import os
import time
import traceback

from utils.api import Coding
from utils.message_card import trigger_button
from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, option


@CommandParser.register
class Sync_agent_image_panel(Handler):
    command = "sync_agent_image_panel"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "sync_agent_image_watch",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        tags_version = Coding("dfs").describe_package_version("flow-engine", "dfs-flow-engine")
        tags = [tag["Version"] for tag in tags_version]
        tags_version_list = option.ImageVersion(self.job_id, self.open_id, option_list=tags)
        message_card = mc.MessageCard(header=mc.Header("从Coding同步flow-engine镜像"), config=mc.Config())
        agent_tag_selector = tags_version_list.make_select(placeholder="请选择")
        trigger_div = trigger_button(
            "开始",
            command=f"sync_agent_image job_id={self.job_id} | sync_agent_image_watch job_id={self.job_id} | clear"
        )
        message_card.add_element(agent_tag_selector, trigger_div)
        return message_card.to_dict(), True
