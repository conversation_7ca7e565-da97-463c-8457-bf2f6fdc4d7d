"""
clear 命令

@author: 
@date: 2022.11.11
"""
import os
import time
import traceback

from utils.connector import redis_cli
from utils.init import app
from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc


@CommandParser.register
class Clear(Handler):
    command = "clear"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "clear",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        """删除该Job的key"""
        spec_key = None
        if len(args) > 0:
            spec_key = args[0]
        user_cache = redis_cli.hgetall(self.open_id)
        for k, v in user_cache.items():
            key = k.decode("utf8")
            if key.startswith(f"{self.job_id}#{self.open_id}#") or (spec_key is not None and spec_key in key):
                for _ in range(3):
                    redis_cli.hdel(self.open_id, key)
                    if redis_cli.hget(self.open_id, key) is None:
                        break

        return None, True
