"""
cloud_error 命令

@author: 
@date: 2023.02.02
"""

from utils.schdule import CommandParser
from utils.handler import Handler
from utils.common import utc2local

import bson

import pymongo, datetime, requests

from utils.common import get_help


@CommandParser.register
class Cloud_error(Handler):
    command = "云版任务报错||云版任务报错分析||云版任务报错分析, 用户为:, 任务id为:||云版任务报错分析, 用户为:云版任务报错分析, 用户为:, 任务名为:||云版任务报错分析, 任务名为||云版任务报错分析原理"
    check_template = [
        # 填写参数规则
    ]
    message_type = "text"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "cloud_error",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        command = kwargs.get("command")
        if command == "云版任务报错分析原理":
            text = "分析过程首先会根据用户输入, 获取 用户 ID, 任务 ID, 任务名, 并组合搜索出 7 天内的报错任务(上限 10 个), 随后会进行一系列查询, 包括:\n"
            text += "1. 在 Coding 中查找自动转换的工单, 并查找评论, 如果评论中存在一 客户: 开头的评论, 采用这个评论作为方案\n"
            text += "2. 针对上述工单, 进行状态检查, 如果状态不为 待处理/已拒绝/挂起, 并且处理人不为 Erin, 将会回复 这确认为产品 BUG, 已经安排给 $研发 处理, 目前的状态是: $状态\n"
            text += "3. 如果都未能获取, 会尝试寻找错误堆栈, 并将其与 wiki 中的产品错误手册进行比对, 如果比对成功, 输出产品错误手册内容\n"
            text += "4. 最后如果都没能匹配, 会用错误堆栈的部分数据, 尝试询问 ChatGPT 获取答案"
            return text, True

        user = None
        task_id = None
        task_name = None
        user_splits = ["用户为:", "用户:", "用户名为:", "用户名:", "用户名", "用户"]
        for user_split in user_splits:
            if user_split in command:
                user = command.split(user_split)[1].split(",")[0].lstrip(":").strip()
                break

        task_id_splits = ["taskId为", "task id 为", "taskId", "task_id", "任务ID:", "任务ID为", "任务id为", "任务Id为", "任务ID", "任务 ID 为:", "任务 ID 为", "任务 id 为"]
        for task_id_split in task_id_splits:
            if task_id_split.lower() in command.lower():
                task_id = command.split(task_id_split)[1].split(",")[0].lstrip(":").strip()
                break

        task_name_splits = ["任务为:", "任务名为:", "任务:", "任务名:", "任务为", "任务名为"]
        for task_name_split in task_name_splits:
            if task_name_split in command:
                task_name = command.split(task_name_split)[1].split(",")[0].lstrip(":").strip()

        if user is not None:
            if "[" in user and "]" in user:
                user = user.split("[")[1].split("]")[0]

        last_seconds = 86400 * 7
        text = get_help(user=user, task_id=task_id, task_name=task_name, last_seconds=last_seconds)
        return text, True
