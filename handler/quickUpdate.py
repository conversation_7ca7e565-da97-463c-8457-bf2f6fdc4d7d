"""
autoupdate 命令

@author: 
@date: 2023.01.13
"""

import os
import time
import traceback

from utils.schdule import CommandParser, Context, Schedule
from utils.handler import Handler, Letter
from utils import message_card as mc, common, option
from utils.session import Session


@CommandParser.register
class Quickupdate(Handler):
    command = "quickupdate"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "quickupdate [cloud/op] [env or namespace]",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def _cache_branch(self, branch, cla):
        """预设分支"""
        branch_option = cla(self.job_id, self.open_id, option_list=[branch])
        branch_option.cache_list()
        branch_option.cache_value(t=branch)

    def _cache_cloud_option(self, namespace):
        """设置云版打包选项，更新哪个环境"""
        cloud_env = option.CloudEnv(self.job_id, self.open_id)
        cloud_env.cache_list()
        cloud_env.cache_value(namespace)

    def _cache_op_option(self, deployment):
        """设置OP打包选项"""
        deployment = option.Deployment(self.job_id, self.open_id, option_list=[deployment])
        deployment.cache_list()
        deployment.cache_value(deployment)
        push_to_qingcloud = option.PushToQingcloud(self.job_id, self.open_id)
        push_to_qingcloud.cache_list()
        push_to_qingcloud.cache_value(1)
        push_to_coding = option.PushToCoding(self.job_id, self.open_id)
        push_to_coding.cache_list()
        push_to_coding.cache_value(1)
        push_to_private_repo = option.PushToPrivateRepo(self.job_id, self.open_id)
        push_to_private_repo.cache_list()
        push_to_private_repo.cache_value(2)

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        """
        判断是OP版还是cloud版
        1、获取最新开发分支
        2、设置缓存信息
        3、开启流程
        """
        if args[0] == "cloud":
            # Cloud
            namespace = args[1]  # 获取命名空间
            # 1、获取最新开发分支
            opensource_branch, enterprise_branch, web_branch, cloud_branch = common.get_newest_branch("^dfs")
            # 2、设置缓存信息
            for branch_info in [
                [opensource_branch, option.OpenSourceBranch],
                [enterprise_branch, option.EnterpriseBranch],
                [web_branch, option.FrontendBranch],
                [cloud_branch, option.CloudBranch],
            ]:
                self._cache_branch(*branch_info)
            self._cache_cloud_option(namespace)
            # 3、开启流程
            command = f"buildCloud job_id={self.job_id} | " \
                      f"Watchcloudupdate cloud=qingcloud namespace={namespace} | " \
                      "clear"
            data = {
                "command": command,
                "open_id": open_id,
                "chat_type": chat_type,
                "quite": True
            }
            message = common.field_list_message_card("云版研发环境自动更新", "green",
                                                     开源版分支=opensource_branch,
                                                     企业版分支=enterprise_branch,
                                                     前端分支=web_branch,
                                                     云版分支=cloud_branch,
                                                     构建Console=True,
                                                     构建TCM=True,
                                                     构建TM_Java=True,
                                                     构建Agent_TapdataAgent=False,
                                                     更新数据源=False)
        else:
            deployment = args[1]
            # 1、获取最新开发分支
            opensource_branch, enterprise_branch, web_branch, _ = common.get_newest_branch("develop")
            # 2、设置缓存信息
            for branch_info in [
                [opensource_branch, option.OpenSourceBranch],
                [enterprise_branch, option.EnterpriseBranch],
                [web_branch, option.FrontendBranch],
            ]:
                self._cache_branch(*branch_info)
            self._cache_op_option(deployment)
            # 3、开启流程
            command = f"buildOp job_id={self.job_id} | watchDeployment | clear"
            data = {
                "command": command,
                "open_id": open_id,
                "chat_type": chat_type,
                "quite": True
            }
            message = common.field_list_message_card("OP版研发环境自动更新", "green",
                                                     开源版分支=opensource_branch,
                                                     企业版分支=enterprise_branch,
                                                     前端分支=web_branch)
        context = Context(**data)
        Schedule().register(context)
        return message.to_dict(), True
