"""
cronrun 命令

@author: 
@date: 2022.11.11
"""
import json
import os
import re
import time
import traceback
import uuid

from utils.init import app
from utils.schdule import CommandParser, Context, Schedule
from utils.handler import Handler, Letter
from utils import message_card as mc, common
from utils.session import Session


@CommandParser.register
class Fastupdate(Handler):
    command = "云版自动更新"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "云版自动更新",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        opensource_branch, enterprise_branch, frontend_branch, cloud_branch = common.get_branch_threading(
            'tapdata', 'tapdata-enterprise', 'tapdata-enterprise-web', 'tapdata-cloud')
        dfs_branches = []
        for b in cloud_branch:
            r = re.match(r"dfs-v[0-9]+.[0-9]+", b)
            if r is None:
                continue
            if len(r.group()) != len(b):
                continue
            dfs_branches.append(b)
        branch = common.find_last_branch(dfs_branches, version_count=1)[0]
        app.logger.info(f"target branch: {branch}")
        ret = {
            "opensource_branch": common.make_cache(opensource_branch),
            "enterprise_branch": common.make_cache(enterprise_branch),
            "frontend_branch": common.make_cache(frontend_branch),
            "cloud_branch": common.make_cache(cloud_branch),
        }
        open_index = common.find_element_index_in_list(branch, opensource_branch) + 1
        enter_index = common.find_element_index_in_list(branch, enterprise_branch) + 1
        front_index = common.find_element_index_in_list(branch, frontend_branch) + 1
        cloud_index = common.find_element_index_in_list(branch, cloud_branch) + 1
        if open_index == 0 or enter_index == 0 or front_index == 0 or cloud_index == 0:
            app.logger.info(f"branches: {open_index}, {enter_index}, {front_index}, {cloud_index}")
            return "Skip CICD because branches not found", False
        for key, value in ret.items():
            Session().add_session("webhook", key, json.dumps(value))
        Session().add_session("webhook", "cache opensource_branch", open_index)
        Session().add_session("webhook", "cache enterprise_branch", enter_index)
        Session().add_session("webhook", "cache frontend_branch", front_index)
        Session().add_session("webhook", "cache cloud_branch", cloud_index)
        # 设置打包参数
        Session().add_session(open_id="webhook", key="cache is_build_frontend", value="2")
        Session().add_session(open_id="webhook", key="cache is_build_tcm", value="2")
        Session().add_session(open_id="webhook", key="cache is_build_tm_java", value="2")
        Session().add_session(open_id="webhook", key="cache is_build_agent", value="2")
        Session().add_session(open_id="webhook", key="cache is_tapdata_agent", value="2")
        Session().add_session(open_id="webhook", key="cache is_register_connector", value="1")

        # 打包（除connector外）-> 等待打包完成 -> 并发部署各个组件 -> 等待部署完成 -> 设置缓存（只打包connector）-> 打包 -> 等待打包完成
        command = f"build id={str(uuid.uuid4())} is_cloud=True quite=True | " \
                  f"watch cicd is_cloud=True quite=True image_repo=tapdata-docker.pkg.coding.net/tapdata/tapdata | " \
                  f"deploy update dfs-tm-java concurrency=True is_cloud=True namespace=cloud-dev quite=True % " \
                  f"deploy update dfs-agent concurrency=True is_cloud=True namespace=cloud-dev quite=True % " \
                  f"deploy update dfs-console concurrency=True is_cloud=True namespace=cloud-dev quite=True % " \
                  f"deploy update dfs-tcm concurrency=True is_cloud=True namespace=cloud-dev quite=True %" \
                  f"deploy update dfs-tapdata-agent concurrency=True is_cloud=True namespace=cloud-dev quite=True | " \
                  f"watch env_update namespace=cloud-dev is_cloud=True | " \
                  f"cache is_build_frontend 1 % " \
                  f"cache is_build_tcm 1 % " \
                  f"cache is_build_tm_java 1 % " \
                  f"cache is_build_agent 1 % " \
                  f"cache is_tapdata_agent 1 % " \
                  f"cache is_register_connector 2 | " \
                  f"build id={str(uuid.uuid4())} is_cloud=True quite=True" \
                  f"watch cicd is_cloud=True quite=True"
        context = Context(command=command, open_id="webhook", chat_type="p2p")
        Schedule().register(context)
        return None, True
