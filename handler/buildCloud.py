"""
buildCloud 命令

@author: <PERSON>
@date: 2023.05.09
"""
import json
import os
import time
import traceback

from utils.api import GithubApi
from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, option, common


@CommandParser.register
class Buildcloud(Handler):
    command = "buildCloud"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "buildCloud",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def format_component(self, update_console, update_tcm, update_tm, update_agent, update_tapdata_agent):
        component = []
        if update_console:
            component.append("console")
        if update_tm:
            component.append("tm-java")
        if update_tcm:
            component.append("tcm")
        if update_agent:
            component.append("agent")
        if update_tapdata_agent:
            component.append("tapdata_agent")
        return ','.join(component)

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        """
        1. 获取选项信息
        2. 选项信息合法化判断
        3. 格式化选项信息
        4. 触发构建
        5. 构建信息发送
        """
        # 1. 获取选项信息
        enterprise_branch = option.EnterpriseBranch(self.job_id, open_id).get_option()
        opensource_branch = option.OpenSourceBranch(self.job_id, open_id).get_option()  # 开源版分支
        frontend_branch = option.FrontendBranch(self.job_id, open_id).get_option()  # 前端分支
        tapflow_branch = option.TapFlowBranch(self.job_id, open_id).get_option()  # TapFlow分支
        cloud_branch = option.CloudBranch(self.job_id, open_id).get_option()  # 云版分支
        update_console = option.UpdateConsole(self.job_id, open_id).get_option()  # 构建前端
        update_tapflow = option.UpdateTapFlow(self.job_id, open_id).get_option()  # 构建TapFlow
        update_tm = option.UpdateTM(self.job_id, open_id).get_option()  # 构建TM_Java
        update_agent = option.UpdateAgent(self.job_id, open_id).get_option()  # 构建Agent/TapdataAgent
        update_ticket = option.UpdateTicket(self.job_id, open_id).get_option()  # 更新工单系统
        env = option.CloudEnv(self.job_id, open_id).get_option()
        # update_tcm = False if env in ["gcp-dev", "gcp-test"] else option.UpdateTCM(self.job_id, open_id).get_option()  # 构建TCM
        update_tcm = False

        # 2. 选项信息合法化判断
        if enterprise_branch is None or opensource_branch is None or frontend_branch is None or cloud_branch is None:
            return mc.make_error_branch().to_dict(), False
        # 3. 格式化选项信息
        json_inputs = {
            "FRONTEND_BRANCH": frontend_branch,
            "TAPFLOW_BRANCH": tapflow_branch,
            "OPENSOURCE_BRANCH": opensource_branch,
            "ENTERPRISE_BRANCH": enterprise_branch,
            "DFS_BRANCH": cloud_branch,
            "BUILD-FRONTEND": update_console,
            "BUILD-TCM": update_tcm,
            "BUILD-TM-JAVA": update_tm,
            "BUILD-AGENT": update_agent,
            "BUILD-TICKET": update_ticket,
            "BUILD-TAPFLOW": update_tapflow,
            "ENV": env,
            "JOB_ID": self.job_id,
        }
        # 4. 触发构建
        owner = os.getenv("OWNER")
        repo = "tapdata-application"
        github_obj = GithubApi(owner, repo)
        res = github_obj.trigger_by_workflow_dispatch(
            "84064988",
            "main",
            PARAMS_AS_JSON=json.dumps(json_inputs)
        )
        if res:
            header = "🎉 构建任务创建成功，执行成功后会通知你哦"
            color = "green"
        else:
            header = "😭 构建任务创建失败，请联系Jerry处理"
            color = "red"
        message_card = common.field_list_message_card(
            header=header,
            color=color,
            企业版分支=enterprise_branch,
            开源版分支=opensource_branch,
            前端分支=frontend_branch,
            云版分支=cloud_branch,
            构建前端=update_console,
            构建TM_Java_and_TM=update_tm,
            构建TapFlow=update_tapflow,
            构建Agent=update_agent,
            更新数据源=False
        )
        return message_card.to_dict(), True

