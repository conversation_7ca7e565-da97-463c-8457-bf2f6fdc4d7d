"""
list_ds 命令

@author: 
@date: 2022.12.09
"""

import os
import time
import traceback

from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc
from utils.common import field_list_message_card
import pymongo


@CommandParser.register
class Cloud_vip(Handler):
    command = "标记云版用户||是云版 VIP 用户||是云版普通用户||是云版付费用户||商家名字是"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "cloud_vip",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True


    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        user_sign = str(kwargs.get("command").split("是云版")[0]).strip()
        customer_name = str(kwargs.get("command").split("名字是")[1]).strip()

        client = pymongo.MongoClient("mongodb://root:Gotapd8!@***************:32550/cloud?authSource=admin")
        db = client["cloud"]
        collection = db["M_User"]
        user = collection.find_one({"username": user_sign})
        if user is None:
            user = collection.find_one({"phone": user_sign})
        if user is None:
            user = collection.find_one({"nickname": user_sign})
        if user is None:
            user = collection.find_one({"email": user_sign})
        if user is None:
            message = field_list_message_card(
                header="{} 不是云版用户".format(user_sign),
                color="red",
                输入格式提示="wechat_xxx 是云版 VIP 用户, 商家名字是 xxx"
            )
            return message.to_dict(), True

        customer_type = "user"
        c = kwargs.get("command").strip()
        if "vip" in c.lower() or "付费用户" in c.lower():
            customer_type = "vip"

        user["customer_type"] = customer_type
        user["customer_name"] = customer_name
        collection.replace_one({"_id": user["_id"]}, user)
        message = field_list_message_card(
            header="云版用户: {} 完成信息录入".format(user_sign),
            color="blue",
            is_short=True,
            商家名字=customer_type,
            用户类型=customer_name
        )
        return message.to_dict(), True
