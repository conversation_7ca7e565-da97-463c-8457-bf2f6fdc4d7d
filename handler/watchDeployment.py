"""
watchDeployment 命令

@author: 
@date: 2023.05.10
"""

import os
import time

from lib.api import send_text
from utils.api import GithubApi, KubernetesCommand
from utils.init import app
from utils.schdule import CommandParser
from utils.handler import Handler
from utils import message_card as mc, common, option
from utils.shell import Shell


@CommandParser.register
class Watchdeployment(Handler):
    command = "watchDeployment"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "watchDeployment",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "deployment": "",
            "cloud": "gcp",
            "namespace": "dev",
        }
        self.concurrency_limit = True

    def _format_kubeconfig(self):
        if self.magic_arg.get("cloud") == "qingcloud":
            return f"--kubeconfig /conf/qingcloud/kubeconfig"

    def get_deployment_image(self):
        """获取deployment镜像"""
        deployment = option.Deployment(self.job_id, self.open_id).get_option()
        namespace = self.magic_arg['namespace']
        out, err, return_code = KubernetesCommand(self.magic_arg["cloud"]).get_deployment_image(deployment, namespace)
        print(out, err, return_code)
        if return_code != 0:
            message = common.field_list_message_card(
                header="监听镜像更新失败",
                color="red",
                is_short=False,
                处理方式="获取deployment镜像失败",
                output=out,
                error=err,
            )
            return message.to_dict(), False
        return out

    def get_service_ip(self):
        """获取deployment对应的pod的IP"""
        deployment = option.Deployment(self.job_id, self.open_id).get_option()
        namespace = self.magic_arg['namespace']
        out, err, return_code = KubernetesCommand(self.magic_arg["cloud"]).get_lb_ip(namespace, deployment.replace("-tapdaas", ""))
        if return_code != 0:
            message = common.field_list_message_card(
                header="监听镜像更新失败",
                color="red",
                is_short=False,
                处理方式="获取deployment IP失败",
                output=out,
                error=err,
            )
            return message.to_dict(), False
        return out

    def get_service_eip(self):
        """获取service对应的eip"""
        deployment = option.Deployment(self.job_id, self.open_id).get_option()
        namespace = self.magic_arg['namespace']
        out, err, return_code = KubernetesCommand(self.magic_arg["cloud"]).get_lb_ip(deployment.replace("-tapdaas", ""), namespace)
        if return_code != 0:
            message = common.field_list_message_card(
                header="监听镜像更新失败",
                color="red",
                is_short=False,
                处理方式="获取deployment EIP失败",
                output=out,
                error=err,
            )
            return message.to_dict(), False
        return out.replace('"', "").replace("\n", "")

    def get_service_port(self):
        """获取deployment对应的pod的port"""
        deployment = option.Deployment(self.job_id, self.open_id).get_option()
        namespace = self.magic_arg['namespace']
        out, err, return_code = KubernetesCommand(self.magic_arg["cloud"]).get_svc_port(
            namespace, deployment.replace("-tapdaas", ""), 3030)
        if return_code != 0:
            message = common.field_list_message_card(
                header="监听镜像更新失败",
                color="red",
                is_short=False,
                处理方式="获取deployment PORT失败",
                output=out,
                error=err,
            )
            return message.to_dict(), False
        return out

    def check_service_available(self, ip, port):
        command = f"curl http://{ip}:{port}/health"
        return Shell().run(command, shell=True)

    class RunnerConclusion:
        success = "success"
        failure = "failure"
        cancelled = "cancelled"
        start_failed = "start_failed"

    def _get_runner_id(self):
        owner, repo = os.getenv("OWNER"), os.getenv("TRIGGER_REPO")
        github_obj = GithubApi(owner, repo)
        runner_id, detail_url = github_obj.find_runner_id(self.job_id)
        return runner_id, detail_url

    def _get_runner_info(self, runner_id):
        owner = os.getenv("OWNER")
        repo = os.getenv("TRIGGER_REPO")
        github_obj = GithubApi(owner, repo)
        res = github_obj.get_runner_info(runner_id)
        if res["status"] == "completed":
            if res.get("conclusion", None) == self.RunnerConclusion.success:
                # if option.PushToQingcloud(self.job_id, self.open_id).get_option():
                #     image_repo = "dockerhub.qingcloud.com/tapdata"
                # elif option.PushToCoding(self.job_id, self.open_id).get_option():
                #     image_repo = "tapdata-docker.pkg.coding.net/tapdata/enterprise"
                # elif option.PushToPrivateRepo(self.job_id, self.open_id).get_option():
                #     image_repo = "10.96.227.108:5000/tapdata"
                # else:
                image_repo = "asia-docker.pkg.dev/crypto-reality-377106/tapdata"
                try:
                    package_image_tag, current_branch, opensource_branch, frontend_branch, tag = \
                        github_obj.runner_branch_info_enterprise(runner_id, image_repo=image_repo)
                except TypeError as e:
                    app.logger.error(f"获取runner分支信息失败: {e}")
                    return None, True
                return (package_image_tag, current_branch, opensource_branch, frontend_branch, tag), True
            elif res.get("conclusion", None) == self.RunnerConclusion.cancelled:
                return self.RunnerConclusion.cancelled, True
            elif res.get("conclusion", None) == self.RunnerConclusion.failure:
                return self.RunnerConclusion.failure, True
        return None, None

    def job_status(self):
        """
        监听任务创建，启动和结束，最终返回消息和镜像tag

        1. 根据job_id获取runner_id
        2. 发送任务已经运行的通知
        3. 监听runner运行
        4. 发送runner状态
        """
        # 1. 根据job_id获取runner_id
        runner_id, detail_url = common.get_runner_id(self.job_id)
        # 2. 发送任务已经运行的通知
        if runner_id is None or detail_url is None:  # 获取不到任务信息，任务创建失败
            return self.RunnerConclusion.start_failed, False
        else:  # 发送任务成功创建的信息
            message_card = mc.make_workflow_scheduling_notes(runner_id, detail_url)
            send_text(self.message_type, self.open_id, message_card.to_dict(), self.chat_type)
        # 3. 监听runner运行
        tags, status = None, False
        for t in range(120):
            app.logger.info("waiting for completed, %s times / %s times" % (t + 1, 120))
            tags, status = self._get_runner_info(runner_id)
            if tags is None and status:
                message = common.field_list_message_card(
                    header="监听构建任务失败，自行点击链接查看进度",
                    color="yellow",
                    is_short=False,
                    处理方式="遇到了一些错误，自定查看进度并告知Jerry",
                )
                return message, None
            app.logger.info(f"[{self.job_id}]workflow status is {status}, tags is {tags}")
            if status:
                break
            time.sleep(30)
        # 4. 发送runner状态
        package_image_tag = None
        if status and tags not in [self.RunnerConclusion.failure, self.RunnerConclusion.cancelled]:
            package_image_tag, current_branch, opensource_branch, frontend_branch, tag = tags
            kwargs = {
                "header": "🎉 构建任务执行成功",
                "镜像tag": package_image_tag,
                "企业版分支": current_branch,
                "开源版分支": opensource_branch,
                "前端分支": frontend_branch,
            }
            package_info = package_image_tag.split("/")[-1].replace(":", "-")
            tag = package_image_tag.split(":")[-1]
            mode = option.FrontendMode(self.job_id, self.open_id).get_option()
            print("package_tar", option.IsPackageTar(self.job_id, self.open_id).get_option())
            if option.IsPackageTar(self.job_id, self.open_id).get_option():
                print("执行了这里")
                kwargs.update({
                    "压缩包下载链接":
                        f"http://fileserver.tapdata.io/#/gz/{mode}-tapdata-enterprise-{tag}.tar.gz"
                })
            if option.IsDockerSave(self.job_id, self.open_id).get_option():
                kwargs.update({
                    "镜像压缩包下载链接":
                        f"http://fileserver.tapdata.io/#/docker-gz/tapdata-docker-{package_info}.tar.gz"
                })
            message = common.field_list_message_card(**kwargs)
        elif status and tags == self.RunnerConclusion.cancelled:
            message = common.field_list_message_card(
                header="📣 流程被cancelled掉了",
                color="red",
                is_short=False,
                处理方式="自行查看Github流程执行情况"
            )
        elif status and tags == self.RunnerConclusion.failure:
            message = common.field_list_message_card(
                header="😭 流程执行失败了",
                color="red",
                is_short=False,
                处理方式="自行查看Github流程执行情况"
            )
        elif not status and tags == self.RunnerConclusion.start_failed:
            message = mc.workflow_start_error()
        else:
            message = common.field_list_message_card(
                header="😭 流程执行超时",
                color="red",
                处理方式="自行查看Github执行情况并联系Jerry处理"
            )
        return message, package_image_tag

    def watch_deployment_image_update(self, package_image_tag):
        """查看deployment的镜像是否已经更新"""
        ok = False
        for _ in range(4):  # 由于cicd流程已经更新镜像，这里最多等待40秒
            image = self.get_deployment_image()
            app.logger.info(f"[{self.job_id}]image: deployment: {image}, cicd: {package_image_tag}")
            if image == package_image_tag:
                ok = True
                break
            time.sleep(10)
        return ok

    def watch_server_available(self, ip, port):
        """查看服务是否可用"""
        ok = False
        for _ in range(3):  # 重试3次，间隔10秒
            if self.check_service_available(ip, port) == 0:
                ok = True
                break
            time.sleep(10)
        return ok

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        """
        1. 监听CICD JOB是否创建和完成
        2. 查看镜像是否已经更新
        3. 查看rollout status
        4. 查看服务是否可用
        """
        self.open_id = open_id
        self.chat_type = chat_type
        # 1. 监听CICD JOB是否创建和完成
        message, package_image_tag = self.job_status()  # 返回消息体和镜像TAG
        if package_image_tag is None:
            return message.to_dict(), False
        send_text(self.message_type, self.open_id, message.to_dict(), self.chat_type)
        message = common.field_list_message_card(
            header="构建任务运行成功",
            color="green",
            is_short=False,
            运行状态="正在更新服务，请稍等",
        )
        send_text(self.message_type, self.open_id, message.to_dict(), self.chat_type)
        print("package_image_tag: ", package_image_tag)
        deployment = option.Deployment(self.job_id, self.open_id).get_option()
        print("deployment: ", deployment)
        if deployment is None or deployment == "无":
            message = common.field_list_message_card(
                header="任务结束",
                color="green",
                is_short=False,
                具体原因="选择了不更新服务",
            )
            return message.to_dict(), False
        message = common.field_list_message_card(
            header="服务更新成功",
            color="green",
            is_short=False,
            detail="稍等片刻方可访问服务",
        )
        return message.to_dict(), True
        # # 2. 查看镜像是否已经更新，deployment的镜像更新说明cicd已经执行成功
        # if not self.watch_deployment_image_update(package_image_tag):
        #     message = common.field_list_message_card(
        #         header="镜像更新失败",
        #         color="red",
        #         is_short=False,
        #         具体原因="Deployment的镜像未更新",
        #     )
        #     return message.to_dict(), False
        # # 3. 查看rollout status 等待15分钟
        # deployment = option.Deployment(self.job_id, self.open_id).get_option()
        # namespace = self.magic_arg["namespace"]
        # out, err, ret_code = KubernetesCommand(self.magic_arg["cloud"]).rollout_status(deployment, namespace, timeout=15)
        # if ret_code != ret_code:
        #     message = common.field_list_message_card(
        #         header="服务更新失败",
        #         color="red",
        #         is_short=False,
        #         具体原因="镜像已经替换，两个原因导致启动出错\n1.服务启动失败，需要具体查看\n2.拉取镜像比较慢，需要继续等待",
        #     )
        #     return message.to_dict(), False
        # # 4. 查看服务是否可用
        # ip, port = self.get_service_ip(), self.get_service_port()
        # if self.watch_server_available(ip, port):
        #     message = common.field_list_message_card(
        #         header=f"服务更新成功",
        #         color="green",
        #         is_short=False,
        #         更新结果="服务启动成功",
        #         访问链接=f"http://{ip}:{port}"
        #     )
        #     return message.to_dict(), True
        # else:
        #     message = common.field_list_message_card(
        #         header="服务更新失败",
        #         color="red",
        #         is_short=False,
        #         具体原因="镜像已经替换，两个原因导致启动出错\n1.服务启动失败，需要具体查看\n2.拉取镜像比较慢，需要继续等待",
        #     )
        #     return message.to_dict(), False
