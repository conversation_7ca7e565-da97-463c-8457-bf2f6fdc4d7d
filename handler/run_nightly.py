"""
run_nightly 命令

@author: 
@date: 2022.12.09
"""

import os
import time
import traceback

from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc
from utils.common import field_list_message_card
import pymongo
from utils.init import app
import requests

@CommandParser.register
class Run_nightly(Handler):
    command = "运行测试用例||运行测试||跑一下测试||测试运行情况||测试情况||测试引擎在线吗||测试机器在线吗||测试引擎在线||测试服务在线||强制运行测试用例||强制运行测试||强制跑一下测试||sudo 运行测试用例||sudo 运行测试||sudo 跑一下测试"
    check_template = [
        # 填写参数规则
    ]
    message_type = "text"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "run_nightly",  # 命令示例
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True
        self.server = "http://taptest.natapp1.cc"
        self.header = {"token": "3c4288f5-630a-4df3-bcb9-ef11737200a8"}

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        case = None
        command = kwargs.get("command")
        # 正则匹配数字, 或者数字带 ., 比如 1.2 这样的文本
        import re
        pattern = r'\d+(?:\.\d+)+'
        matches = re.findall(pattern, command)
        if len(matches) > 0:
            case = matches[0]

        if "在线" in command:
            try:
                res = requests.get(self.server+'/ping', headers=self.header, timeout=5)
                return "测试引擎在线", True
            except requests.exceptions.ReadTimeout as e:
                return "测试引擎不在线", True
        if "情况" in command:
            try:
                res = requests.get(self.server+'/tests', headers=self.header, timeout=5)
                data = res.json()
                if "data" not in data or len(data["data"]) == 0:
                    return "没有测试在运行", True
                test = data["data"][0]
                start_time = test["start_time"]
                return f"测试正在运行, 运行时间为 {int(time.time()-start_time)} 秒, 运行情况请访问 http://tmate.io/t/xbsura/taptest-f 查看", True
            except Exception as e:
                return "发生错误了, 请您直接登录机器查看", True
        try:
            if "强制" in command or "sudo" in command:
                requests.delete(self.server+'/tests', headers=self.header, timeout=5)
            if case is None:
                res = requests.post(self.server+'/tests', headers=self.header, timeout=5)
            else:
                res = requests.post(self.server+'/tests', headers=self.header, timeout=5, json={
                    "case": case,
                    "chat_id": open_id,
                })
            data = res.json()
            code = data["code"]
            test = data["data"][0]
            if code != 0:
                return f"有没有完成的测试, 运行时间为 {int(time.time()-test['start_time'])} 秒, 运行情况请访问 http://tmate.io/t/xbsura/taptest-f 查看", True
            if case is None:
                return "命令收到, 完整用例开始运行, 运行情况请访问 http://tmate.io/t/xbsura/taptest-f 查看", True
            else:
                return f"命令收到, 用例: {case} 开始运行, 运行情况请访问 http://tmate.io/t/xbsura/taptest-f 查看", True
        except Exception as e:
            return "发生错误了, 请您直接登录机器查看", True