"""
critical_issue 命令

@author: 
@date: 2022.11.24
"""
import time, datetime

from utils.schdule import CommandParser
from utils.handler import Handler
import requests
from utils.common import get_time_range, field_list_message_card, time_readable

@CommandParser.register
class Expire_issue(Handler):
    token = "9bb7d35bf5fd952fac672e2dc9897392a747541b"
    command = "Expire_issue||超期工单情况||当前超期的工单处理情况||超期的工单||工单超期情况||目前超期的工单"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "expire_issue",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        request_ok = False
        issues = []
        page_number = 1
        while True:
            if request_ok:
                break
            # 这里默认按照 Code 倒序
            data = {
                "Action": "DescribeIssueListWithPage",
                "ProjectName": "tapdata",
                "IssueType": "DEFECT",
                "PageNumber": page_number,
                "Conditions": [{"Key": "PRIORITY", "Value": "3"}],
                "PageSize": 500,
            }
            page_issues = requests.post(
                url="https://tapdata.coding.net/open-api?Action=DescribeIssueListWithPage",
                headers={"Authorization": "token " + self.token},
                json=data,
            ).json().get("Response", {}).get("Data", {}).get("List", [])
            page_number += 1
            for issue in page_issues:
                if not issue.get("Name", "").startswith("#"):
                    continue
                # 只处理 2 个月内的工单
                if issue.get("CreatedAt", 0) / 1000 <= time.time() - 86400 * 30 * 2:
                    request_ok = True
                    break
                issues.append(issue)

        expire_issues = {
            "expired": {
                "3": [],
            },
            "near_expire": {
                "3": [],
            },
            "wait": {
                "3": [],
            }
        }
        s1 = 0
        s2 = 0
        s3 = 0
        for issue in issues:
            if issue.get("IssueStatusName", "") in ["已关闭", "已发布", "挂起", "已拒绝", "已完成", "待验证"]:
                continue
            if issue["Priority"] not in ['3']:
                continue
            if issue.get("AssigneeId") in [8578217, 8506485, 8381266]:
                continue
            expire_at = issue.get("DueDate")
            issue["Code_Status"] = "{}, 状态为: {}".format(issue["Code"], issue["IssueStatusName"])

            if expire_at == 0:
                k = "wait"
                s3 += 1
                expire_issues[k][issue["Priority"]].append(issue)
                continue

            if expire_at <= time.time() * 1000:
                k = "expired"
                s1 += 1
                expire_issues[k][issue["Priority"]].append(issue)
                continue

            if expire_at <= time.time() * 1000 + 86400*1000/2:
                k = "near_expire"
                s2 += 1
                expire_issues[k][issue["Priority"]].append(issue)

        if s1+s2+s3 == 0:
            message = field_list_message_card(
                header="😃 目前没有发现要超期, 或者快要超期的 紧急 工单, 请继续保持!",
                color="green"
            )
            return message.to_dict(), True

        message = field_list_message_card(
            header="🙏 紧急工单中, 有 {} 个需要分配, 有 {} 个已经超期, 有 {} 个即将超期, 请项目经理关注, 请对应的同学及时处理".format(s3, s1, s2),
            color="blue",
            is_short=False,
            等待分配工单列表="\n".join(["https://tapdata.coding.net/p/tapdata/all/issues/{}".format(i["Code_Status"]) for i in expire_issues["wait"]["3"]]),
            超期紧急工单列表="\n".join(["https://tapdata.coding.net/p/tapdata/all/issues/{}".format(i["Code_Status"]) for i in expire_issues["expired"]["3"]]),
            即将超期紧急工单列表="\n".join(["https://tapdata.coding.net/p/tapdata/all/issues/{}".format(i["Code_Status"]) for i in expire_issues["near_expire"]["3"]])
        )
        return message.to_dict(), True
