"""
buildOpensource 命令

@author: 
@date: 2024.03.12
"""

import os
import time
import traceback

from utils.api import GithubApi
from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, option, common


@CommandParser.register
class Buildopensource(Handler):
    command = "buildOpensource"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "buildOpensource",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        """
        1. 获取信息：
            - Tapdata 分支
            - Tapdata Enterprise Web 分支
            - Tapdata Connectors 分支
        2. 触发构建
        3. 构建信息发送
        """
        # 1. 获取信息
        tapdata_branch = option.OpenSourceBranch(self.job_id, open_id).get_option()
        tapdata_frontend_branch = option.FrontendBranch(self.job_id, open_id).get_option()
        tapdata_connectors_branch = option.ConnectorsBranch(self.job_id, open_id).get_option()
        # 2. 触发构建
        owner = os.getenv("OWNER")
        repo = 'tapdata'
        github_obj = GithubApi(owner, repo)
        res = github_obj.trigger_by_workflow_dispatch(
            "build.yml",
            tapdata_branch,
            frontend_branch=tapdata_frontend_branch,
            connectors_branch=tapdata_connectors_branch,
        )
        # 3. 构建信息发送
        if res:
            header = "🎉 构建任务创建成功，执行成功后会通知你哦"
            color = "green"
        else:
            header = "😭 构建任务创建失败，请联系Jerry处理"
            color = "red"
        return common.field_list_message_card(
            header=header,
            color=color,
            Tapdata分支=tapdata_branch,
            TapdataEnterpriseWeb分支=tapdata_frontend_branch,
            TapdataConnectors分支=tapdata_connectors_branch,
        ).to_dict(), res
