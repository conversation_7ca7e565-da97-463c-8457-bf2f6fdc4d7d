"""
bussiness_user_t 命令

@author: 
@date: 2022.12.09
"""
import time

import requests

from utils.schdule import CommandParser
from utils.handler import Handler, Letter
import pymongo


@CommandParser.register
class Time_cal_user(Handler):
    command = "更新一下飞书用户任务表"
    check_template = [
        # 填写参数规则
    ]
    message_type = "text"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "Time_cal_user",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True


    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        app_token = "Sk3ibWVGTamkTqs5xG4cAfRpnCe"
        table_id = "tblyVBXSoGMHEgcD"
        client = pymongo.MongoClient("mongodb://root:Gotapd8!@139.198.127.204:32550/cloud?authSource=admin")
        db = client["cloud"]
        m_user = db["M_User"]
        m_task = db["M_Task"]
        users = m_user.find()

        user_task_status = {}
        lark_header = self.get_lark_header()
        for user in users:
            running_agent = client["cloud"]["M_Agent"].count_documents({
                "status": "Running",
                "tmInfo.userId": str(user["_id"])
            })
            if user.get("username") is None:
                continue
            if user.get("record_id") is None:
                continue

            user_task_status[user["username"]] = {
                "user": user,
                "running_agent": running_agent,
                "tasks": [],
            }
            tasks = list(m_task.find({"createUser": user["username"], "status": {"$nin": ["deleting", "delete_failed"]}}))
            error_task_count = 0
            valid_task_count = 0
            for task in tasks:
                user_task_status[user["username"]]["tasks"].append({
                    "name": task.get("name", ""),
                    "status": task["status"],
                })

                if task["status"] in ["error", "renewing", "schedule_failed", "wait_run", "wait_start"]:
                    error_task_count += 1
                if task["status"] in ["running", "complete", "stop"]:
                    valid_task_count += 1
            url = "https://open.feishu.cn/open-apis/bitable/v1/apps/" + app_token + "/tables/" + table_id + "/records/" + user.get("record_id")
            payload = {
                "fields": {
                    "存活 agent 数量": running_agent,
                    "任务数": len(tasks),
                    "有效任务数": valid_task_count,
                    "异常任务数": error_task_count,
                }
            }
            requests.put(url, json=payload, headers=lark_header)
            time.sleep(0.05)

        return "云版用户基本情况更新完成", True


    def get_lark_header(self):
        get_token_url = "https://open.feishu.cn/open-apis/auth/v3/tenant_access_token/internal"
        g_headers = {
            'Authorization': 'Bearer t-g104au61KXUWHLQ6677SY35VYWS2JOXYYGWEKZR4',
            'Content-Type': 'application/json'
        }
        g_body = {
            "app_id": "cli_a26d15bc2bf99013",
            "app_secret": "6JYz2K0sCV0rMmI6OkMkyhSgnaBZY7PA"
        }

        token = requests.post(get_token_url, json=g_body, headers=g_headers).json()
        t = token["tenant_access_token"]

        headers = {
            'Authorization': 'Bearer ' + t,
            'Content-Type': 'application/json'
        }
        return headers
