"""
暂存数据 用来暂存用户交互历史用

@author: <PERSON>
@date: 2022.08.24
"""
import json
import os

from utils.connector import redis_cli
from utils.handler import Handler
from utils.init import app
from utils.schdule import CommandParser, Schedule, Context
from utils.common import all_release_branch

owner = os.getenv("OWNER")


@CommandParser.register
class Cache(Handler):

    command = "cache"
    help = {
        "format": "cache [key] [value] ",
        "说明": "底层操作命令，用来缓存表单选项",
    }
    image_list_key = "image-tag-list"
    cache = {}
    except_cache = True
    message_type = "text"

    def _get_image_list(self):
        data = redis_cli.get(self.image_list_key)
        if data is not None:
            return json.loads(data.decode("utf-8"))
        return None

    def _set_image_list(self, image_list):
        redis_cli.set(self.image_list_key, image_list, ex=120)

    def _compile_image_list(self, image_list):
        """
        比较原有的镜像tag列表和当前的镜像tag差异
        :param image_list: 当前镜像列表
        """
        # 获取原有的镜像tag列表
        original_image_list = self._get_image_list() and {list(item.keys())[0]: list(item.values())[0] for item in self._get_image_list()}
        # 更新redis镜像tag列表
        self._set_image_list(image_list)
        # 原有的镜像tag列表不存在，跳过此次更新
        if not original_image_list:
            return None
        current_image_list = {list(item.keys())[0]: list(item.values())[0] for item in json.loads(image_list)}
        app.logger.info("original_image_list: %s" % original_image_list)
        app.logger.info("current_image_list: %s" % current_image_list)
        new_image_tag = {}  # 当前更新的镜像tag
        # 比较差异
        for image_tag, image_versions in current_image_list.items():
            if original_image_list.get(image_tag) is None:
                new_image_tag[image_tag] = image_versions[0]["tag"]
            elif len(image_versions) > len(original_image_list[image_tag]):
                new_image_tag[image_tag] = image_versions[0]["tag"]
        app.logger.info("更新的版本: %s" % new_image_tag)
        return new_image_tag

    def _update_image(self, image_tag: dict):
        app.logger.info("开始更新环境")
        app.logger.info(image_tag)
        for tag, version in image_tag.items():
            for env in redis_cli.lrange(f"auto-update#{tag}", 0, -1):
                command = f"deploy update {tag}:{version} {env.decode('utf-8')} | watch env_update"
                context = Context(command=command, open_id="none", chat_type="p2p")
                app.logger.info("准备更新环境: %s" % command)
                Schedule().register(context)
        app.logger.info("更新环境结束")

    def get_cache(self, *args, **kwargs):
        return self.cache

    def handle(self, open_id, chat_type, *args: str, **kwargs) -> (str, bool):
        command = args[0]
        key, value = args
        # 兼容老版本
        self.cache = {f"cache {key}": value, key: value}
        if command == "menu":
            pass
        elif command == "list-image":
            image_list = " ".join(args[1:])
            image_tags = self._compile_image_list(image_list)
            if image_tags:
                self._update_image(image_tags)
        elif command == "versions":
            return f"branch: {json.dumps(all_release_branch())}, host: {os.uname().nodename}, PID: {os.getpid()}", True

        return None, True
