"""
quickupdate_op 命令

@author: 
@date: 2024.05.15
"""

import os
import time
import traceback

from celery import chain, group

from model.task import Task
from model.version import Version
from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, option, tasks, common
from utils.tasks import send_text_to_feishu, trigger_workflow_and_wait, artifact_build_result_send, update_op_env, \
    env_update_result_send, finish_feishu_task, scale_deployment, check_passed_for_cloud_env, open_cloud_gray_env, \
    sync_agent_version, release_agent_version
from utils.workflows import merge_to_develop, merge_to_release


@CommandParser.register
class Quickupdate_op(Handler):
    command = "更新环境，使用"
    check_template = [
        # 填写参数规则
    ]
    message_type = "text"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "quickupdate_op",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def _get_task(self, branch, release_version) -> (Task, str):
        _tasks = Task.query.filter_by(release_version=release_version).all()
        print("run")
        for task in _tasks:
            print(task.summary)
            if task.summary.startswith("1.") and branch == "develop":
                return task, task.id
            if task.summary.startswith("6.") and branch == "release":
                return task, task.id

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        if len(args) == 0:
            return "用法示例：更新环境，使用 develop【使用develop更新OP环境】;更新环境，使用 release 【使用最新的release更新OP和云版灰度环境】", False
        branch = args[0]
        # 当前的迭代号
        sprint = common.get_active_sprint()
        # 找出当前的迭代分支，从version表中获取最大的release版本号
        release_version = common.get_current_release()
        task, task_id = self._get_task(branch, release_version.replace("release-v", ""))
        if not sprint:
            return "当前没有活跃的迭代", False
        if branch == "develop":
            # 使用develop分支更新OP测试环境
            merge_to_develop(sprint, task, task_id)
            return "", True
        elif branch == "release":
            print(f"release_version: {release_version}")
            # 使用最新的release更新OP和云版灰度环境
            merge_to_release(sprint, task, task_id,
                             Version.query.filter_by(version=release_version).first())
            return "", True
