"""
watchOpNew 命令

@author: 
@date: 2024.04.17
"""

import os
import time
import traceback
import json
from datetime import datetime

from lib.api import send_text
from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, option, api, common, message_card
from utils.init import app, db


@CommandParser.register
class Watchopnew(Handler):
    command = "watchOpNew"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "watchOpNew",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def create_job_success_message(self, result: bool, **kwargs):
        if result:
            mc = common.field_list_message_card(
                header="🎉 任务创建成功，执行成功后会通知你哦",
                color="green",
                **kwargs
            )
        else:
            mc = common.field_list_message_card(
                header="😭 任务创建失败",
                color="red",
                处理方式="自行查看Github流程执行情况"
            )

        if kwargs["_BUIILD_NAME"] is not None:
            mc.add_element(message_card.Hr(),message_card.Div(
                text=message_card.Text(f"使用这个命令重命名本次构建: version rename {kwargs['_BUIILD_NAME']} NewBuildName")))

        send_text(self.message_type, self.open_id, mc.to_dict(), self.chat_type)

    def send_run_link(self, runner_id, detail_url):
        message_card = mc.make_workflow_scheduling_notes(runner_id, detail_url)
        send_text(self.message_type, self.open_id, message_card.to_dict(), self.chat_type)

    def get_tag(self, workflow_id):
        run_info = api.GithubApi('tapdata', 'tapdata-application').get_runner_job_info(workflow_id)
        for job in run_info["jobs"]:
            for step in job["steps"]:
                if isinstance(step["name"], str) and step["name"].startswith("TAG="):
                    return step["name"].replace("TAG=", "")
        return ""

    def _get_branches(self, open_id):
        """
        1. 从缓存中获取release_branch_value的选项，如果为空，则是直接选择已发布分支打包
        2. 如果不为空，则为自选分支打包
        """
        release_branch = option.ReleaseVersion(self.job_id, open_id).get_option()
        if release_branch is None:
            # 获取选项信息
            tapdata_branch = option.OpenSourceBranch(self.job_id, open_id).get_option()
            tapdata_enterprise_branch = option.EnterpriseBranch(self.job_id, open_id).get_option()
            tapdata_frontend_branch = option.FrontendBranch(self.job_id, open_id).get_option()
            tapdata_connectors_branch = option.ConnectorsBranch(self.job_id, open_id).get_option()
            tapdata_connectors_enterprise_branch = option.ConnectorsEnterpriseBranch(self.job_id, open_id).get_option()
            tapdata_license_branch = option.LicenseBranch(self.job_id, open_id).get_option()
        else:
            branches = common.all_release_branch()[release_branch]
            tapdata_branch, tapdata_enterprise_branch, tapdata_frontend_branch, \
                tapdata_connectors_branch, tapdata_connectors_enterprise_branch, tapdata_license_branch = \
                branches.get("tapdata"), branches.get("tapdata_enterprise"), branches.get("tapdata_enterprise_web"), \
                branches.get("tapdata_connectors"), branches.get("tapdata_connectors_enterprise"), \
                branches.get("tapdata_license")
        return tapdata_branch, tapdata_enterprise_branch, tapdata_frontend_branch, tapdata_connectors_branch, \
            tapdata_connectors_enterprise_branch, tapdata_license_branch

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        # 获取选项信息
        tapdata_branch, tapdata_enterprise_branch, tapdata_frontend_branch, tapdata_connectors_branch, \
            tapdata_connectors_enterprise_branch, tapdata_license_branch = self._get_branches(open_id)
        # 检查分支是空的
        if not tapdata_branch or not tapdata_enterprise_branch or not tapdata_frontend_branch or not tapdata_connectors_branch or not tapdata_connectors_enterprise_branch or not tapdata_license_branch:
            return common.field_list_message_card(
                header="😭 任务创建失败",
                color="red",
                处理方式="请填写完整的分支信息"
            ).to_dict(), False
        
        # 获取所有选项
        is_tar = option.IsPackageTar(self.job_id, open_id).get_option()
        frontend_mode = option.FrontendMode(self.job_id, open_id).get_option()
        platform = option.PlatformOption(self.job_id, open_id).get_option()
        java_version = option.JavaVersion(self.job_id, open_id).get_option()
        run_test = option.RunTest(self.job_id, open_id).get_option()
        connectors_options = option.ConnectorsOptions(self.job_id, open_id).get_option()
        # 构建BUILD_CONFIG JSON字符串
        build_config = json.dumps({
            "FRONTEND_BRANCH": tapdata_frontend_branch,
            "OPENSOURCE_BRANCH": tapdata_branch,
            "ENTERPRISE_BRANCH": tapdata_enterprise_branch,
            "CONNECTORS_BRANCH": f"{tapdata_connectors_branch}#{tapdata_connectors_enterprise_branch}",
            "LISENSE_BRANCH": tapdata_license_branch,
            "TAG_NAME": ""
        })
        include_apiserver = option.IncludeApiServer(self.job_id, open_id).get_option()
        save_template = option.SaveTemplateOptions(self.job_id, open_id).get_option()

        # 保存构建分支为模版
        template_name:str = None
        if save_template:
            template_name = f"AUTO-{datetime.now().strftime('%Y-%m-%d_%H:%M:%S')}"
            common.insert_build_template(db, app,
                                         template_name, tapdata_branch, tapdata_enterprise_branch,
                                         tapdata_frontend_branch, tapdata_connectors_branch,
                                         tapdata_connectors_enterprise_branch, tapdata_license_branch)
        
        # 开始触发构建
        workflow_id, result = api.GithubApi('tapdata', 'tapdata-application').workflow_dispatch_trigger_and_wait(
            'build-tapdata-op.yaml',
            'main',
            send_create_job_result=self.create_job_success_message,
            send_detail_info=self.send_run_link,
            BUILD_CONFIG=build_config,
            IS_TAR=is_tar,
            FRONTEND_MODE=frontend_mode,
            TAPDATA_PLATFORM_VERSION=platform,
            JAVA_VERSION=java_version,
            RUN_TEST=run_test,
            INCLUDE_APISERVER=include_apiserver,
            _BUIILD_NAME=template_name,
            **{"CONNECTORS-OPTIONS": connectors_options}
        )
        tag = ""
        if result:
            params = {}
            tag = self.get_tag(workflow_id)
            if tag:
                params.update({
                    "Tag": tag,
                })
            if is_tar:
                params.update({
                    "获取压缩包": "访问 https://lark.tapdata.net/package/list/ 获取压缩包",
                    "登陆信息": "admin / Gotapd8!",
                })
            message = common.field_list_message_card(
                header="构建任务运行成功",
                color="green",
                is_short=False,
                **params
            )
        else:
            message = common.field_list_message_card(
                header="😭 流程执行失败了",
                color="red",
                is_short=False,
                处理方式="自行查看Github流程执行情况"
            )
        # 查看是否需要更新
        deployment = option.Deployment(self.job_id, open_id).get_option()
        if deployment != "无" and deployment is not None:
            # 获取Java版本
            java_version = option.JavaVersion(self.job_id, open_id).get_option()
            
            workflow_id, result = api.GithubApi('tapdata', 'tapdata-application').workflow_dispatch_trigger_and_wait(
                'deploy-tapdata-op.yaml',
                'main',
                send_create_job_result=self.create_job_success_message,
                send_detail_info=self.send_run_link,
                TAG_NAME=tag,
                DEPLOY=deployment,
                AUTO_TEST=False,
                JAVA_VERSION=java_version
            )
            if result:
                message = common.field_list_message_card(
                    header="部署成功",
                    color="green",
                    is_short=False,
                )
            else:
                message = common.field_list_message_card(
                    header="😭 流程执行失败了",
                    color="red",
                    is_short=False,
                    处理方式="自行查看Github流程执行情况"
                )
            return message.to_dict(), True
        else:
            return message.to_dict(), True
