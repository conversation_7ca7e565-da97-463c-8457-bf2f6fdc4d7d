"""
sync_agent_image_watch 命令

@author: 
@date: 2023.08.25
"""

import os
import time
import traceback

from lib.api import send_text
from utils.api import GithubApi
from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, option, common


@CommandParser.register
class Sync_agent_image_watch(Handler):
    command = "sync_agent_image_watch"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "sync_agent_image_watch",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def send_message(self, runner_id, detail_url):
        message_card = mc.make_workflow_scheduling_notes(runner_id, detail_url)
        print(f"runner_id: {runner_id}, detail_url: {detail_url}")
        send_text(self.message_type, self.open_id, message_card.to_dict(), self.chat_type)

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        owner, repo = os.getenv("OWNER"), "tapdata-application"
        github_obj = GithubApi(owner, repo)
        _, status = github_obj.job_status(self.job_id, runner_url_fn=self.send_message, event="workflow_dispatch")
        image_version = option.ImageVersion(self.job_id, self.open_id).get_option()
        print(f"image_version: {image_version}")
        if status and status not in [github_obj.RunnerConclusion.failure, github_obj.RunnerConclusion.cancelled]:
            kwargs = {
                "header": "🎉 镜像分发任务执行结束",
                "Agent版本": f"{image_version}",
            }
            message = common.field_list_message_card(**kwargs)
        elif status and status == github_obj.RunnerConclusion.cancelled:
            kwargs = {
                "header": "📣 镜像分发任务被取消",
                "color": "red",
                "Agent版本": f"{image_version}",
            }
            message = common.field_list_message_card(**kwargs)
        elif status and status == github_obj.RunnerConclusion.failure:
            kwargs = {
                "header": "📣 镜像分发任务执行失败",
                "color": "red",
                "Agent版本": f"{image_version}",
            }
            message = common.field_list_message_card(**kwargs)
        elif not status and status == github_obj.RunnerConclusion.start_failed:
            message = mc.workflow_start_error()
        else:
            kwargs = {
                "header": "📣 镜像分发任务执行超时",
                "color": "red",
                "Agent版本": f"{image_version}",
            }
            message = common.field_list_message_card(**kwargs)
        return message.to_dict(), True
