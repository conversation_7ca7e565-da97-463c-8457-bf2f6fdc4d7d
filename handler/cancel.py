"""
cancel 命令

@author: 
@date: 2022.11.03
"""

import os
import time
import traceback

from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc
from utils.api import GithubApi


@CommandParser.register
class Cancel(Handler):
    command = "cancel"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "cancel [cancel_type] [the_id]",  # 命令示例
        "cancel_type": "取消任务运行的类型, cicd",  # 填写选项和说明
        "id": "such as cicd id",
    }

    def _make_card(self, header, message, color="green"):
        message_card = mc.MessageCard(mc.Header(header, color=color), mc.Config())
        div_1 = mc.Div(mc.Text(content=message))
        message_card.add_element(div_1)
        return message_card

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        cancel_type, the_id = args
        if cancel_type == "cicd":
            ret = GithubApi("tapdata", "tapdata-application").cancel_workflow_run(the_id)
            if ret:
                message_card = self._make_card("取消任务成功", message="success!!!", color="green")
            else:
                message_card = self._make_card("取消任务失败", message="failed!!!", color="red")
            return message_card.to_dict(), True
        return None, True
