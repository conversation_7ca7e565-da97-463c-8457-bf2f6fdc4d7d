"""
check 命令

@author: 
@date: 2022.11.08
"""

import os

from utils.schdule import CommandParser
from utils.handler import Handler
from utils import message_card as mc
from utils.init import app
from utils import api
from utils import exception
from utils import common


@CommandParser.register
class Check(Handler):
    command = "check"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "check [cicd] [runner id]",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {
            "namespace": "dev",
            "env": None,
        }

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        # do something
        if len(args) <= 1:
            app.logger.warn(f"内部命令语法错误, 命令: `check {args}`")
            raise exception.InternalCommandSyncTaxError
        if args[0] == "cicd":
            runner_id = args[1]
            github_api = api.GithubApi(os.getenv("OWNER"), "tapdata-application")
            runner_info = github_api.get_runner_job_info(runner_id)
            jobs = common.get_runner_info(runner_info)
            message_card = mc.MessageCard(mc.Header("查看构建任务进度", color="blue"), mc.Config())
            for job_name, job in jobs.items():
                message_card.add_element(mc.Div(mc.Text(f"**[任务名称]** {job_name}", tag_type=mc.TextType.lark_md)))
                if job.get("current_step") is not None:
                    fields_div = common.make_fields_div(
                        任务状态=job["status"], 运行结果=job["conclusion"],
                        开始时间=job["start_time"], 结束时间=job["completed_at"],
                        当前正在运行=job["current_step"]
                    )
                else:
                    fields_div = common.make_fields_div(
                        任务状态=job["status"], 运行结果=job["conclusion"],
                        开始时间=job["start_time"], 结束时间=job["completed_at"],
                    )
                message_card.add_element(fields_div)
                message_card.add_element(mc.Hr())
            return message_card.to_dict(), True
        elif args[0] == "env_update":
            namespace = args[1]
            env = args[2]
            app.logger.info(f"namespace: {namespace}, env: {env}")
            result = api.K8sApi(namespace).check_pod_event(namespace, env)
            message_card = mc.MessageCard(mc.Header("查看部署进度", color="blue"), mc.Config())
            for pod_name, pod_info in result.items():
                # print(pod_name, pod_info)
                # text = mc.Div(text=mc.Text(f"**{pod_name}**", tag_type=mc.TextType.lark_md))
                fields_div = common.make_fields_div(
                    Pod名称=pod_name,
                    Message=pod_info["message"],
                    Reason=pod_info["reason"],
                    Time=pod_info["start_time"],
                )
                message_card.add_element(fields_div, mc.Hr())
            return message_card.to_dict(), True
