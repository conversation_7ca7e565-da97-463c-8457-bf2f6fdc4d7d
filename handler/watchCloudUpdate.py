"""
watchCloudUpdate 命令

@author: 
@date: 2023.05.15
"""

import os
import time
import traceback

from lib.api import send_text
from utils.api import GithubApi, KubernetesCommand
from utils.init import app
from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, common, option, api
from utils.shell import Shell


@CommandParser.register
class Watchcloudupdate(Handler):
    command = "watchCloudUpdate"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "watchCloudUpdate",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    deployment_name_convert = {
        "huaweiCloud": {
            "dfs-console": "console-prod3",
            "dfs-tcm": "tcm-prod3",
            "dfs-tm-java": "tm-prod3",
        },
        "gcp": {
            "dfs-console": "console-prod3",
            "dfs-tcm": "tcm-prod3",
            "dfs-tm-java": "tm-prod3",
        }
    }

    namespace_name_convert = {
        "huawei-gray": "gray",
        "huawei-prod": "prod",
        "gcp-gray": "cloud-gray",
        "gcp-prod": "cloud-prod",
        "gcp-dev": "cloud-dev",
        "gcp-test": "cloud-test",
        "gcp-prod-net": "cloud-prod-net",
        "gcp-uat": "cloud-uat",
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "gcp-test",
            "concurrency": False,
            "cloud": "gcp",
        }
        self.concurrency_limit = True

    def _deployment(self, deployment):
        cloud = self.magic_arg["cloud"]
        if cloud in self.deployment_name_convert:
            if deployment in self.deployment_name_convert[cloud]:
                return self.deployment_name_convert[cloud][deployment]
        return deployment

    def _namespace(self):
        return self.namespace_name_convert.get(self.magic_arg["namespace"], self.magic_arg["namespace"])

    def send_message(self, runner_id, detail_url):
        message_card = mc.make_workflow_scheduling_notes(runner_id, detail_url)
        send_text(self.message_type, self.open_id, message_card.to_dict(), self.chat_type)

    def image_repo(self):
        image_repo = option.ImageRepo(self.job_id, self.open_id).get_option()
        return image_repo

    def deployments(self, tag):
        deployments = {}
        image_repo = self.image_repo()
        project = "tapdata" if self.magic_arg["cloud"] != "gcp" else "tapdata-cloud"
        print("project: ", project)
        if option.UpdateTM(self.job_id, self.open_id).get_option():
            deployments["tm-prod3"] = f"{image_repo}/{project}/dfs-tm-java:{tag}"
        if option.UpdateTCM(self.job_id, self.open_id).get_option():
            deployments["tcm-prod3"] = f"{image_repo}/{project}/dfs-tcm:{tag}"
        if option.UpdateConsole(self.job_id, self.open_id).get_option():
            deployments["console-prod3"] = f"{image_repo}/{project}/dfs-console:{tag}"
        return deployments

    def job_status(self):
        """
        监听任务创建，启动和结束，最终返回镜像tag

        1. 根据job_id获取runner_id
        2. 发送任务已经运行的通知
        3. 监听runner运行
        4. 发送runner状态
        """
        owner, repo = os.getenv("OWNER"), 'tapdata-application'
        github_obj = GithubApi(owner, repo)
        runner_info, status = github_obj.job_status(self.job_id, runner_url_fn=self.send_message,
                                                    event="workflow_dispatch")
        image_repo = option.ImageRepo(self.job_id, self.open_id).get_option()
        if status and status not in [github_obj.RunnerConclusion.failure, github_obj.RunnerConclusion.cancelled]:
            print("runner_info: ", runner_info)
            tag = runner_info["Dfs_tag"]
            print()
            project = "tapdata" if self.magic_arg["cloud"] != "gcp" else "tapdata-cloud"
            tm = f"{image_repo}/{project}/dfs-tm-java:{tag}" if option.UpdateTM(self.job_id, self.open_id).get_option() else "不更新"
            agent = f"{image_repo}/{project}/dfs-flow-engine:{tag}" if option.UpdateAgent(self.job_id, self.open_id).get_option() else "不更新"
            console = f"{image_repo}/{project}/dfs-console:{tag}" if option.UpdateConsole(self.job_id, self.open_id).get_option() else "不更新"
            tcm = f"{image_repo}/{project}/dfs-tcm:{tag}" if option.UpdateTCM(self.job_id, self.open_id).get_option() else "不更新"
            kwargs = {
                "header": "🎉 构建任务执行成功",
                "TM": tm,
                "Agent": agent,
                "TapdataAgent": agent,
                "Console": console,
                "Tcm": tcm,
                "Cloud": f"{tag}",
            }
            message = common.field_list_message_card(**kwargs)
            return message, tag
        elif status and status == github_obj.RunnerConclusion.cancelled:
            message = common.field_list_message_card(
                header="📣 流程被cancelled掉了",
                color="red",
                is_short=False,
                处理方式="自行查看Github流程执行情况"
            )
        elif status and status == github_obj.RunnerConclusion.failure:
            message = common.field_list_message_card(
                header="😭 流程执行失败了",
                color="red",
                is_short=False,
                处理方式="自行查看Github流程执行情况"
            )
        elif not status and status == github_obj.RunnerConclusion.start_failed:
            message = mc.workflow_start_error()
        else:
            message = common.field_list_message_card(
                header="😭 流程执行超时",
                color="red",
                处理方式="自行查看Github执行情况并联系Jerry处理"
            )
        return message, None

    def get_deployment_image(self, deployment):
        """获取deployment镜像"""
        out, err, return_code = KubernetesCommand(self.magic_arg["cloud"]).get_deployment_image(
            self._deployment(deployment),
            self._namespace())
        if return_code != 0:
            message = common.field_list_message_card(
                header="监听镜像更新失败",
                color="red",
                is_short=False,
                处理方式="获取deployment镜像失败",
                output=out,
                error=err,
            )
            return message.to_dict(), False
        return out

    def check_deployment_image_update(self, package_image_tag):
        """检查Deployment的镜像是否更新, 返回未替换镜像的deployment和状态"""
        for deployment, image_tag in self.deployments(package_image_tag).items():
            ok = True
            for _ in range(4):  # 由于cicd流程已经更新镜像，这里最多等待40秒
                image = self.get_deployment_image(deployment)
                app.logger.info(f"[{self.job_id}]image: {image}, cicd: {package_image_tag}")
                print(f"image is: {image}, image_tag is: {image_tag}")
                image = image.replace("registry-vpc", "registry")
                if image == image_tag:
                    ok = False
                    break
                time.sleep(10)
            if ok:
                return deployment, False
        return None, True

    def check_deployment_rollout_status(self, package_image_tag):
        """检查deployment的更新状态是否成功"""
        success_deployment, failure_deployment, status = [], [], True
        for deployment, _ in self.deployments(package_image_tag).items():
            out, err, ret_code = KubernetesCommand(self.magic_arg["cloud"]).rollout_status(self._deployment(deployment),
                                                                                           self._namespace(), timeout=5)
            if ret_code != 0:
                message = common.field_list_message_card(
                    header=f"{deployment}更新失败",
                    color="red",
                    is_short=False,
                    更新结果="更新失败",
                )
                send_text(self.message_type, self.open_id, message.to_dict(), self.chat_type)
                failure_deployment.append(deployment)
                status = False
            else:
                message = common.field_list_message_card(
                    header=f"{deployment}更新成功",
                    color="green",
                    is_short=False,
                    更新结果="更新成功",
                )
                send_text(self.message_type, self.open_id, message.to_dict(), self.chat_type)
                success_deployment.append(deployment)
        return success_deployment, failure_deployment, status

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        """
        云版的部署方式，是否成功更新的方式
        1. 通过Kubernetes部署到Gcp
            (1). 1. 监听CICD JOB流程是否执行完毕
            (2). 检查Deployment是否更新
            (3). 检查镜像是否已经更新成功
            (4). 检查deployment更新状态
        2. 通过Docker Compose部署到本地机房 TODO
            (1). 检查docker的镜像是否正确
            (2). 循环执行docker exec -it curl -s http://localhost:xxxx/xxx
        """
        self.chat_type = chat_type
        env = option.CloudEnv(self.job_id, open_id).get_option()
        # 1. 监听CICD JOB流程是否执行完毕
        message, package_image_tag = self.job_status()
        if package_image_tag is None:
            return message.to_dict(), False
        send_text(self.message_type, self.open_id, message.to_dict(), self.chat_type)
        if env in ["gcp-dev", "gcp-test"]:
            message = common.field_list_message_card(
                header=f"服务更新成功",
                color="green",
                is_short=False,
                更新结果="更新成功",
            )
            return message.to_dict(), True
        # 2. 检查Deployment是否更新
        deployment, status = self.check_deployment_image_update(package_image_tag)
        if not status:
            message = common.field_list_message_card(
                header="镜像更新失败",
                color="red",
                is_short=False,
                镜像名称=deployment,
                具体原因="Deployment的镜像未更新",
            )
            return message.to_dict(), False
        success_deployment, failure_deployment, status = self.check_deployment_rollout_status(package_image_tag)
        if status:
            message = common.field_list_message_card(
                header=f"服务更新成功",
                color="green",
                is_short=False,
                更新结果="更新成功",
                更新的Deployment=",".join(success_deployment),
            )
        else:
            message = common.field_list_message_card(
                header=f"服务更新失败",
                color="red",
                is_short=False,
                更新结果="更新失败",
                失败的Deployment=",".join(failure_deployment),
                成功的Deployment=",".join(success_deployment),
            )
        return message.to_dict(), True

