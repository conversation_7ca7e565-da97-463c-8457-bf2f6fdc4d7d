import json

from utils import common
from utils.handler import Handler
from utils.schdule import CommandParser


@CommandParser.register
class Version(Handler):
    command = "version"
    help = {
        "format": "version [operation] [value] [value1] ",
        "说明": """版本管理，可用操作包括:
            rename <原版本名称> <新版本名称>
            rm <原版本名称>
            ls 
            show <版本名称>
            help
        """,
    }
    except_cache = False
    message_type = "text"

    def handle(self, open_id, chat_type, *args: str, **kwargs) -> (str, bool):
        command = args[0]
        if command == "help":
            return f"{self.help['format']}\n{self.help['说明']}", True
        elif command == "ls":
            keys = common.all_release_branch().keys()
            return ", ".join(keys), True
        elif command == "show":
            if len(args) != 2:
                return "参数错误, 格式为: version show <版本名称>", True
            version = common.get_release_version(args[1])
            return f"{common.serialize_model(version, indent=4)}", True
        elif command == "rm":
            if len(args) != 2:
                return "参数错误, 格式为: version rm <版本名称>", True
            count = common.remove_release_version(args[1])
            return f"Removed {count} version(s)", True
        elif command == "rename":
            if len(args) != 3:
                return "参数错误, 格式为: version rename <原版本名称> <新版本名称>", True
            count = common.rename_release_version(args[1], args[2])
            return f"Renamed {count} version(s)", True

        return None, True


