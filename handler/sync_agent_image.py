"""
sync_agent_image 命令

@author: 
@date: 2023.08.25
"""

import os
import time
import traceback

from utils.api import GithubApi
from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, option, common


@CommandParser.register
class Sync_agent_image(Handler):
    command = "sync_agent_image"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "sync_agent_image",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        """
        对照tapdata-enterprise仓库的cloud_sync_agent_image.yaml文件

        1. 获取最新的脚本分支
        2. 获取Agent版本列表
        3. 触发脚本执行
        """
        # 1. 获取最新的脚本分支
        ref = "main"
        # 2. 获取Agent版本列表
        image_version = option.ImageVersion(self.job_id, self.open_id).get_option()
        # 3. 触发脚本执行
        owner = os.getenv("OWNER")
        repo = "tapdata-application"
        github_obj = GithubApi(owner, repo)
        res = github_obj.trigger_by_workflow_dispatch("83640374",
                                                      ref,
                                                      **{
                                                          "AGENT_VERSION": f"{image_version}",
                                                          "JOB_ID": f"{self.job_id}",
                                                      })
        if res:
            return common.field_list_message_card(
                header="开始同步镜像",
                脚本分支=f"main",
                Agent版本=f"{image_version}",
            ).to_dict(), True
        else:
            return common.field_list_message_card(
                header="同步镜像失败",
                color="red",
                脚本分支=f"main",
                Agent版本=f"{image_version}",
                处理方式="请立即联系Jerry",
            ).to_dict(), False
