"""
bussiness_user_t 命令

@author: 
@date: 2022.12.09
"""

import os
import time
import traceback

from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc
from utils.common import field_list_message_card
import pymongo
from utils.init import app


@CommandParser.register
class Bussiness_user_t(Handler):
    command = "云版商家情况"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "bussiness_user_t",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True


    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        client = pymongo.MongoClient("mongodb://root:Gotapd8!@***************:32550/cloud?authSource=admin")
        db = client["cloud"]
        m_user = db["M_User"]
        m_task = db["M_Task"]
        user_type = "user"
        if "重要" in kwargs.get("command") or "vip" in kwargs.get("command").lower():
            user_type = "vip"
        users = m_user.find({"customer_type": user_type})

        user_task_status = {}
        for user in users:
            running_agent = client["cloud"]["M_Agent"].count_documents({
                "status": "Running",
                "tmInfo.userId": str(user["_id"])
            })
            user_task_status[user["username"]] = {
                "user": user,
                "running_agent": running_agent,
                "tasks": [],
            }
            tasks = m_task.find({"createUser": user["username"], "status": {"$nin": ["deleting", "delete_failed"]}})
            for task in tasks:
                user_task_status[user["username"]]["tasks"].append({
                    "name": task["name"],
                    "status": task["status"],
                })
        message = field_list_message_card(
            header="云版目前有 {} 个 {} 商户, 基本情况如下:".format(len(user_task_status), "普通" if user_type == "user" else "VIP"),
            color="blue",
            is_short=False,
        )

        fields = mc.Fields()
        for username, info in user_task_status.items():
            error_task_count = 0
            for task in info["tasks"]:
                if task["status"] in ["error", "renewing", "schedule_failed", "wait_run", "wait_start"]:
                    error_task_count += 1
            field_text = f"**{username}**, 商户: {info['user'].get('customer_name', '未标记')},  存活agent: {info['running_agent']}, 任务数: {len(info['tasks'])}, 异常任务数: {error_task_count}\n"
            app.logger.info(field_text)
            fields.add_child(mc.Text(field_text, mc.TextType.lark_md), is_short=False)
        div = mc.Div(fields=fields)
        message.add_element(div)
        return message.to_dict(), True
