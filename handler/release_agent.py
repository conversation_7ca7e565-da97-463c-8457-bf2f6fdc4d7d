"""
release_agent 命令

@author: 
@date: 2023.05.19
"""

import os
import pickle
import time
import traceback
from datetime import datetime
import json

from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, option
from utils.connector import redis_cli, MongoConnector
from utils.shell import Shell


@CommandParser.register
class Release_agent(Handler):
    command = "release_agent"
    check_template = [
        # 填写参数规则
    ]
    message_type = "text"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "release_agent",  # 命令示例
        "选项": "发布agent",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到

        }
        self.concurrency_limit = True

    def _get_enabled_resource_pool(self, option_group_value_list: dict):
        enabled_resource_pool = []
        for key, value in option_group_value_list.items():
            if value:
                enabled_resource_pool.append(key)
        return enabled_resource_pool

    def _find_enabled_resource_poll_info_by_name(self, cloud_env, enabled_resource_poll_name_list: list):
        """根据资源池名称列表获取资源池信息, 用enabled_resource_poll_name_list与self._get_resource_pool()的返回值进行比对"""
        resource_pool_info_list = option.ResourcePoll.get_resource_pool(cloud_env)
        return [i for i in resource_pool_info_list
                if option.ResourcePoll.format_option_key(i) in enabled_resource_poll_name_list]

    def _image_not_exists_text(self, image_tag):
        return f"镜像{image_tag}不存在，请先同步镜像到对应云商"

    def _oss_file_not_exists_text(self, oss_file_name):
        return f"文件{oss_file_name}不存在，检查文件是否上传到oss"

    def _check_agent_exists(self, agent_version, cloud_env):
        """检查Agent是否已经存在
        # 1. 检查agent镜像是否存在
        #     (1) Coding镜像是否存在
            # (2) 判断各个云商是否存在镜像
            #     prod: 阿里云北京 阿里云香港
            #     huawei-prod: 阿里云香港
            #     cloud-dev cloud-test cloud-uat: 不做检查
        2. 检查tapdata-agent资源是否可以下载
            (1) prod huawei-prod: feagent
            (2) cloud-dev cloud-test cloud-uat: test
        """
        # 1. 检查agent镜像是否存在
        # (1) Coding镜像是否存在
        image_name = "tapdata-docker.pkg.coding.net/dfs/flow-engine/dfs-flow-engine"
        # 登录 login
        # _, _, return_code = Shell.execute(option.DOCKER_REGISTRY["coding"]['login'])
        # if return_code != 0:
        #     return False, f"登录Coding镜像仓库失败"
        # for v in option.DOCKER_REGISTRY.values():
        #     _, _, return_code = Shell.execute(v["login"])
        #     if return_code != 0:
        #         return False, f"执行{v['login']}失败"
        # check_image_exists_command = f"docker manifest inspect {image_name}:{agent_version}"
        # out, err, return_code = Shell.execute(check_image_exists_command)
        # if return_code != 0:
        #     return False, self._image_not_exists_text(f"{image_name}:{agent_version}")
        # (2) 判断各个云商是否存在镜像
        # if cloud_env == "prod":
        #     # 判断阿里云北京是否存在镜像
        #     aliyun_beijing_image = "registry.cn-beijing.aliyuncs.com/tapdata/dfs-flow-engine"
        #     check_image_exists_command = \
        #         f"docker manifest inspect {aliyun_beijing_image}:{agent_version}"
        #     out, err, return_code = Shell.execute(check_image_exists_command)
        #     if return_code != 0:
        #         return False, self._image_not_exists_text(f"{aliyun_beijing_image}:{agent_version}")
        #     # 判断阿里云香港是否存在镜像
        #     aliyun_hongkong_image = "registry.cn-hongkong.aliyuncs.com/tapdata/dfs-flow-engine"
        #     check_image_exists_command = \
        #         f"docker manifest inspect {aliyun_hongkong_image}:{agent_version}"
        #     out, err, return_code = Shell.execute(check_image_exists_command)
        #     if return_code != 0:
        #         return False, self._image_not_exists_text(f"{aliyun_hongkong_image}:{agent_version}")
        # elif cloud_env == "huawei-prod":
        #     # 判断阿里云香港是否存在镜像
        #     aliyun_hongkong_image = "registry.cn-hongkong.aliyuncs.com/tapdata/dfs-flow-engine"
        #     check_image_exists_command = \
        #         f"docker manifest inspect {aliyun_hongkong_image}:{agent_version}"
        #     out, err, return_code = Shell.execute(check_image_exists_command)
        #     if return_code != 0:
        #         return False, self._image_not_exists_text(f"{aliyun_hongkong_image}:{agent_version}")
        # 2. 检查tapdata-agent资源是否可以下载
        if cloud_env in ["cloud-net", "cloud-prod"]:  # prod io and net
            flag = "feagent"
        elif cloud_env in ["cloud-dev", "cloud-test", "cloud-uat"]:  # cloud-dev cloud-test cloud-uat
            flag = "test"
        else:
            return False, f"cloud_env参数错误"
        oss_uri = f"oss://tapdata-cdn-beijing/package/{flag}/dfs-{agent_version}"
        files = [".version", "log4j2.yml", "tapdata", "tapdata-agent", "tapdata.exe"]
        for file in files:
            check_file_exists_command = f"ossutil ls {oss_uri}/{file} | " \
                    "grep 'Object Number is:' | awk -F' ' '{print $4}'"
            out, err, return_code = Shell.execute(check_file_exists_command)
            if return_code != 0 or int(out) == 0:
                return False, self._oss_file_not_exists_text(f"{oss_uri}/{file}")
        return True, ""

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        """
        1. 从缓存获取发布的环境、Agent版本号、支持的资源池信息、启用灰度
        2. 检查Agent是否已经存在
        3. 向数据库插入一条记录以发布Agent
        4. 验证数据库是否存在记录
        """
        # 1. 从缓存获取发布的环境、Agent版本号、支持的资源池
        cloud_env = option.CloudEnv(self.job_id, self.open_id).get_option()  # 发布的环境
        print("cloud_env", cloud_env)
        agent_version = option.AgentVersion(self.job_id, self.open_id).get_option()  # Agent版本号
        if agent_version is None:
            return "请先选择Agent版本号", False
        option_group_string = option.OptionGroupCache(self.job_id, self.open_id).get_option()  # 资源池选项组
        enable_gray = option.EnableGray(self.job_id, self.open_id).get_option()  # 启用灰度
        option_group_obj = option.OptionGroup(self.job_id, self.open_id, json.loads(option_group_string))  # 资源池选项组对象
        enabled_resource_poll_name_list = self._get_enabled_resource_pool(
            option_group_obj.get_option_list()
        )  # 选中的资源池名称列表
        enabled_resource_pool_info_list = self._find_enabled_resource_poll_info_by_name(
            cloud_env, enabled_resource_poll_name_list
        )  # 选中的资源池信息列表
        # 2. 检查Agent是否已经存在
        is_exist, msg = self._check_agent_exists(agent_version, cloud_env)
        if not is_exist:
            return msg, False
        # 3. 向数据库插入一条记录以发布Agent
        agent_tag = 'latest' if cloud_env == 'cloud-net' or cloud_env == 'cloud-prod' else '{version}'
        document = {
            "version": agent_version,
            "lbsVersion": agent_version,
            "enable": True,
            "productType": "agent",
            "createAt": datetime.now(),
            "supportResPools": [
                {
                    "region": "-",
                    "zone": "-"
                },
                {
                    "region": "-",
                    "zone": "-",
                    "provider": "-"
                }
            ] + enabled_resource_pool_info_list,
            "tmServerUrl": option.ResourcePoll.agent_info[cloud_env]["tmServerUrl"],
            "downloadUrl": option.ResourcePoll.agent_info[cloud_env]["downloadUrl"],
            "links": [
                {
                    "os": "windows",
                    "command": "{token}"
                },
                {
                    "os": "linux",
                    "command": "wget '{downloadUrl}tapdata' && chmod +x tapdata && ./tapdata start backend --downloadUrl {downloadUrl} --token '{token}'"
                },
                {
                    "os": "docker",
                    "command": f"docker run --pull always --restart always -itd tapdata-docker.pkg.coding.net/dfs/flow-engine/dfs-flow-engine:{agent_tag} " + "/opt/agent/tapdata start backend  --token '{token}'"
                },
                {
                    "os": "AliComputenest",
                    "url": "https://computenest.console.aliyun.com/user/cn-hangzhou/serviceInstanceCreate?ServiceId=service-1dda29c3eca648cfb0cb&params=%7B%22RegionId%22%3A%22cn-hangzhou%22%2C%22Parameters%22%3A%7B%22PayType%22%3A%22PostPaid%22%2C%22PayPeriodUnit%22%3A%22Month%22%2C%22PayPeriod%22%3A1%2C%22TapdataAgentVersion%22%3A%22{version}%22%2C%22TapdataAgentToken%22%3A%22{token}%22%7D%2C%22TemplateName%22%3A%22TapdataCloudAgentPrivateDeployTemplate%22%7D&spm=5176.24779694.0.0.29494d22hmLPRe",
                    "trialUrl": "https://computenest.console.aliyun.com/user/cn-hangzhou/serviceInstanceCreate?ServiceId=service-1dda29c3eca648cfb0cb&isTrial=true&params=%7B%22RegionId%22%3A%22cn-hangzhou%22%2C%22Parameters%22%3A%7B%22PayType%22%3A%22PostPaid%22%2C%22PayPeriodUnit%22%3A%22Month%22%2C%22PayPeriod%22%3A1%2C%22TapdataAgentVersion%22%3A%22{version}%22%2C%22TapdataAgentToken%22%3A%22{token}%22%7D%2C%22TemplateName%22%3A%22TapdataCloudAgentPrivateDeployTemplate%22%7D&spm=5176.24779694.0.0.29494d22hmLPRe"
                }
            ],
            "changeList": "1. 修复已知缺陷",
            "enableGrayScale": enable_gray,
            "packageSize": 235929600,
            "releaseNoteUri": "https://mp.weixin.qq.com/s/eBHKEZBVkuQ0ah8Kv0wRKQ",
            "estimatedUpgradeTime": 600
        }
        db = option.ResourcePoll.agent_info[cloud_env]["database"]
        MongoConnector(option.ResourcePoll.agent_info[cloud_env]["mongo_uri"]).insert_one(
            db, "drs_product_release", document
        )
        # 4. 验证数据库是否存在记录
        result = MongoConnector(option.ResourcePoll.agent_info[cloud_env]["mongo_uri"]).get(db, "drs_product_release", {
            "version": agent_version
        })
        if result:
            return "发布Agent成功, 等待30秒左右生效", True
        else:
            return "发布Agent失败", False


