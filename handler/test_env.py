"""
test_env 命令

@author: 
@date: 2022.12.22
"""

import os
import time
import traceback

import requests

from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc


@CommandParser.register
class Test_env(Handler):
    command = "测试一下这个环境||测一下这个环境||测一下环境||简单测一下||跑下这个环境的测试||跑下测试"
    check_template = [
        # 填写参数规则
    ]
    message_type = "text"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "test_env",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        import re
        command = kwargs.get("command")
        addr = re.findall(r'[0-9]+(?:\.[0-9]+){3}:[0-9]+', command)[0]
        smart_cdc = "false"
        core = "true"
        t1 = "普通"
        t2 = "测试任务会进行常规数据库全量测试, 少数数据库进行增量测试"
        t3 = "核心数据库"
        t4 = "十几"
        if "全面" in command or "智能" in command or "smart cdc" in command or "大力" in command:
            smart_cdc = "true"
            t1 = "大力"
            t2 = "测试任务会智能启动尽可能多的源与目标进行实时增量测试"
            t4 = "四五十"
        if "全部" in command or "包含全部":
            core = "false"
            t3 = "全部数据库"
        message = "对 {} 进行 {} 测试的任务已经下发, {}, 测试包含 {}, 请等待测试结果发送, 大约 {} 分钟, 如果没有发送, 一般是测试严重不通过, 你可以打开 https://github.com/tapdata/tapdata-enterprise/actions/workflows/test_env.yaml 手动查看下结果".format(
            addr, t1, t2, t3, t4)
        requests.post(
            "https://api.github.com/repos/tapdata/tapdata-enterprise/actions/workflows/test_env.yaml/dispatches",
            headers={
                "Accept": "application/vnd.github+json",
                "Authorization": "Bearer ****************************************",
                "X-GitHub-Api-Version": "2022-11-28",
            },
            json={
                "ref": "master",
                "inputs": {"ADDR": addr, "FEISHU_CHAT_ID": open_id, "SMART_CDC": smart_cdc, "CORE": core}
            }
        )
        return message, True
