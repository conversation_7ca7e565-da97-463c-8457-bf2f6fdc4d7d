"""
watchOpensource 命令

@author: 
@date: 2024.03.22
"""

import os
import time
import traceback

from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, option, common
from utils import api
from lib.api import send_text


@CommandParser.register
class Watchopensource(Handler):
    command = "watchOpensource"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "watchOpensource",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到

        }
        self.concurrency_limit = True

    def send_run_link(self, runner_id, detail_url):
        message_card = mc.make_workflow_scheduling_notes(runner_id, detail_url)
        send_text(self.message_type, self.open_id, message_card.to_dict(), self.chat_type)

    def create_job_success_message(self, result: bool, **kwargs):
        if result:
            send_text(self.message_type, self.open_id,
                      common.field_list_message_card(
                          header="🎉 构建任务创建成功，执行成功后会通知你哦",
                          color="green",
                      ).to_dict(),
                      self.chat_type)
        else:
            send_text(self.message_type, self.open_id,
                      common.field_list_message_card(
                          header="😭 构建任务创建失败",
                          color="red",
                          处理方式="自行查看Github流程执行情况"
                      ).to_dict(),
                      self.chat_type)

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        tapdata_branch, tapdata_frontend_branch, tapdata_connectors_branch = \
            option.OpenSourceBranch(self.job_id, open_id).get_option(), \
                option.FrontendBranch(self.job_id, open_id).get_option(), \
                option.ConnectorsBranch(self.job_id, open_id).get_option()
        workflow_id, result = api.GithubApi('tapdata', 'tapdata').workflow_dispatch_trigger_and_wait(
            'build.yml',
            tapdata_branch,
            send_create_job_result=self.create_job_success_message,
            send_detail_info=self.send_run_link,
            frontend_branch=tapdata_frontend_branch,
            connectors_branch=tapdata_connectors_branch,
        )
        if result:
            message = common.field_list_message_card(
                header="构建任务运行成功",
                color="green",
                is_short=False,
                制品包="点击流程链接获取",
                流程链接=f"https://github.com/tapdata/tapdata/actions/runs/{workflow_id}",
            )
        else:
            message = common.field_list_message_card(
                header="😭 流程执行失败了",
                color="red",
                is_short=False,
                处理方式="自行查看Github流程执行情况",
                流程链接=f"https://github.com/tapdata/tapdata/actions/runs/{workflow_id}",
            )
        return message.to_dict(), True
