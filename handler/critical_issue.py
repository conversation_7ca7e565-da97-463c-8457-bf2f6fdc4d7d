"""
critical_issue 命令

@author: 
@date: 2022.11.24
"""
import datetime

from utils.schdule import CommandParser
from utils.handler import Handler
import requests
from utils.common import get_time_range, field_list_message_card, time_readable

@CommandParser.register
class Critical_issue(Handler):
    token = "9bb7d35bf5fd952fac672e2dc9897392a747541b"
    command = "紧急工单情况||紧急工单处理情况||高级工单情况||工单情况||工单处理情况||高级工单处理情况||高级工单情况||所有工单处理情况||所有工单情况"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "critical_issue",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        command = kwargs.get("command")
        time_range = get_time_range(command)
        issues = []

        # 默认取紧急工单, 如果没有指定紧急, 取紧急和高的工单
        priority = []
        title_priority = ""
        if "紧急" in command:
            priority.append("3")
            title_priority = "紧急"
        if "高级" in command:
            priority.append("2")
            title_priority += "和高级"
        page_number = 1
        request_ok = False
        while True:
            if request_ok:
                break
            # 这里默认按照 Code 倒序
            data = {
                "Action": "DescribeIssueListWithPage",
                "ProjectName": "tapdata",
                "IssueType": "DEFECT",
                "PageNumber": page_number,
                "PageSize": 500,
            }
            if priority is not None and len(priority) > 0:
                data["Conditions"] = [{"Key": "PRIORITY", "Value": priority}]
            page_issues = requests.post(
                url="https://tapdata.coding.net/open-api?Action=DescribeIssueListWithPage",
                headers={"Authorization": "token " + self.token},
                json=data,
            ).json().get("Response", {}).get("Data", {}).get("List", [])
            page_number += 1
            for issue in page_issues:
                if not issue.get("Name", "").startswith("#"):
                    continue
                # 取到创建时间小于给定时间范围小值的时候, 说明请求可以停止
                if issue.get("CreatedAt", 0) / 1000 <= time_range[0]:
                    request_ok = True
                if issue.get("CreatedAt", 0) / 1000 <= time_range[0] or issue.get("CreatedAt", 0) / 1000 > time_range[1]:
                    continue
                issues.append(issue)

        months_group = {}
        is_month_group = False
        if "按月" in kwargs.get("command") or "按照月份" in kwargs.get("command"):
            is_month_group = True

        # 全部的值, 在 0 里面记一份
        months_group["0"] = {
            "fix_cost": 0,
            "fix_number": 0,
            "number": 0,
            "two_days_fix_number": 0,
            "fix_percent": 100,
            "avg_fix_cost": 0,
            "two_days_fix_percent": 100,
        }
        for issue in issues:
            created_at = issue.get("CreatedAt")
            created_month = datetime.datetime.fromtimestamp(created_at/1000).strftime("%Y%m")

            keys = ["0", created_month]
            for k in keys:
                if months_group.get(k) is None:
                    months_group[k] = {
                        "fix_cost": 0,
                        "fix_number": 0,
                        "number": 1,
                        "two_days_fix_number": 0,
                    }
            for k in keys:
                months_group[k]["number"] += 1

            if issue.get("IssueStatusName", "") in ["已关闭", "已发布", "挂起", "已拒绝", "已完成"]:
                fix_at = issue.get("CompletedAt")
                if fix_at == 0:
                    fix_at = issue.get("UpdatedAt")
                this_fix_cost = (fix_at - issue.get("CreatedAt")) / 1000

                for k in keys:
                    months_group[k]["fix_number"] += 1
                    months_group[k]["fix_cost"] += this_fix_cost
                # 额外记录一下解决时间在 2 天内的占比
                if this_fix_cost <= 86400 * 2:
                    for k in keys:
                        months_group[k]["two_days_fix_number"] += 1

        for k, fix in months_group.items():
            fix_percent = 100
            if fix["number"] > 0:
                fix_percent = int(fix["fix_number"] / fix["number"] * 100)
            avg_fix_cost = 0
            if fix["number"] > 0:
                avg_fix_cost = time_readable(fix["fix_cost"] / fix["number"])
            two_days_fix_percent = 100
            if fix["number"] > 0:
                two_days_fix_percent = int(fix["two_days_fix_number"] / fix["number"] * 100)
            fix["fix_percent"] = fix_percent
            fix["avg_fix_cost"] = avg_fix_cost
            fix["two_days_fix_percent"] = two_days_fix_percent
        start_date = datetime.datetime.fromtimestamp(time_range[0]).strftime("%Y年 %m月 %d号")
        end_date = datetime.datetime.fromtimestamp(time_range[1]).strftime("%Y年 %m月 %d号")
        if "紧急" in command:
            if not is_month_group:
                message = field_list_message_card(
                    header="🛠  {} 到 {} 的 {} 工单处理情况如下: ".format(start_date, title_priority, end_date),
                    color="blue",
                    is_short=True,
                    总数量=months_group["0"]["number"],
                    解决数量=months_group["0"]["fix_number"],
                    解决率="{}%".format(months_group["0"]["fix_percent"]),
                    平均解决时间=months_group["0"]["avg_fix_cost"],
                    解决时间在两天内的工单数=months_group["0"]["two_days_fix_number"],
                    解决时间在两天内的占比="{}%".format(months_group["0"]["two_days_fix_percent"]),
                )
            else:
                sort_months = sorted(months_group)
                group_number = ""
                group_fix_number = ""
                group_fix_percent = ""
                group_fix_avg_cost = ""
                group_two_days_percent = ""
                for m in sort_months:
                    if m == "0":
                        continue
                    group_number = "{},{}".format(group_number, months_group[m]["number"])
                    group_fix_number = "{},{}".format(group_fix_number, months_group[m]["fix_number"])
                    fix_percent = 100
                    if months_group[m]["number"] > 0:
                        fix_percent = int(months_group[m]["fix_number"] / (months_group[m]["number"]+1) * 100)
                    group_fix_percent = "{},{}%".format(group_fix_percent, fix_percent)
                    fix_avg_cost = 0
                    if months_group[m]["number"] > 0:
                        fix_avg_cost = months_group[m]["fix_cost"] / (months_group[m]["fix_number"]+1)
                    group_fix_avg_cost = "{},{}".format(group_fix_avg_cost, time_readable(fix_avg_cost))
                    two_days_percent = 100
                    if months_group[m]["number"] > 0:
                        two_days_percent = int(months_group[m]["two_days_fix_number"] / months_group[m]["number"] * 100)
                    group_two_days_percent = "{},{}%".format(group_two_days_percent, two_days_percent)

                message = field_list_message_card(
                    header="🛠  {} 到 {} 的 {} 工单处理情况如下: ".format(start_date, title_priority, end_date),
                    color="blue",
                    is_short=True,
                    总数量=months_group["0"]["number"],
                    解决数量=months_group["0"]["fix_number"],
                    解决率="{}%".format(months_group["0"]["fix_percent"]),
                    平均解决时间=months_group["0"]["avg_fix_cost"],
                    解决时间在两天内的工单数=months_group["0"]["two_days_fix_number"],
                    解决时间在两天内的占比="{}%".format(months_group["0"]["two_days_fix_percent"]),
                    按月汇总总数量=group_number.lstrip(","),
                    按月汇总解决数量=group_fix_number.lstrip(","),
                    按月汇总解决率=group_fix_percent.lstrip(","),
                    按月汇总平均解决时间=group_fix_avg_cost.lstrip(","),
                    按月汇总两天内解决百分比=group_two_days_percent.lstrip(","),
                )
        else:
            h = ""
            if "高级" in command:
                h = "高级"
            if not is_month_group:
                message = field_list_message_card(
                    header="🛠  {} 到 {} 的 {} 工单处理情况如下: ".format(start_date, end_date, h),
                    color="blue",
                    is_short=True,
                    总数量=months_group["0"]["number"],
                    解决数量=months_group["0"]["fix_number"],
                    解决率="{}%".format(months_group["0"]["fix_percent"]),
                )
            else:
                sort_months = sorted(months_group)
                group_number = ""
                group_fix_number = ""
                group_fix_percent = ""
                group_fix_avg_cost = ""
                group_two_days_percent = ""
                for m in sort_months:
                    if m == "0":
                        continue
                    group_number = "{},{}".format(group_number, months_group[m]["number"])
                    group_fix_number = "{},{}".format(group_fix_number, months_group[m]["fix_number"])
                    fix_percent = 100
                    if months_group[m]["number"] > 0:
                        fix_percent = int(months_group[m]["fix_number"] / months_group[m]["number"] * 100)
                    group_fix_percent = "{},{}%".format(group_fix_percent, fix_percent)
                    fix_avg_cost = 0
                    if months_group[m]["number"] > 0:
                        fix_avg_cost = months_group[m]["fix_cost"] / months_group[m]["fix_number"]
                    group_fix_avg_cost = "{},{}".format(group_fix_avg_cost, time_readable(fix_avg_cost))
                    two_days_percent = 100
                    if months_group[m]["number"] > 0:
                        two_days_percent = int(
                            months_group[m]["two_days_fix_number"] / months_group[m]["number"] * 100)
                    group_two_days_percent = "{},{}%".format(group_two_days_percent, two_days_percent)
                message = field_list_message_card(
                    header="🛠  {} 到 {} 的{}工单处理情况如下: ".format(start_date, end_date, h),
                    color="blue",
                    is_short=True,
                    总数量=months_group["0"]["number"],
                    解决数量=months_group["0"]["fix_number"],
                    解决率="{}%".format(months_group["0"]["fix_percent"]),
                    按月汇总总数量=group_number.lstrip(","),
                    按月汇总解决数量=group_fix_number.lstrip(","),
                    按月汇总解决率=group_fix_percent.lstrip(","),
                )
        return message.to_dict(), True