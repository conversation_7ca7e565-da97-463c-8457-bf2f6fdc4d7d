"""
release_version 命令

@author: 
@date: 2023.02.02
"""
from utils import common
from utils.schdule import CommandParser
from utils.handler import Handler
import time
import requests
import json

from model.task import Task
from utils.init import app, db
from lib.api import send_text

sam = "ou_f29f30714e57211bcc230eba39c407b1"
leon = "ou_35609f620a43ddecc208a4d7f85d1fd1"
jarad = "ou_562c37b89f53e858eb5f5fbcad1f97c5"
jerry = "ou_677b941f814ae3c4235d046a1f8f5478"
berry = "ou_b05a61325c928af129539a4474c881b2"
peng = "ou_b998848146bfcda565cdcd8b8f92c439"
xufei = "ou_789b17fedcd7bb6184ddf9c68b321203"
martin = "ou_c7a662fadc8e06198a39ac7d329b8109"
jackin = "ou_9402d9587684b4d7c5cfc1085e2617e6"
tony = "ou_894a699805e516d48b95b97c05b9b992"

#sam = leon = jarad = jerry = peng = xufei = martin = jackin = berry


def get_lark_header():
    base_url = "https://open.feishu.cn/open-apis"
    headers = {"content-type": "application/json; charset=utf-8"}
    app_id = "cli_a26d15bc2bf99013"
    app_secret = "6JYz2K0sCV0rMmI6OkMkyhSgnaBZY7PA"
    url = base_url + "/auth/v3/tenant_access_token/internal"
    data = {
        "app_secret": app_secret,
        "app_id": app_id
    }
    for i in range(3):
        response = requests.post(url, headers=headers, json=data, timeout=10)
        if response.json()['code'] == 0:
            token = response.json()['tenant_access_token']
            headers.update({
                "Authorization": f"Bearer {token}"
            })
        else:
            continue
    return headers

@CommandParser.register
class Release_version(Handler):
    command = "发布||版本发布||发布版本"
    check_template = [
    ]
    message_type = "text"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "release_version",  # 命令示例
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True


    def get_latest_release_branch(self, repo_url):
        import subprocess
        # 使用 git ls-remote 获取所有远程分支
        result = subprocess.run(["git", "ls-remote", "--heads", repo_url], text=True, capture_output=True, check=True)

        # 处理命令输出，筛选出以 'release-v' 开头的分支
        branches = result.stdout.splitlines()
        release_branches = []
        for line in branches:
            parts = line.split()
            if parts and 'refs/heads/release-v' in parts[1]:
                release_branches.append(parts[1].split('/')[-1])

        # 排序并返回最新的一个符合条件的分支（按名称排序）
        release_branches.sort(reverse=True)
        if release_branches:
            return release_branches[0]
        else:
            return None


    def get_active_sprint(self):
        token = "ATATT3xFfGF01Xlg_FWptPLRRUwZ0szNctX257VXj7dTU-SwLLcp4FZquz_16mrJ9TXnkbI2dfyzPhtpH8icSQmKmH5IAXzSLhM4leHJ3FUevuzcqcAmT9Gpm5VRAcnBQDuldD82fosVmaW0Q2qgmCiY9gDIbhSKX6zFEgYEMpaeZlJdGihb-ns=847D503E"
        url1 = "https://tapdata.atlassian.net/rest/agile/1.0/"
        from requests.auth import HTTPBasicAuth
        auth3 = HTTPBasicAuth("<EMAIL>", token)
        jira_retry = 3
        jira_timeout = 10
        url = "{}/board/4/sprint?state=active".format(url1)
        for i in range(jira_retry):
            try:
                response = requests.get(
                    url,
                    auth=auth3,
                    timeout=jira_timeout
                )
                return response.json().get("values")[0]["name"].split()[-1]
            except Exception as e:
                continue
        return 0


    def increment_last_number(self, s):
        # 将字符串分割成三部分
        parts = s.split('.')
        if len(parts) != 3:
            raise ValueError("Input string must be in the format a.b.c")

        # 将第三部分（c）转换为整数，增加1
        try:
            parts[2] = str(int(parts[2]) + 1)
        except ValueError:
            raise ValueError("The last part of the string must be a number")

        # 将更新后的部分重新组合成一个字符串并返回
        return '.'.join(parts)


    def get_release_version(self, command):
        import re
        import os
        # 从 command 中, 正则匹配 a.b.c 或 a.b 这种模式, a b c 都是数字
        pattern = r'\d+\.\d+(?:\.\d+)?'
        result = re.findall(pattern, command)
        if len(result) >= 1:
            return result[0] + ".0"
        # 从 git 中, 获取最新的以 release-v 开头的分支, 并返回
        latest_release_branch = self.get_latest_release_branch("https://github.com/tapdata/tapdata.git")
        if not latest_release_branch:
            return None
        result = re.findall(pattern, latest_release_branch)
        if len(result) == 0:
            return None

        return self.increment_last_number(result[0])

    def finish_task(self, task_guid):
        url = "https://open.feishu.cn/open-apis/task/v2/tasks/{}".format(task_guid)
        headers = get_lark_header()
        res = requests.request("PATCH", url, headers=headers, data=json.dumps({"completed_at": int(time.time())*1000}))
        if res.status_code != 200:
            return False
        code = res.json().get("code")
        if code != 0:
            return False
        return True

    def create_task(self, task, parent_task=None, dependencies=None):
        task_guid = None
        if parent_task is None:
            url = "https://open.feishu.cn/open-apis/task/v2/tasks?user_id_type=open_id"
            k = "task"
        else:
            url = "https://open.feishu.cn/open-apis/task/v2/tasks/{}/subtasks?user_id_type=open_id".format(parent_task)
            k = "subtask"

        headers = get_lark_header()
        res = requests.request("POST", url, headers=headers, data=json.dumps(task))
        if res.status_code != 200:
            return None
        code = res.json().get("code")
        if code != 0:
            return None
        task_guid = res.json().get("data", {}).get(k, {}).get("guid")
        if not task_guid:
            return None

        if dependencies is not None:
            url = "https://open.feishu.cn/open-apis/task/v2/tasks/{}/add_dependencies".format(task_guid)
            for dependency in dependencies:
                payload = json.dumps({
                    "dependencies": [
                        {
                            "type": "prev",
                            "task_guid": dependency,
                        }
                    ]
                })
                res = requests.request("POST", url, headers=headers, data=payload)

        db.session.add(
            Task(id=task_guid, summary=task.get("summary", ""), status="todo", release_version=self.release_version)
        )
        db.session.commit()

        return task_guid

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        command = kwargs.get("command")
        release_version = self.get_release_version(command)
        self.release_version = release_version

        # 向发布版本分支关系表插入当前版本的分支映射关系
        # tapdata,tapdata-enterprise,tapdata-web 为release-v{release_version},其余为main
        # 旧版本的分支关系由Jerry手动维护

        # cache错误，防止影响后续版本发布，同时将错误信息发给Jerry
        try:
            common.insert_release_info(db, app, release_version)
        except Exception as e:
            app.logger.info(e)
            send_text("text", leon, "发布版本同时插入新版本分支映射失败", "p2p")

        if not release_version:
            return "未能获取到发布版本号, 请使用命令: 发布 v3.5.15 调用此工具", False

        active_sprint = self.get_active_sprint()
        if not active_sprint:
            return "未能获取到当前活跃的 sprint, 请稍候再试", False

        task = {
            "summary": "sprint #{}, release-v{} Release Task".format(active_sprint, release_version),
            "description": "请各位在本周完成发版所需工作",
            "due": {
                "timestamp": (int(time.time()) + 2 * 24 * 3600) * 1000,
                "is_all_day": True
            },
            "members": [
                {
                    "id": berry,
                    "type": "user",
                    "role": "assignee"
                },
                {
                    "id": martin,
                    "type": "user",
                    "role": "assignee"
                },
                {
                    "id": tony,
                    "type": "user",
                    "role": "assignee"
                }
            ],
            "mode": 2,
            "is_milestone": True
        }
        task_guid = self.create_task(task)
        if not task_guid:
            return "创建发版任务失败, 请稍候再试", False

        task_1 = {
            "summary": "1. review and merge pull request",
            "description": "请检查并处理所有需要的代码合并请求",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 5) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": sam,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }

        task_1_guid = self.create_task(task_1, task_guid)

        task_1_1 = {
            "summary": "1-1. review and merge all requests to develop branch (tapdata and tapdata-enterprise repo)",
            "description": "请检查并处理所有到 develop 分支的合并请求(包括 tapdata 和 tapdata-enterprise 仓库)",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 5) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": sam,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        self.create_task(task_1_1, task_1_guid)

        task_1_2 = {
            "summary": "1-2. review and merge all requests to develop branch (tcm repo)",
            "description": "请检查并处理所有到 develop 分支的合并请求(包括 tcm 仓库)",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 5) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": leon,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        self.create_task(task_1_2, task_1_guid)

        task_1_3 = {
            "summary": "1-3. review and merge all requests to develop branch (web repo)",
            "description": "请检查并处理所有到 develop 分支的合并请求(包括 web 仓库)",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 5) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": xufei,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        self.create_task(task_1_3, task_1_guid)

        task_1_4 = {
            "summary": "1-4. review and merge all requests to develop branch (connectors and enterprise connectors repo)",
            "description": "请检查并处理所有到 develop 分支的合并请求(包括数据源与企业数据源仓库)",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 5) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": jarad,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        self.create_task(task_1_4, task_1_guid)

        task_1_5 = {
            "summary": "1-5. merge code from main -> develop for tapdata and tapdata-enterprise repo",
            "description": "请将 main 分支的代码合并到 develop 分支(包括 tapdata 与 tapdata-enterprise 仓库)",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 5) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": sam,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        self.create_task(task_1_5, task_1_guid)

        task_1_6 = {
            "summary": "1-6. merge code from main -> develop for connectors repo",
            "description": "请将 main 分支的代码合并到 develop 分支(包括 tapdata-connectors 与 tapdata-enterprise-connectors 仓库)",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 5) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": jarad,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        self.create_task(task_1_6, task_1_guid)

        task_2 = {
            "summary": "2. run taptest in develop branch(task 1 must be done before this task)",
            "description": "在 develop 分支运行自动化测试, 并发出测试报告(在此之前必须完成 1 任务)",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 12) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": berry,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        task_2_guid = self.create_task(task_2, task_guid, [task_1_guid])

        task_3 = {
            "summary": "3. update op test env using develop branch(task 1 must be done before this task)",
            "description": "更新 op 版本的测试环境(在此之前必须完成 1 任务)",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 18) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": tony,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        task_3_guid = self.create_task(task_3, task_guid, [task_1_guid])

        task_4 = {
            "summary": "4. manual test new features in op env(task 3 must be done before this task)",
            "description": "在 op 版本的测试环境中进行手动测试(在此之前必须完成 3 任务)",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 24) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": martin,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        task_4_guid = self.create_task(task_4, task_guid, [task_3_guid])

        task_5 = {
            "summary": "5. run testsigma in op env, and check result(task 3 must be done before this task)",
            "description": "在 op 版本的测试环境中进行 testsigma 的测试并验证结果(在此之前必须完成 3 任务)",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 24) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": peng,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        task_5_guid = self.create_task(task_5, task_guid, [task_3_guid])

        task_6 = {
            "summary": "6. merge code from develop -> main, and create release-v{} branch from main".format(
                release_version),
            "description": "请发起 develop 到 main 分支的 PR 请求, 并在 PR 合并完成后, 从 main 分支拉出 release-v{} 分支".format(
                release_version),
            "due": {
                "timestamp": (int(time.time()) + 3600 * 24) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": sam,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        task_6_guid = self.create_task(task_6, task_guid,
                                       [task_1_guid, task_2_guid, task_3_guid, task_4_guid, task_5_guid])

        task_6_1 = {
            "summary": "6-1. merge code from develop -> main, and create release-v{} branch from main (tapdata and tapdata-enterprise repo)".format(
                release_version),
            "description": "请发起 develop 到 main 分支的 PR 请求, 并在 PR 合并完成后, 从 main 分支拉出 release-v{} 分支 (包括 tapdata 与 tapdata-enterprise 仓库)".format(
                release_version),
            "due": {
                "timestamp": (int(time.time()) + 3600 * 24) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": sam,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        self.create_task(task_6_1, task_6_guid, [task_1_guid, task_2_guid, task_3_guid, task_4_guid, task_5_guid])

        task_6_2 = {
            "summary": "6-2. merge code from develop -> main, and create release-v{} branch from main (tcm repo)".format(
                release_version),
            "description": "请发起 develop 到 main 分支的 PR 请求, 并在 PR 合并完成后, 从 main 分支拉出 release-v{} 分支 (包括 tcm 仓库)".format(
                release_version),
            "due": {
                "timestamp": (int(time.time()) + 3600 * 24) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": leon,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        self.create_task(task_6_2, task_6_guid, [task_1_guid, task_2_guid, task_3_guid, task_4_guid, task_5_guid])

        task_6_3 = {
            "summary": "6-3. merge code from develop -> main, and create release-v{} branch from main (web repo)",
            "description": "请发起 develop 到 main 分支的 PR 请求, 并在 PR 合并完成后, 从 main 分支拉出 release-v{} 分支 (包括 web 仓库)".format(
                release_version),
            "due": {
                "timestamp": (int(time.time()) + 3600 * 24) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": xufei,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        self.create_task(task_6_3, task_6_guid, [task_1_guid, task_2_guid, task_3_guid, task_4_guid, task_5_guid])

        task_6_4 = {
            "summary": "6-4. merge code from develop -> main (connectors and enterprise connectors repo)",
            "description": "请发起 develop 到 main 分支的 PR 请求, 并确保其被合并(包括数据源与企业数据源仓库)",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 24) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": jarad,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        self.create_task(task_6_4, task_6_guid, [task_1_guid, task_2_guid, task_3_guid, task_4_guid, task_5_guid])

        task_7 = {
            "summary": "7. update env using release-v{} branch".format(release_version),
            "description": "请使用 release-v{} 分支更新环境".format(release_version),
            "due": {
                "timestamp": (int(time.time()) + 3600 * 48) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": tony,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        task_7_guid = self.create_task(task_7, task_guid, [task_6_guid])
        task_7_1 = {
            "summary": "7-1. update op test env using release-v{} branch".format(release_version),
            "description": "请使用 release-v{} 分支更新企业版测试环境".format(release_version),
            "due": {
                "timestamp": (int(time.time()) + 3600 * 48) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": tony,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        task_7_1_guid = self.create_task(task_7_1, task_7_guid, [task_6_guid])

        task_7_2 = {
            "summary": "7-2. update cloud grey env using release-v{} branch".format(release_version),
            "description": "请使用 release-v{} 分支更新云版灰度环境".format(release_version),
            "due": {
                "timestamp": (int(time.time()) + 3600 * 48) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": tony,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        task_7_2_guid = self.create_task(task_7_2, task_7_guid, [task_6_guid])

        task_8 = {
            "summary": "8. run test in env, and make sure it works",
            "description": "请在环境中进行测试, 并确保其验证通过",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 48) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": martin,
                    "type": "user",
                    "role": "assignee"
                },
                {
                    "id": jackin,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        task_8_guid = self.create_task(task_8, task_guid)

        task_8_1 = {
            "summary": "8-1. run manual test in op env, and make sure it works",
            "description": "请在 op 测试环境中手动验证新功能, 并确保其验证通过",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 48) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": martin,
                    "type": "user",
                    "role": "assignee"
                },
                {
                    "id": jackin,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        self.create_task(task_8_1, task_8_guid, [task_7_1_guid])
        task_8_2 = {
            "summary": "8-2. run testsigma in op env, and make sure it works",
            "description": "请在 op 测试环境中运行 testsigma, 并确保其验证通过",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 48) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": peng,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        self.create_task(task_8_2, task_8_guid, [task_7_1_guid])
        task_8_3 = {
            "summary": "8-3. run manual test in grey env, and make sure it works",
            "description": "请在云版灰度环境中手动验证新功能, 并确保功能验证通过",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 48) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": martin,
                    "type": "user",
                    "role": "assignee"
                },
                {
                    "id": jackin,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        self.create_task(task_8_3, task_8_guid, [task_7_2_guid])
        task_8_4 = {
            "summary": "8-4. run testsigma in grey env, and make sure it works",
            "description": "请在 云版灰度 测试环境中运行 testsigma, 并确保其测试通过",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 48) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": peng,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        self.create_task(task_8_4, task_8_guid, [task_7_2_guid])

        task_9 = {
            "summary": "9. build opensource image, and make sure it works",
            "description": "请构建开源版本的镜像, 并确保其组件可启动, 在这里粘贴镜像地址",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 96) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": tony,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        task_9_guid = self.create_task(task_9, task_guid)

        task_10 = {
            "summary": "10. release readme document",
            "description": "请更新 release 文档, 并粘贴链接到评论中",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 96) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": martin,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        task_10_guid = self.create_task(task_10, task_guid)

        task_11 = {
            "summary": "11. tag for repos",
            "description": "请在对应仓库下打 tag, 以保证版本号准确",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 96) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": martin,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        task_11_guid = self.create_task(task_11, task_guid)

        task_11_1 = {
            "summary": "11-1. tag for tcm repo",
            "description": "请在 tcm 仓库下打 tag, 以保证线上版本号更新",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 96) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": leon,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        task_11_1_guid = self.create_task(task_11_1, task_11_guid)

        task_11_2 = {
            "summary": "11-2. tag for tapdata release repo",
            "description": "请在 tapdata 仓库下打 发版 tag, 以保证企业版版本号更新",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 96) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": sam,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        task_11_2_guid = self.create_task(task_11_2, task_11_guid)

        task_11_3 = {
            "summary": "11-3. tag for tapdata develop repo",
            "description": "请在 tapdata 仓库下打 develop tag, 以保证新迭代开发时版本号准确",
            "due": {
                "timestamp": (int(time.time()) + 3600 * 96) * 1000,
                "is_all_day": False
            },
            "members": [
                {
                    "id": sam,
                    "type": "user",
                    "role": "assignee"
                },
            ],
            "mode": 2,
            "is_milestone": False
        }
        task_11_3_guid = self.create_task(task_11_3, task_11_guid)

        return "已创建任务: sprint #{}, release-v{} Release Task, 并为各位同学创建发版任务, 预祝发版顺利 ~_~".format(active_sprint, release_version), True
