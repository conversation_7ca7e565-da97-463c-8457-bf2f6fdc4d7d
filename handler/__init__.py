"""
自动导入handler模块

@author: <PERSON>
@date: 2022.08.18
"""
import sys
import traceback
from importlib import import_module
import os


def init_handle(parent_module="handler", sub_dir="."):
    path_exclude = ["__init__.py", "__pycache__", "unsupport", ".DS_Store"]
    files = os.listdir(os.sep.join([os.path.dirname(__file__), sub_dir]))
    for file in files:
        if file in path_exclude:
            continue
        elif os.path.isdir(os.sep.join([os.path.dirname(__file__), file])):
            init_handle(parent_module=f"{parent_module}.{file}", sub_dir=file)
            continue
        module = file.replace(".py", "")
        try:
            pkg = import_module(f"{parent_module}.{module}")
        except ModuleNotFoundError:
            print(traceback.format_exc())
            continue
        getattr(pkg, f"{module.capitalize()}")()
