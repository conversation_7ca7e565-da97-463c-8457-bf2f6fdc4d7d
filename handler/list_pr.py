"""
list_pr 命令

@author: 
@date: 2022.12.01
"""

import os
import time
import traceback
import requests

from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc
from utils.common import field_list_message_card, github_map_feishu
from lib.api import message_api_client



@CommandParser.register
class List_pr(Handler):
    command = "我想看 PR||我还有多少 PR 没看||开始看代码了||看一下PR"
    token = "****************************************"
    check_template = [
        # 填写参数规则
    ]
    message_type = "text"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "list_pr",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        user = message_api_client.get_user_by_user_id("open_id", open_id)
        if user is None:
            return
        user_id = user.get("user_id")
        if user_id is None:
            return
        github_user = None
        for k, v in github_map_feishu.items():
            if len(v) < 2:
                continue
            if v[0] == user_id:
                github_user = k
                break
        if github_user is None:
            return

        repos = [
            "tapdata",
            "tapdata-enterprise",
            "tapdata-enterprise-web"
        ]
        text = ""
        for repo in repos:
            prs = requests.get(
                url="https://api.github.com/repos/tapdata/{}/pulls".format(repo),
                params={
                    "state": "open",
                },
                headers={
                    "Accept": "application / vnd.github + json",
                    "Authorization": "Bearer " + self.token,
                    "X-GitHub-Api-Version": "2022-11-28"
                }
            ).json()
            if len(prs) == 0:
                continue
            for pr in prs:
                add = False
                assignees = pr.get("assignees", [])
                reviewers = pr.get("requested_reviewers", [])
                for assignee in assignees:
                    if assignee.get("login") == github_user:
                        add = True
                        break
                for reviewer in reviewers:
                    if reviewer.get("login") == github_user:
                        add = True
                        break
                if not add:
                    continue
                if text == "":
                    text = "需要你处理的代码合并请求列表如下, 请及时处理: \r\n"
                text = text + pr.get("title")[0:35] + ": " + pr.get("html_url") + "\r\n"

        if text == "":
            text = "目前没有需要你处理的代码合并请求 ~"

        return text, True