"""
test_branch 命令

@author: 
@date: 2022.12.22
"""

import os
import time
import traceback

from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc


@CommandParser.register
class Test_branch(Handler):
    command = "测试下分支||测一下分支||测试一下分支情况||测试一下这个分支||测一下这个分支||测试一下分支"
    check_template = [
        # 填写参数规则
    ]
    message_type = "text"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "test_branch",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        import re, requests
        command = kwargs.get("command")
        if "分支:" not in command:
            return "无法识别分支名称, 示例语法: 测试一下分支: develop-v2.12", True

        branch = command.split("分支:")[1].split(",")[0].strip()
        op_branch = branch
        web_branch = branch
        opensource_branch = branch
        try:
            op_branch = command.split("企业分支:")[1].split(",")[0]
        except Exception as e:
            pass
        try:
            web_branch = command.split("前端分支:")[1].split(",")[0]
        except Exception as e:
            pass
        try:
            opensource_branch = command.split("开源分支:")[1].split(",")[0]
        except Exception as e:
            pass

        if op_branch == "" or web_branch == "" or opensource_branch == "":
            return "无法识别分支名称, 示例语法: 测试一下分支: develop-v2.12", True

        message = "对 企业分支: {}, 开源分支: {}, 前端分支: {} 代码进行测试的任务已经下发, 请等待测试结果发送, 由于需要进行编译, 所需时间略长, 一般在 40min 左右, 如果没有发送, 一般是测试严重不通过, 你可以打开 https://github.com/tapdata/tapdata-enterprise/actions/workflows/test_night.yaml 手动查看下结果".format(
        op_branch, opensource_branch, web_branch)

        requests.post(
            "https://api.github.com/repos/tapdata/tapdata-enterprise/actions/workflows/test_night.yaml/dispatches",
            headers={
                "Accept": "application/vnd.github+json",
                "Authorization": "Bearer ****************************************",
                "X-GitHub-Api-Version": "2022-11-28",
            },
            json={
                "ref": op_branch,
                "inputs": {"FRONTEND_BRANCH": web_branch, "OPENSOURCE_BRANCH": opensource_branch, "FEISHU_CHAT_ID": open_id}
            }
        )
        return message, True
