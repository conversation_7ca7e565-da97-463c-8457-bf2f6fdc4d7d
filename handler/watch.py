"""
watch 命令

@author: <PERSON>
@date: 2022.08.22
"""

import os
import time
import traceback
from datetime import datetime

from utils.handler import Handler
from lib.api import send_text
from utils.api import GithubApi, K8sApi
from utils.init import app
from utils import message_card as mc, common
from utils.message_card import trigger_button
from utils.schdule import CommandParser, Schedule, Context
from utils.session import Session


@CommandParser.register
class Watch(Handler):
    command = "watch"
    check_template = []
    message_type = "message_card"
    help = {
        "format": "watch cicd/env_update/env_create 值",
        "说明": "触发监听任务执行，并且在执行成功/失败后发送通知",
        "watch cicd $runner_id": "监听指定runner_id的流程，超时时间30分钟",
        "watch env_update/env_create $name": "监听指定环境名称的环境更新/创建进度，超时时间为15分钟"
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {
            "is_cloud": False,
            "namespace": "dev",
            "image_repo": "tapdata-docker.pkg.coding.net/tapdata/tapdata",
            "is_package_tar": False,
            "is_docker_save": False,
            "image_repo_optional": "",
        }

    def _watch_cicd(self, run_id, is_cloud=False):
        owner = os.getenv("OWNER")
        repo = os.getenv("TRIGGER_REPO")
        github_obj = GithubApi(owner, repo)
        res = github_obj.get_runner_info(run_id)
        app.logger.info("github action status is: %s, conclusion is %s" % (res["status"], res.get("conclusion", None)))
        if res["status"] == "completed" and res.get("conclusion", None) == "success":
            if is_cloud:
                tm_java_tag, tapdata_agent_tag, agent_tag, console_tag, tcm_tag, cloud_tag = \
                    github_obj.runner_branch_info_cloud(run_id, image_repo=self.magic_arg.get("image_repo"))
                return (tm_java_tag, tapdata_agent_tag, agent_tag, console_tag, tcm_tag, cloud_tag), True
            else:
                print(self.magic_arg)
                image_repo_optional = self.magic_arg.get("image_repo_optional")
                # image_repo_optional = f"{push_to_github}%{push_to_coding}%{push_to_qingcloud}"
                push_to_github, push_to_coding, push_to_qingcloud = image_repo_optional.split("%")
                if push_to_qingcloud == "True":
                    image_repo = "dockerhub.qingcloud.com/tapdata"
                elif push_to_coding == "True":
                    image_repo = "tapdata-docker.pkg.coding.net/tapdata/enterprise"
                else:
                    image_repo = "ghcr.io/tapdata"
                package_image_tag, current_branch, opensource_branch, frontend_branch, tag = \
                    github_obj.runner_branch_info_enterprise(run_id, image_repo=image_repo)
                return (package_image_tag, current_branch, opensource_branch, frontend_branch, tag), True
        elif res["status"] == "completed" and res.get("conclusion", None) == "failure":
            return "failure", True
        elif res["status"] == "completed" and res.get("conclusion", None) == "cancelled":
            return "cancelled", True
        else:
            return None, None

    def make_workflow_scheduling_notes(self, runner_id, detail_url):
        message_card = mc.MessageCard(mc.Header("▶️ 构建任务等待调度结束，正在运行", color="green"), mc.Config())
        div_1 = mc.Div(text=mc.Text(f"▶️ **流程链接**\n{detail_url}", tag_type=mc.TextType.lark_md))
        div_2 = mc.Div(text=mc.Text(f"▶️ **流程ID**\n{runner_id}", tag_type=mc.TextType.lark_md))
        cancel_button = trigger_button("取消该流程", command=f"cancel cicd {runner_id} | clear")
        check_button = trigger_button("查看任务进度", command=f"check cicd {runner_id}")
        message_card.add_element(div_1, div_2)
        message_card.add_element(cancel_button, check_button)
        return message_card

    def workflow_start_error(self):
        message_card = mc.MessageCard(mc.Header("📣 构建任务启动失败", color="red"), mc.Config())
        div_1 = mc.Div(text=mc.Text(f"可能是此流程等待前一个流程执行完毕时间过长或在调度阶段被canceled掉", tag_type=mc.TextType.lark_md))
        message_card.add_element(div_1)
        return message_card

    def watch_cicd(self, open_id, cicd_job_id, is_cloud=False, chat_type='p2p'):
        """watch GitHub cicd, return image tag and branch name"""
        owner = os.getenv("OWNER")
        repo = os.getenv("TRIGGER_REPO")
        github_obj = GithubApi(owner, repo)
        # wait cicd which in queue is scheduled
        runner_id, detail_url = github_obj.find_runner_id(cicd_job_id)
        Session().add_session(open_id, "runner_id", runner_id)  # 此行不能删！！！deploy命令会用到
        if self.magic_arg.get("quite"):
            app.logger.info("job exists, stop watch")
        elif runner_id is not None and detail_url is not None:
            message_card = self.make_workflow_scheduling_notes(runner_id, detail_url)
            send_text(self.message_type, open_id, message_card.to_dict(), chat_type)
        else:
            return "start_failed", False
        err_num = 0
        times = 270
        for t in range(times):
            try:
                ret, done = self._watch_cicd(runner_id, is_cloud=is_cloud)
            except Exception:
                app.logger.warn(traceback.format_exc())
                if err_num == 3:
                    break
                err_num += 1
                continue
            if done:
                return ret, done
            else:
                app.logger.info("waiting for completed, %s times / %s times" % (t + 1, times))
                time.sleep(20)  # 等待20s
                continue
        return None, None

    def _watch_env(self, env_name, namespace, tag):
        """
        环境更新监听
        :param env_name: 环境名称
        :param namespace: 命名空间
        :param tag: 标签
        :return:
        """
        for t in range(30):
            try:
                status, is_exists = K8sApi(namespace).deployment_status(env_name, tag)
                app.logger.info(
                    "[wait env] env_name: %s, status: %s, is_exists: %s, image_tags: %s, %s times/ 30 times" %
                    (env_name, status, is_exists, tag, t + 1)
                )
                if status and is_exists:
                    return True
                time.sleep(30)
            except Exception:
                app.logger.warn(traceback.format_exc())
                time.sleep(30)
                continue
        return False

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, True):
        command = args[0]
        namespace = self.magic_arg.get("namespace")
        status = False
        if command == "cicd":
            ret, done = self.watch_cicd(open_id, args[1], is_cloud=self.magic_arg["is_cloud"], chat_type=chat_type)
            if done and ret != "failure" and ret != "cancelled":
                # 企业版信息返回
                if not self.magic_arg["is_cloud"]:
                    package_image_tag, current_branch, opensource_branch, frontend_branch, tag = ret
                    self.output = package_image_tag
                    kwargs = {
                        "header": "🎉 构建任务执行成功",
                        "镜像tag": package_image_tag,
                        "企业版分支": current_branch,
                        "开源版分支": opensource_branch,
                        "前端分支": frontend_branch,
                    }
                    package_info = package_image_tag.split("/")[-1].replace(":", "-")
                    if self.magic_arg.get("is_package_tar"):
                        kwargs.update({
                            "压缩包下载链接":
                                f"http://139.198.127.226:30385/#/gz/tapdata-{package_info}.tar.gz"
                        })
                    if self.magic_arg.get("is_docker_save"):
                        kwargs.update({
                            "镜像压缩包下载链接":
                                f"http://139.198.127.226:30385/#/docker-gz/tapdata-docker-{package_info}.tar.gz"
                        })
                    message = common.field_list_message_card(**kwargs)
                # 云版信息返回
                else:
                    tm_java_tag, tapdata_agent_tag, agent_tag, console_tag, tcm_tag, cloud_tag = ret
                    now = datetime.now()
                    fields = {
                        "TM tag": None if tcm_tag is None else tm_java_tag.replace("registry.cn-beijing.aliyuncs.com/", "registry.cn-beijing.aliyuncs.com/data/"),
                        "Tapdata-agent tag": tcm_tag,
                        "agent tag": None if tcm_tag is None else agent_tag.replace("registry.cn-beijing.aliyuncs.com/", "registry.cn-beijing.aliyuncs.com/data/"),
                        "前端 tag": None if tcm_tag is None else console_tag.replace("registry.cn-beijing.aliyuncs.com/", "registry.cn-beijing.aliyuncs.com/data/"),
                        "TCM tag": None if tcm_tag is None else tcm_tag.replace("registry.cn-beijing.aliyuncs.com/", "registry.cn-beijing.aliyuncs.com/data/"),
                        "cdn地址": f"https://resource.tapdata.net/package/test/{now.strftime('%Y-%m-%d')}/dfs-{cloud_tag}/[指定的文件]",
                    }
                    for k, v in fields.items():
                        # 不打包tapdata-agent和agent
                        if k == "cdn地址" and agent_tag is None and tapdata_agent_tag is None:
                            fields[k] = "agent、tapdata-agent未打包"
                            continue
                        if v is None:
                            fields[k] = "未打包"
                    if tm_java_tag is None and tapdata_agent_tag is None and agent_tag is None and \
                            console_tag is None and tcm_tag is None and cloud_tag is None:
                        fields = {"注册数据源": "成功"}
                    self.output = tuple(ret[:1] + ret[2:])
                    message = common.field_list_message_card(
                        header="🎉 构建任务执行成功",
                        **fields,
                    )
                status = True
            elif done and ret == "cancelled":
                message = common.field_list_message_card(
                    header="📣 流程被cancelled掉了",
                    color="red",
                    is_short=False,
                    处理方式="自行查看Github流程执行情况"
                )
            elif done and ret == "failure":
                message = common.field_list_message_card(
                    header="😭 流程执行失败了",
                    color="red",
                    is_short=False,
                    处理方式="自行查看Github流程执行情况"
                )
            elif ret == "start_failed" and not done:
                return self.workflow_start_error().to_dict(), False
            else:
                message = common.field_list_message_card(
                    header="😭 流程执行超时",
                    color="red",
                    处理方式="自行查看Github执行情况并联系Jerry处理"
                )
            if not status:
                context = Context(command="clear", open_id=open_id, chat_type=chat_type)
                Schedule().register(context)
            return message.to_dict(), status
        elif command == "env_update" or command == "env_create":
            app.logger.info(
                f"[watch env_update] namespace is: {self.magic_arg.get('namespace')}, env_name is: {args[1]}")
            ret = self._watch_env(args[1], namespace, args[2])
            service_name = "dfs-gateway" if self.magic_arg.get("is_cloud") else args[1]
            node_port = K8sApi(namespace).svc(service_name)
            if ret and node_port:
                if self.magic_arg.get("is_cloud"):
                    env = self.magic_arg.get("env")
                    url_map = {
                        "cloud_dev": "https://dev.cloud.tapdata.net/console/",
                        "cloud_uat": "https://uat.cloud.tapdata.net/console/",
                        "cloud_test": "https://test3.cloud.tapdata.net/console/"
                    }
                    message = common.field_list_message_card(
                        header=f"🎉 {args[1]}部署完毕",
                        is_short=False,
                        访问地址=url_map.get(env)
                    )
                else:
                    message = common.field_list_message_card(
                        header=f"🎉 {args[1]}部署完毕",
                        is_short=False,
                        访问地址=f"http://139.198.127.204:{node_port}/"
                    )
                status = True
            elif ret and not node_port:
                message = common.field_list_message_card(
                    header=f"📣 {args[1]}部署完毕, 访问地址查看失败，联系下Jerry改Bug吧",
                    color="red",
                    is_short=False,
                    访问地址=f"啥也没有"
                )
            else:
                message = common.field_list_message_card(
                    header=f"😭 {args[1]}部署失败或启动超时被重启了, 联系下Jerry改Bug吧",
                    color="red",
                    is_short=False,
                    访问地址=f"啥也没有"
                )
            if not status:
                context = Context(command="clear", open_id=open_id, chat_type=chat_type)
                Schedule().register(context)
            return message.to_dict(), status
