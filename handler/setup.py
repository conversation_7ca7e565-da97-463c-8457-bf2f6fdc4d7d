"""
setup 命令

@author: 
@date: 2023.07.06
"""

import os
import time
import traceback

from utils.connector import redis_cli
from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, option
from utils.user_setup import UserSetup


@CommandParser.register
class Setup(Handler):
    command = "setup"
    check_template = [
        # 填写参数规则
    ]
    message_type = "text"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "setup",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "use_worker": "",  # worker名称
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        try:
            UserSetup.set_user_worker(open_id, self.magic_arg["use_worker"])
        except Exception as e:
            return f"设置失败，原因：{e}", False
        return "设置成功", True
