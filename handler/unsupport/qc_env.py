"""
qc_env 命令

@author: 
@date: 2022.12.08
"""

from utils.api import KubernetesCommand
from utils.schdule import CommandParser
from utils.handler import Handler
import requests


@CommandParser.register
class Qc_env(Handler):
    command = "可用的青云环境||可用的环境地址||环境地址||可用地址"
    check_template = [
        # 填写参数规则
    ]
    message_type = "text"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "qc_env",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        """获取GCP环境地址"""
        svc, _, _ = KubernetesCommand(cloud_type="gcp").get_service_name("dev")
        svc = svc.split("\n")[:-1]
        text = "当前GCP环境线上可用的地址如下:\n"
        for svc_name in svc:
            try:
                lb, _, _ = KubernetesCommand(cloud_type="gcp").get_lb_ip("dev", svc_name)
                port, _, _ = KubernetesCommand(cloud_type="gcp").get_svc_port("dev", svc_name, 3030)
                ok = True
                try:
                    res = requests.get("http://{}:{}".format(lb, port), timeout=3)
                    if res.status_code != 200:
                        ok = False
                except Exception as e:
                    ok = False
                if ok:
                    text = text + "这个环境可用, 环境名: " + svc_name + ", 访问地址: " + "http://" + str(lb) + ":" + str(port) + "\n"
                else:
                    text = text + "这个环境挂了, 环境名: " + svc_name + ", 访问地址: " + "http://" + str(lb) + ":" + str(port) + "\n"
            except Exception as e:
                continue
        return text, True
