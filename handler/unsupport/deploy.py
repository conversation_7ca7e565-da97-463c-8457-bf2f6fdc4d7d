"""
deploy 命令

@author: 
@date: 2022.43.31
"""
import json
import os
import re
import subprocess
import time
import traceback
import random

from lib.api import send_text
from utils.connector import redis_cli
from utils.handler import Handler
from utils import message_card as mc, common
from utils import exception
from utils import api
from utils.init import app
from utils.schdule import CommandParser
from utils.session import Session
from utils.shell import Shell


def random_str(length=5):
    return "".join(random.sample("zyxwvutsrqponmlkjihgfedcba0123456789", length))


@CommandParser.register
class Deploy(Handler):
    command = "deploy"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "deploy update/create",  # 命令示例
        "说明": "底层操作命令，用来实现环境更新和部署功能，可以使用'部署 [新环境]'命令实现部署更方便哦",
        "update": "更新环境如 deploy update [镜像tag] [环境名称]",
        "create": "创建新环境如 deploy create [镜像tag] [auto_update] [replica_count] [image_repo]",
        "auto_update": "自动更新",  # 填写选项和说明,
        "replica_count": "副本数",
        "image_repo": "镜像仓库"
    }

    def __init__(self):
        super().__init__()
        self.mode = "update"
        self.magic_arg = {
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False,
            "env": None
        }
        self.concurrency_limit = True

    def _parse_value_list(self, send_text: str) -> (dict, dict):
        env_tag = {}
        image_tags = {}
        flag = 0
        for text in send_text.split("\n"):
            if text.startswith("环境列表："):
                flag = 1
                continue
            elif text.startswith("按照时间顺序排序："):
                flag = 2
                continue
            if len(text) > 0:
                key, value = text.split(": ")
                if flag == 1:
                    env_tag.update({key: value})
                elif flag == 2:
                    image_tags.update({key: value})
            else:
                continue
        return env_tag, image_tags

    def update(self, *args):
        if len(args) != 4:
            raise exception.CommandException
        branch, tag, env_name, ns = args
        return api.K8sApi(ns).update_env(branch, tag, env_name, is_cloud=self.magic_arg.get("is_cloud"))

    def create(self, *args, ns="dev", name=f"{os.getenv('APP_NAME_PRE')}-{random_str(6)}"):
        if len(args) < 2:
            raise exception.CommandException
        branch, tag = args[:2]
        args = args[2:]
        ret = False
        # 如果自动更新，增加订阅
        if len(args) >= 1:
            auto_update = args[0]
            if auto_update:
                redis_cli.rpush(f"auto-update#{branch}", name)
        if len(args) == 0:
            ret = api.K8sApi(ns).create_env(branch, tag, name)
        elif len(args) == 1:
            ret = api.K8sApi(ns).create_env(branch, tag, name)
        elif len(args) == 2:
            ret = api.K8sApi(ns).create_env(branch, tag, name, replica_count=args[1])
        elif len(args) == 3:
            ret = api.K8sApi(ns).create_env(branch, tag, name, replica_count=args[1], image_repo=args[2])
        return ret

    def make_card(self, status, image_tag, env_name, namespace, color: str = "green"):
        if status == 0:
            status_text = "🎉 部署任务创建成功，部署成功后会通知你哦"
        elif status == 1:
            status_text = "📣 部署任务创建成功，部署监听任务创建失败，自行关注部署进度"
        else:
            status_text = "😭 部署任务创建失败，请联系Jerry处理"
        message_card = mc.MessageCard(mc.Header(status_text, color=color), mc.Config())
        fields = mc.Fields()
        image_tag_text = f"**▶️ 镜像tag**：\n{image_tag}"
        env_name_text = f"**▶️ ️环境名称**：\n{env_name}"
        fields.add_child(mc.Text(image_tag_text, mc.TextType.lark_md), is_short=True)
        fields.add_child(mc.Text(env_name_text, mc.TextType.lark_md), is_short=True)
        div = mc.Div(fields=fields)
        check_button = mc.trigger_button(
            "查看部署进度",
            command=f"check env_update {namespace} {env_name}",
        )
        print(f"check env_update {namespace} {env_name}")
        message_card.add_element(div, check_button)
        return message_card

    def _find_value_auto(self, open_id, image_num, env_num):
        """
        自动查询发送消息历史
        :param open_id: 用户openid
        :param cmd: 所查询的命令
        :param kwargs: 所要查找的特定值
        """
        ret = Session().find(open_id, "image_tag", "env", image_num, env_num)
        return common.parse_branch(ret, 2)

    def _get_select_menu_value(self, open_id):
        # 获取标签
        ret = Session().find(open_id, "image_tag", "env", "cache image_tag", "cache env")
        return common.parse_branch(ret, 2)

    def _get_select_menu_env(self, open_id, value):
        ret = Session().find(open_id, value, f"cache {value}")
        return common.parse_branch(ret, 1)

    def _make_error_branch(self):
        message_card = mc.MessageCard(mc.Header("获取分支失败", color="red"), mc.Config())
        div_1 = mc.Div(text=mc.Text(f"可能是个bug"))
        div_2 = mc.Div(text=mc.Text(f'找Jerry反馈改bug'))
        message_card.add_element(div_1, div_2)
        return message_card

    def _find_options(self, open_id):
        ret = Session().find(open_id, "auto_update", "replica_count", "cache auto_update", "cache replica_count")
        auto_update, replica_count = common.parse_branch(ret, 2)
        return (
            auto_update == "2",
            "1" if replica_count is None else str(replica_count),
        )

    def _parse_number(self, open_id, args: list):
        value_default = {
            "image_tag": None,
            "env": None
        }
        if len(args) == 1:
            args.append(None)
        for index, arg in enumerate(args[:len(value_default.keys())]):
            # 不是数字 - 直接填写
            if arg is not None and not re.match(r"^[0-9]+$", arg):
                value_default.update({
                    list(value_default.keys())[index]: arg
                })
            elif arg is None:
                ret = Session().find(
                    open_id, list(value_default.keys())[index], f"cache {list(value_default.keys())[index]}"
                )
                ret = common.parse_branch(ret, 1)
                if ret is None and self.magic_arg["env"]:
                    value_default.update({
                        list(value_default.keys())[index]: self.magic_arg["env"]
                    })
                elif ret is None:
                    return None
                else:
                    value_default.update({
                        list(value_default.keys())[index]: ret[0]
                    })
            # 是数字 - 快捷选择
            else:
                ret = Session().find(open_id, f"cache {list(value_default.keys())[index]}")
                if ret is None:
                    ret = self._get_select_menu_env(open_id, list(value_default.keys())[index])[0]
                    if ret is None:
                        return None
                value_default.update({
                    list(value_default.keys())[index]: ret
                })
        return value_default.values()

    def make_update_package_to_cdn_error(self, module):
        message_card = mc.MessageCard(mc.Header(f"更新{module}到cdn失败", color="red"), mc.Config())
        div_1 = mc.Div(text=mc.Text(f"请从Github Action页面下载制品包手动更新到cdn"))
        message_card.add_element(div_1)
        return message_card

    def make_updating_to_cdn(self, module):
        message_card = mc.MessageCard(mc.Header(f"正在更新 {module} 到cdn", color="green"), mc.Config())
        div_1 = mc.Div(text=mc.Text(f"稍等片刻"))
        message_card.add_element(div_1)
        return message_card

    def make_updated_to_cdn(self, module, success=True):
        if success:
            status = "成功"
            color = "green"
        else:
            status = "失败"
            color = "red"
        message_card = mc.MessageCard(mc.Header(f"更新 {module} 到cdn{status}", color=color), mc.Config())
        div_1 = mc.Div(text=mc.Text(f"暂未设置访问链接"))
        message_card.add_element(div_1)
        return message_card

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        command = args[0]
        self.mode = command
        namespace = self.magic_arg.get("namespace")
        app.logger.info(f"mode is: {self.mode}, namespace is {namespace}")
        # 如果是可视化表单触发
        if len(args[1:]) == 0 or self.magic_arg.get("is_cloud"):
            if self.magic_arg.get("is_cloud"):
                # 获取env值，并自动匹配镜像
                env, image_tags = args[1], eval(" ".join(args[2:]))
                if Session().find(open_id, "cache is_build_tm_java") != "1" and env == "dfs-tm-java":
                    if image_tags[0] is None:
                        return None, False
                    image_tag = image_tags[0]
                elif Session().find(open_id, "cache is_build_frontend") != "1" and env == "dfs-console":
                    if image_tags[2] is None:
                        return None, False
                    image_tag = image_tags[2]
                elif Session().find(open_id, "cache is_tapdata_agent") != "1" and env == "dfs-tapdata-agent":
                    return self.make_updated_to_cdn("tapdata-agent").to_dict(), True
                elif Session().find(open_id, "cache is_build_tcm") != "1" and env == "dfs-tcm":
                    if image_tags[3] is None:
                        return None, False
                    image_tag = image_tags[3]
                else:
                    return None, False
            else:
                tmp = self._get_select_menu_value(open_id)
                if tmp is None:
                    return self._make_error_branch().to_dict(), False
                image_tag, env = tmp
            image_tag.replace(f'{os.getenv("IMAGE_REPO")}/', "")
        # 如果是底层命令触发
        else:
            tmp = self._parse_number(open_id, list(args)[1:])
            if tmp is None:
                return self._make_error_branch().to_dict(), False
            image_tag, env = tmp
        app.logger.info("镜像tag：%s, 环境名称：%s" % (image_tag, env))
        branch, tag = image_tag.split(":")
        if command == "update":
            try:
                ret = self.update(branch, tag, env, namespace)
                self.output = f"{ret} {branch}:{tag}"
                watch = True if ret is not None else False
            except Exception:
                app.logger.error(traceback.format_exc())
                watch = False
                ret = False
        elif command == "create":
            auto_update, replica_count = self._find_options(open_id)
            app.logger.info("自动更新: %s, 副本数: %s" % (auto_update, replica_count))
            try:
                ret = self.create(
                    branch, tag, auto_update, replica_count, ns=namespace, name=f"{branch}-{random_str(6)}"
                )
                app.logger.info("创建的环境: %s" % ret)
                env = ret
                watch = True if ret is not None else False
            except Exception:
                app.logger.error(traceback.format_exc())
                watch = False
        app.logger.info(f"env: {env}, watch: {watch}")
        if ret and watch:
            message_card = self.make_card(0, f"{branch}:{tag}", ret, namespace)
            self.output = f"{ret} {branch}:{tag}"
            status = True
            return message_card.to_dict(), status
        elif ret and not watch:
            message_card = self.make_card(1, f"{branch}:{tag}", ret, namespace, color="red")
            status = False
            return message_card.to_dict(), status
        elif ret is None or not ret and not watch:
            message_card = self.make_card(2, f"{branch}:{tag}", env, namespace, color="red")
            status = False
            return message_card.to_dict(), status
