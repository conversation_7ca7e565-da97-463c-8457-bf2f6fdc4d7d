"""
watchK8sUpdate 命令

@author: 
@date: 2023.04.13
"""

import os
import time
import traceback

from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, common
from utils.shell import Shell


@CommandParser.register
class Watchk8supdate(Handler):
    command = "watchK8sUpdate"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "watchK8sUpdate",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        """
            1. 获取脚本路径
            2. 执行脚本
            3. 判断脚本返回值
        """
        profile, component = eval(''.join(args))
        profile = profile.replace("hw_", "gray")
        # 获取脚本路径
        watch_script_path = os.path.abspath('.') + "/scripts/watch.sh"
        # 执行脚本
        ret_code = Shell.run(command=f"bash {watch_script_path} -d {component} -n {profile}")
        print(f"bash {watch_script_path} -d {component} -n {profile}")
        # 判断脚本返回值
        if ret_code == 0:
            message = common.field_list_message_card(
                header=F"更新{component}成功",
                color="green",
                is_short=False,
                访问地址="cloud.tapdata.io"
            )
            return message.to_dict(), True
        else:
            message = common.field_list_message_card(
                header=f"更新{component}失败",
                color="red",
                is_short=False,
                处理方式="联系Jerry查看具体原因"
            )
            return message.to_dict(), False
