"""
sync_image_repo 命令

@author: 
@date: 2023.04.28
"""

import os
import time
import traceback
from collections.abc import Iterable

from utils.option import Option
from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, option, common
from utils.shell import Shell
from lib.api import send_text


@CommandParser.register
class Sync_image_repo(Handler):
    command = "sync_image_repo"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "sync_image_repo project repo packagename ",  # 命令示例
        "选项": "用来同步镜像",  # 填写选项和说明
    }

    _image_repo_map = {
        "qingcloud": {
            "registry": "dockerhub.qingcloud.com",
            "component": {
                "dfs-tcm": "tapdata",
                "dfs-tm-java": "tapdata",
                "dfs-console": "tapdata",
                "dfs-gateway": "tapdata",
                "dfs-flow-engine": "tapdata",
            },
            "login": "docker login -u Jerry -p Gotapd8! dockerhub.qingcloud.com",
        },  # 青云
        "aliCloud-Beijing": {
            "registry": "registry.cn-beijing.aliyuncs.com",
            "component": {
                "dfs-tcm": "tapdata",
                "dfs-tm-java": "tapdata",
                "dfs-console": "tapdata",
                "dfs-gateway": "tapdata",
                "dfs-flow-engine": "tapdata",
            },
            "login": "docker login --username=sysadmin@1809821306098986 registry.cn-beijing.aliyuncs.com -p Gotapd8!",
        },  # 阿里云 北京
        "aliCloud-HongKong": {
            "registry": "registry.cn-hongkong.aliyuncs.com",
            "component": {
                "dfs-tcm": "tapdata",
                "dfs-tm-java": "tapdata",
                "dfs-console": "tapdata",
                "dfs-gateway": "tapdata",
                "dfs-flow-engine": "tapdata",
            },
            "login": "docker login --username=sysadmin@1809821306098986 registry.cn-hongkong.aliyuncs.com -p Gotapd8!",
        },  # 阿里云 香港
        "huaweiCloud": {
            "registry": "swr.ap-southeast-1.myhuaweicloud.com",
            "component": {
                "dfs-flow-engine": "tapdata",
                "dfs-gateway": "tapdata",
                "dfs-tm-java": "tapdata",
                "dfs-tcm": "tapdata",
                "dfs-console": "tapdata",
            },
            "login": "docker login -u ap-southeast-1@5IWVYC6OA687WXVJDPQD -p 9de4acd4311d0ce31976549325c0ec1ca4282b05c52ca6465b6b0ed5bc9a65fb swr.ap-southeast-1.myhuaweicloud.com",
        },  # 华为云 香港
        "coding": {
            "registry": "tapdata-docker.pkg.coding.net",
            "component": {
                "dfs-flow-engine": "dfs/flow-engine",
                "dfs-tm-java": "tapdata/tapdata",
                "dfs-tcm": "tapdata/tapdata",
                "dfs-console": "tapdata/tapdata",
                "dfs-gateway": "tapdata/tapdata",
            },
            "login": "docker login -u tapdata-1668675309097 -p 7548670226e4ab592246dd0fd11ae96b3a6104b1 tapdata-docker.pkg.coding.net"
        },  # Coding
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "source_cloud": "coding",
            "target_cloud": "qingcloud",
            "component": "",
        }
        self.concurrency_limit = True

    def _docker_login(self, *clouds):
        commands = []
        for cloud in clouds:
            commands.append(self._image_repo_map[cloud]["login"])
        return "\n".join(commands)

    def _image(self, cloud_type, component, version):
        source_cloud_info = self._image_repo_map[cloud_type]
        return f"{source_cloud_info['registry']}/{source_cloud_info['component'][component]}/{component}:{version}"

    def _check_cloud_type(self, cloud_type):
        if not isinstance(cloud_type, str):
            raise Exception("镜像仓库名字必须为 str 类型")
        if cloud_type not in self._image_repo_map:
            raise Exception("不支持这个仓库")
        return cloud_type

    def _check_component_name(self, component):
        if not isinstance(component, str):
            raise Exception("组件名称必须为 str 类型")
        if component not in self._image_repo_map["qingcloud"]["component"]:
            raise Exception("不支持该组件")
        return component

    def _exec(self, command, step_name, next_step_name=None, quite=False):
        out, err, ret_code = Shell.execute(command)
        out = out if out is not None else ""
        err = err if err is not None else ""
        if ret_code != 0:
            message = common.field_list_message_card(
                header=step_name,
                color="red",
                is_short=False,
                结果="失败",
                标准输出=out,
                标准错误=err,
            )
            send_text(self.message_type, self.open_id, message.to_dict(), self.chat_type)
            return False
        else:
            if quite:
                return True
            kwargs = {
                "header": step_name,
                "color": "green",
                "is_short": False,
                "结果": "成功",
            }
            print(next_step_name)
            if next_step_name is not None:
                kwargs["下一步"] = next_step_name
            message = common.field_list_message_card(**kwargs)
            send_text(self.message_type, self.open_id, message.to_dict(), self.chat_type)
            return True

    def _run_commands(self, commands: [str], quite=False):
        for i, command in enumerate(commands):
            cmd, step_name = command
            next_step_name = None
            if i < len(commands) - 1:
                next_step_name = commands[i + 1][1]
                print(next_step_name)
            if not self._exec(cmd, step_name, next_step_name, quite):
                return False
        return True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        """
        1. 从缓存获取镜像版本
        2. 获取源仓库,目标仓库和镜像名称
        3. 执行脚本：
            (1) 登录源仓库和目标仓库
            (2) 拉取源仓库镜像到本地
            (3) 将源仓库镜像重新打tag到目标仓库
            (4) 推送镜像到目标仓库
            (5) 删除本地镜像
            (6) 检查目标仓库的镜像是否存在
        4. 发送镜像同步消息
        """
        message_card = common.field_list_message_card(
            header="镜像同步",
            color="green",
            is_short=False,
            任务ID=self.job_id,
            信息="正在同步镜像",
        )
        send_text(self.message_type, self.open_id, message_card.to_dict(), self.chat_type)
        # 1. 从缓存获取镜像版本
        image_version = option.ImageVersion(self.job_id, self.open_id).get_option()
        # 2. 获取源仓库,目标仓库和镜像名称
        source_cloud = option.SourceCloudType(self.job_id, self.open_id).get_option()
        target_cloud = option.TargetCloudType(self.job_id, self.open_id).get_option()
        component = option.Components(self.job_id, self.open_id).get_option()
        # 3. 执行脚本
        # 如果为target_cloud为all,则同步到所有云商
        quite = False
        if isinstance(target_cloud, Iterable):
            quite = True
            commands = [
                [self._docker_login(source_cloud, *target_cloud), "登录源库和目标仓库"],  # (1) 登录源仓库和目标仓库
                [f"docker pull {self._image(source_cloud, component, image_version)}", "拉取源仓库镜像"],  # (2) 拉取源仓库镜像到本地
            ]
            for cloud in target_cloud:
                commands += [
                    [
                        f"docker tag {self._image(source_cloud, component, image_version)} {self._image(cloud, component, image_version)}",
                        "将源库镜像tag到目标仓库"],  # (3) 将源仓库镜像重新打tag到目标仓库
                    [f"docker push {self._image(cloud, component, image_version)}", "推送镜像到目标仓库"],
                    # (4) 推送镜像到目标仓库
                    [f"docker rmi {self._image(cloud, component, image_version)}", "删除本地镜像"],  # (5) 删除本地镜像
                    [f"docker manifest inspect {self._image(cloud, component, image_version)}", "检查目标仓库的镜像是否存在"],
                    # (6) 检查目标仓库的镜像是否存在
                ]
        else:
            commands = [
                [self._docker_login(source_cloud, target_cloud), "登录源库和目标仓库"],  # (1) 登录源仓库和目标仓库
                [f"docker pull {self._image(source_cloud, component, image_version)}", "拉取源仓库镜像"],  # (2) 拉取源仓库镜像到本地
                [
                    f"docker tag {self._image(source_cloud, component, image_version)} {self._image(target_cloud, component, image_version)}",
                    "将源库镜像tag到目标仓库"],  # (3) 将源仓库镜像重新打tag到目标仓库
                [f"docker push {self._image(target_cloud, component, image_version)}", "推送镜像到目标仓库"],  # (4) 推送镜像到目标仓库
                [f"docker rmi {self._image(target_cloud, component, image_version)}", "删除本地镜像"],  # (5) 删除本地镜像
                [f"docker manifest inspect {self._image(target_cloud, component, image_version)}", "检查目标仓库的镜像是否存在"],
                # (6) 检查目标仓库的镜像是否存在
            ]
        # 4. 发送镜像同步消息
        if self._run_commands(commands, quite):
            if isinstance(target_cloud, Iterable):
                target_images = []
                for cloud in target_cloud:
                    target_images.append(self._image(cloud, component, image_version))
                target_image_text = "\n".join(target_images)
            else:
                target_image_text = self._image(target_cloud, component, image_version)
            message = common.field_list_message_card(
                header="同步镜像成功",
                color="green",
                is_short=False,
                源镜像=f"{self._image(source_cloud, component, image_version)}",
                目标镜像=target_image_text,
            )
            return message.to_dict(), True
        else:
            message = common.field_list_message_card(
                header="同步镜像失败",
                color="red",
                is_short=False,
                源镜像=f"{self._image(source_cloud, component, image_version)}",
            )
            return message.to_dict(), False



