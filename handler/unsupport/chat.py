"""
chat 命令

@author: 
@date: 2022.12.08
"""
import requests

from utils.schdule import CommandParser
from utils.handler import Handler

@CommandParser.register
class Chat(Handler):
    command = "chat"
    check_template = [
        # 填写参数规则
    ]
    message_type = "text"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "chat",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        command = kwargs.get("command")
        resp = requests.post("http://45.120.216.132:5002/query", headers={"token": "Gotapd8!"}, json={"query": command},
                             timeout=1200)
        message = resp.json()["answer"]
        return message, True