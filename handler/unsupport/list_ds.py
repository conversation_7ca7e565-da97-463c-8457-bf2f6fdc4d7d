"""
list_ds 命令

@author: 
@date: 2022.12.09
"""

import os
import time
import traceback

from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc
from utils.common import field_list_message_card


@CommandParser.register
class List_ds(Handler):
    command = "数据源列表||青云环境数据源列表||列出常见的数据源列表"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "list_ds",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        message = field_list_message_card(
            header="当前青云环境可用的数据源列表如下:",
            color="blue",
            数据源列表地址="https://tapdata.feishu.cn/sheets/shtcnm1fBh6ZA83tB2tEQUprB2c"
        )
        return message.to_dict(), True
