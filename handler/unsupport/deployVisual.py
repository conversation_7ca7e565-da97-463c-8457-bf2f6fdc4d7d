"""
deployVisual 命令

@author: 
@date: 2022.41.31
"""
import json

from utils.handler import Hand<PERSON>
from utils import message_card as mc, common
from utils.common import make_text, get_all_images, get_all_env
from utils.message_card import trigger_button
from utils.schdule import CommandParser


@CommandParser.register
class Deployvisual(Handler):
    command = "部署"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "部署",  # 命令示例
        "说明": "创建应用、service、deployment",  # 填写选项和说明
    }
    except_cache = True

    def __init__(self):
        super().__init__()
        self.image_tags = []
        self.envs = []

    def default_handler(self, mode="update") -> mc.MessageCard:
        image_tags = get_all_images()
        self.image_tags = image_tags
        envs = get_all_env()
        self.envs = envs
        if mode == "update":
            header = mc.Header("▶️ 青云-更新环境")
        else:
            header = mc.Header("▶️ 青云-创建新环境")
        config = mc.Config()
        image_tag = mc.make_select(image_tags, "image_tag")
        env = mc.make_select(envs, "env")
        image_tag_div = mc.Div(mc.Text("镜像tag(必选)"), extra=image_tag)
        if mode == "update":
            env_div = mc.Div(mc.Text("环境(必选)"), extra=env)
        if mode == "create":
            auto_update = mc.make_select(["否", "是"], "auto_update", placeholder=mc.Text("否"))
            replica_count = mc.make_select([str(i+1) for i in range(10)], "replica_count", placeholder=mc.Text("1"))
            auto_update_div = mc.Div(mc.Text("自动更新"), extra=auto_update)
            replica_count_div = mc.Div(mc.Text("副本数"), extra=replica_count)
        message_card = mc.MessageCard(header, config)
        # add elements
        if mode == "update":
            message_card.add_element(image_tag_div, env_div)
        elif mode == "create":
            message_card.add_element(image_tag_div, auto_update_div, replica_count_div)
        return message_card

    def get_cache(self, *args, **kwargs):
        ret = {
            "env": common.make_cache(self.envs),
            "image_tag": common.make_cache(self.image_tags)
        }
        return ret

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        trigger_div = trigger_button("开始部署", command="deploy create | watch env_create | clear")
        message_card = self.default_handler(mode="create")
        message_card.add_element(trigger_div)
        return message_card.to_dict(), True
