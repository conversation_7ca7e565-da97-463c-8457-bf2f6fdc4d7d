"""
updateVisual 命令

@author: 
@date: 2022.49.02
"""
import os

from utils.common import get_all_images, get_all_env
from utils.handler import Handler
from utils import message_card as mc, common
from utils.init import app
from utils.message_card import trigger_button
from utils.schdule import CommandParser
from utils import exception


@CommandParser.register
class Updatevisual(Handler):
    command = "更新"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "更新 [云版] [研发环境/测试环境]",  # 命令示例
        "说明": "更新指定的Deployment",  # 填写选项和说明
        "云版": "更新 云版",
        "研发环境/测试环境": "选择环境，默认是研发环境",
    }

    except_cache = True

    def __init__(self):
        super().__init__()
        self.image_tags = []
        self.envs = []
        self.magic_arg = {
            "namespace": "dev"
        }
        self.is_cloud = False

    def make_card(self, header, namespace="dev"):
        envs = get_all_env(namespace)
        app.logger.info(f"当前命名空间 %s 的环境数量: %s" % (namespace, len(envs)))
        if len(envs) == 0:
            raise exception.EnvLengthIsEmpty
        self.envs = envs
        if self.is_cloud:
            image_tags = common.get_dfs_images()
        else:
            image_tags = get_all_images()
        self.image_tags = image_tags
        header = mc.Header(f"▶️ {header}")
        config = mc.Config()
        image_tag = mc.make_select(image_tags, "image_tag")
        env = mc.make_select(envs, "env")
        image_tag_div = mc.Div(mc.Text("镜像tag(必选)"), extra=image_tag)
        env_div = mc.Div(mc.Text("环境(必选)"), extra=env)
        message_card = mc.MessageCard(header, config)
        message_card.add_element(image_tag_div, env_div)
        return message_card

    def get_cache(self, *args, **kwargs):
        ret = {
            "env": common.make_cache(self.envs),
            "image_tag": common.make_cache(self.image_tags),
            "namespace": self.namespace,
        }
        return ret

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        if len(args) > 0 and args[0] == "云版":
            self.is_cloud = True
        if len(args) > 0 and args[0] == "云版":
            trigger_div = trigger_button(
                "开始更新",
                command="""
                deploy update is_cloud=True namespace=cloud-dev | watch env_update is_cloud=True namespace=cloud-dev
                """
            )
            if len(args) == 2 and (args[1] == "测试环境" or args[1] == "测试"):
                self.namespace = eval(os.getenv("NAMESPACE_CLOUD")).get("test")
                message_card = self.make_card("青云-更新云版-测试环境", namespace=self.namespace)
            elif len(args) == 2 and (args[1] == "生产环境" or args[1] == "生产"):
                self.namespace = eval(os.getenv("NAMESPACE_CLOUD")).get("prod")
                message_card = self.make_card("青云-更新云版-生产环境", namespace=self.namespace)
            else:
                trigger_div = trigger_button(
                    "开始更新",
                    command="""
                    deploy update is_cloud=True namespace=cloud-dev | watch env_update namespace=cloud-dev
                    """
                )
                self.namespace = eval(os.getenv("NAMESPACE_CLOUD")).get("dev")
                message_card = self.make_card("青云-更新云版-研发环境", namespace=self.namespace)
            message_card.add_element(trigger_div)
            return message_card.to_dict(), True
        else:
            trigger_div = trigger_button("开始更新", command="deploy update | watch env_update | clear")
            self.namespace = "dev"
            message_card = self.make_card("青云-更新环境", "dev")
            message_card.add_element(trigger_div)
            return message_card.to_dict(), True
