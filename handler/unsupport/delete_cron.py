"""
delete_cron 命令

@author: 
@date: 2023.01.17
"""
import json
import os
import time
import traceback

from utils.schdule import CommandParser, Schedule
from utils.handler import Handler, Letter
from utils import message_card as mc


@CommandParser.register
class Delete_cron(Handler):
    command = "delete_cron"
    check_template = [
        # 填写参数规则
    ]
    message_type = "text"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "delete_cron",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        # 任务ID
        job_id = args[0]
        for cronjob in Schedule().cron_th:
            # context 类
            context = json.loads(cronjob.context)
            # 找到对应的job_id
            print(context["job_id"], job_id)
            if context["job_id"] != job_id:
                continue
            # 停止定时任务
            cronjob.stop()
            Schedule().del_cron_th(cronjob)
            return "停止定时任务成功", True
        return "找不到定时任务", False
