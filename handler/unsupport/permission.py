"""
audit 命令

@author: 
@date: 2022.12.08
"""
import base64
import json
import os
import uuid
from datetime import datetime
from importlib import import_module

from lib.api import send_text
from utils.schdule import CommandParser, Schedule, Context
from utils.handler import Handler, Letter
from model.permission import Permission as Limit
from utils import exception, datastruct
from utils.init import db
from utils import message_card as mc
from utils.init import app


@CommandParser.register
class Permission(Handler):
    command = "permission"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "permission id [pass/reject] | ...",  # 命令示例
        "说明": "权限审核",  # 填写选项和说明
        "id": "权限id",
        "pass/reject": "通过/拒绝",
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "job_id": None,
            "applicant": None,
            "reviewer": '',
            "command": None,
            "chat_type": None,
            "permission_item": None,
        }
        self.concurrency_limit = True

    def compare_permissions(self, command: str, user_name):
        files = os.listdir(os.path.dirname(__file__))
        files.remove("__init__.py")
        files.remove("__pycache__")
        for file in files:
            module = file.replace(".py", "")
            if command.lower() == module.lower():
                try:
                    pkg = import_module(f"handler.{module}")
                except ModuleNotFoundError:
                    continue
                if user_name == getattr(getattr(pkg, f"{module.capitalize()}")(), "permissions"):
                    return True
        return False

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        # 格式一：permission id pass/reject, 在数据库中已经保存
        if len(args) == 2:
            limit_id, action = args
            limit = Limit.query.filter_by(id=limit_id).first()
            # 检查申请是否存在
            if limit is None:
                raise exception.LimitNotFound
            # 检查申请记录是否正常
            if limit.status != 0:
                raise exception.LimitError
            # 检查权限
            command = base64.urlsafe_b64decode(limit.command.encode("utf8")).decode("utf8")
            github_name, feishu_uid, feishu_name = datastruct.FeishuUserInfo.get_user_info(kwargs["user_id"])
            if github_name not in limit.reviewer and feishu_name not in limit.reviewer and feishu_uid not in limit.reviewer:
                raise Exception("不允许这个操作")
            # 修改申请记录状态
            if action == "pass":
                limit.status = 1
            elif action == "reject":
                limit.status = 2
            else:
                raise exception.InternalCommandSyncTaxError
            # 设置审核时间
            limit.end_at = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            # 设置审核者
            limit.reviewer = self.magic_arg["reviewer"]
            # 提交数据库修改
            db.session.commit()
            # 注册命令
            command = base64.urlsafe_b64decode(command).decode("utf8")
            context = Context(command=command, open_id=limit.applicant,
                              chat_type=limit.chat_type)
            Schedule().register(context)
            return mc.limit_permitted(limit.reviewer, action).to_dict(), True
        # 格式二：permission job_id=xxx applicant=xxx ... 保存到数据库
        else:
            try:
                permission_id = str(uuid.uuid4())
                limit = Limit(
                    id=permission_id,
                    job_id=self.magic_arg["job_id"],
                    applicant=self.magic_arg["applicant"],
                    command=self.magic_arg["command"],
                    create_at=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    end_at="",
                    status=0,
                    chat_type=self.magic_arg["chat_type"],
                    reviewer=self.magic_arg["reviewer"],
                )
            except KeyError:
                raise exception.InternalCommandSyncTaxError
            db.session.add(limit)
            db.session.commit()
            _, _, user_name = datastruct.FeishuUserInfo.get_user_info(kwargs["user_id"])
            app.logger.info(f"permission magic is: {self.magic_arg}")
            # 如果审核者为空则报错
            if not len(self.magic_arg["reviewer"]):
                raise exception.ReviewersIsNone
            # 发送申请通知
            for r in self.magic_arg["reviewer"].split(","):
                _, user_id, _ = datastruct.FeishuUserInfo.get_user_info(r)
                ret = mc.permitted_request(user_name, self.magic_arg["permission_item"], permission_id)
                app.logger.info(f"准备发送审核消息给: {user_id}")
                send_text("message_card", user_id, ret.to_dict(), "p2p")
            return mc.apply_success().to_dict(), True

