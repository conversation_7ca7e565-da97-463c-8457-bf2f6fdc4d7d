"""
register_connector 命令

@author: 
@date: 2023.05.18
"""
import json
import os
import time
import traceback

from utils.api import GithubApi
from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, option, common, api


@CommandParser.register
class Register_connector(Handler):
    command = "register_connector"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "register_connector",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "env": "cloud-dev",
        }
        self.concurrency_limit = True

    def _make_register_connector_args(self, connector):
        env = self.magic_arg["env"]
        cloud_env_map = eval(os.getenv("cloud_env_map"))
        print(json.dumps(cloud_env_map, indent=4))
        cloud_register_info = cloud_env_map[env]
        return f"{cloud_register_info['url']}#{cloud_register_info['a']}#{cloud_register_info['ak']}#{cloud_register_info['sk']}#{connector}"

    def get_all_connectors(self, opensource_branch, enterprise_branch):
        opensource_connector = api.GithubApi(os.getenv("OWNER"), os.getenv("REPO")).get_connectors(
            "connectors",
            "tapdata",
            except_path=['build', 'pom.xml', 'pom-ent.xml', 'config.xml', 'base-connector.properties'],
            ref=opensource_branch
        )
        enterprise_connector = api.GithubApi(os.getenv("OWNER"), os.getenv("REPO")).get_connectors(
            "connectors",
            "tapdata-enterprise",
            except_path=['build', 'pom.xml', 'pom-ent.xml', 'config.xml', 'base-connector.properties'],
            ref=enterprise_branch
        )
        return list(set(opensource_connector + enterprise_connector))

    def get_saas_connector(self, branch):
        opensource_connector_saas = api.GithubApi(os.getenv("OWNER"), os.getenv("REPO")).get_connectors(
            "connectors-javascript",
            "tapdata",
            except_path=['build', 'pom.xml', 'pom-ent.xml', 'config.xml', 'js-core'],
            ref=branch
        )
        return [f"saas/{c}" for c in opensource_connector_saas]

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        """
        1. 获取开源/闭源分支，连接器
        2. 调取api触发流程
        """
        # 1. 获取开源/闭源分支，连接器
        opensource_branch = option.OpenSourceBranch(self.job_id, self.open_id).get_option()
        enterprise_branch = option.EnterpriseBranch(self.job_id, self.open_id).get_option()
        connector = option.Connectors(self.job_id, self.open_id).get_option()
        if not connector:
            connector = ",".join(self.get_all_connectors(opensource_branch, enterprise_branch))
            saas_connector = ",".join(self.get_saas_connector(opensource_branch))
            connector = f"{connector},{saas_connector}"
        # 2. 调取api触发流程
        branches = f"release-v3.2.2-dfs%{enterprise_branch}%{opensource_branch}%release-v3.2.2-dfs"
        client_payload = {
            "register_connector_args": self._make_register_connector_args(connector)
        }
        owner = os.getenv("OWNER")
        repo = os.getenv("TRIGGER_REPO")
        github_obj = GithubApi(owner, repo)
        res = github_obj.trigger(
            branches,
            self.job_id,
            enterprise_or_cloud="cloud",
            **client_payload
        )
        if res:
            header = "🎉 注册任务创建成功，执行成功后会通知你哦"
            color = "green"
        else:
            header = "😭 构建任务创建失败，请联系Jerry处理"
            color = "red"
        message_card = common.field_list_message_card(
            header=header,
            color=color,
            企业版分支=enterprise_branch,
            开源版分支=opensource_branch,
            注册的数据源=connector,
        )
        return message_card.to_dict(), True

