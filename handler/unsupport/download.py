"""
chat 命令

@author: 
@date: 2022.12.08
"""
import datetime
import os
import time
import traceback

import requests
from kubernetes import client, config

from utils.schdule import CommandParser
from utils.handler import Handler
import pymongo
from fuzzywuzzy import fuzz
from utils.common import utc2local, time_readable

@CommandParser.register
class Download(Handler):
    command = "下载站||下载包地址||下载包||发布包下载||安装包下载||安装包地址"
    check_template = [
        # 填写参数规则
    ]
    message_type = "text"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "download",  # 命令示例
    }

    def __init__(self):
        super().__init__()
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        text = "请访问 http://139.198.127.226:8000/ 获取安装包地址, 用户名为: tapdata, 密码为: Gotapd8!, 本地下载请在登录后直接点击下载, 客户环境下载请通过分享链接下载, 为安全起见, 请避免在客户环境登录下载站"
        return text, True
