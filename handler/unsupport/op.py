"""
chat 命令

@author: 
@date: 2022.12.08
"""
import datetime
import os
import time
import traceback

import requests
from kubernetes import client, config

from utils.schdule import CommandParser
from utils.handler import Handler
import pymongo
from fuzzywuzzy import fuzz
from utils.common import utc2local, time_readable

@CommandParser.register
class Op(Handler):
    command = "内部服务情况||内部服务健康状况"
    check_template = [
        # 填写参数规则
    ]
    message_type = "text"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "op",  # 命令示例
    }

    def __init__(self):
        super().__init__()
        self.concurrency_limit = True
        api_token = "**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
        configuration = client.Configuration()
        setattr(configuration, 'verify_ssl', False)
        client.Configuration.set_default(configuration)
        configuration.host = "https://***************:6443"  # ApiHost
        configuration.verify_ssl = False
        configuration.debug = True
        configuration.api_key = {"authorization": "Bearer " + api_token}
        client.Configuration.set_default(configuration)
        self.qc_k8s_client = client

    def get_nodes_ready_time(self):
        v1core_api = self.qc_k8s_client.CoreV1Api()
        nodes = v1core_api.list_node().items
        nodes_ready_time = []
        for node in nodes:
            conditions = node.status.conditions
            x = {
                "node": node.metadata.name,
                "ready": False,
                "time": datetime.datetime.now()
            }
            for condition in conditions:
                if condition.type == "Ready":
                    t = condition.last_transition_time.replace(tzinfo=None)
                    x["time"] = t
                    if condition.status == "True":
                        x["ready"] = True
                    break
            nodes_ready_time.append(x)
        return nodes_ready_time

    def get_node_ready_text(self, tab=1):
        text = ""
        nodes_ready_time = self.get_nodes_ready_time()
        ready_nodes = 0
        for n in nodes_ready_time:
            if n["ready"]:
                ready_nodes += 1
        text += "  "*tab + "青云节点健康状况汇总, 共 {} 个节点, {} 正常:\n".format(len(nodes_ready_time), ready_nodes)
        for i in range(len(nodes_ready_time)):
            n = nodes_ready_time[i]
            text += "{}{}. {}, 状态: {}, 持续时间: {}\n".format("  "*(tab+1), i+1, n["node"], "正常" if n["ready"] else "异常", time_readable((datetime.datetime.now() - n["time"]).total_seconds()-8*3600))
        return text

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        now = utc2local(datetime.datetime.now())
        text = "现在时间是: {}, 内部服务健康状况如下:\n".format(now)
        text += self.get_node_ready_text(1)
        return text, True
