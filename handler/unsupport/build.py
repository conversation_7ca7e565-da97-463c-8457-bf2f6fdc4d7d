"""
build 命令

@author: <PERSON>
@date: 2022.08.16
"""
from __future__ import annotations

import json
import os
import re
import uuid

from utils.handler import Handler
from lib.api import send_text
from utils.init import app
from utils.message_card import trigger_button
from utils.schdule import CommandParser
from utils.session import Session
from utils.api import GithubApi
from utils import message_card as mc, common


owner = os.getenv("OWNER")
repo = os.getenv("TRIGGER_REPO")


@CommandParser.register
class Build(Handler):

    command = "build"
    check_template = []
    message_type = "message_card"
    help = {
        "format": "build [企业版分支] [开源分支] [前端分支] [云版分支] [构建完整版] [运行集成测试] [推送到coding] [压缩包] [镜像压缩包] [前端mode] "
                  "is_cloud=False",
        "说明": "底层操作命令，触发构建可以使用'打包'命令唤出可视化面板 0 -> 关闭, 1 -> 开启，可选默认为1",
        "构建完整版": "构建connector和tapdata-cli",
        "运行集成测试": "运行集成测试",
        "企业版分支(必选)": "分支名称如develop-v2.8或者分支标记如 'a'",
        "开源分支(必选)": "分支名称如develop-v2.8或者分支标记如 'a'",
        "前端分支(必选)": "分支名称如develop-v2.8或者分支标记如 'a'",
        "推送到coding(可选)": "0 -> 关闭，1 -> 开启, 可选",
        "压缩包(可选)": "0 -> 关闭，1 -> 开启，可选",
        "镜像压缩包(可选)": "0 -> 关闭，1 -> 开启，可选",
        "前端mode": "1为production, 2为msa",
        "is_cloud": "为True则表示云版，为False表示企业版",
        "例如": "build develop-v2.8 develop-v2.8 develop-v2.8",
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {
            "id": "",
            "is_cloud": False,
            "quite": False,
            "cloud_env": "cloud-dev",
            "permission": False,
            "permission_item": "",
            "image_repo": None,
        }
        self.permissions = ["openlg", "xbsura"]
        self.concurrency_limit = True

    def has_permission(self, kwargs):
        """
        飞书生产环境权限控制
        :param kwargs:
        :return:
        """
        app.logger.info(f"审核者: {self.permissions}, magic_args: {self.magic_arg}")
        # 云版 and 生产环境，则进行权限判断
        kwargs["permissions"] = self.permissions
        if self.magic_arg.get("is_cloud") and self.magic_arg.get("permission"):
            return super().has_permission(kwargs)
        return True

    def _find_connector(self, open_id):
        ret = Session().find(open_id, "connectors")
        if ret:
            return json.loads(ret).values()
        return ret

    def _find_connector_used_by_register_connector(self, open_id):
        ret = Session().find(open_id, "connectors", "cache connectors")
        if ret[1] is None:
            return "all"
        return json.loads(ret[0])[int(ret[1])-1]

    def _parse_options(self, *args):
        # 结构示意：
        #      0          1        2         3       4           5            6           7
        # [构建完整版][运行集成测试][压缩包][镜像压缩包][前端mode][推送到Github][推送到Coding][推送到青云]
        build_full, run_integrate, is_package_tar, is_docker_save = [arg == "2" for arg in args[:4]]
        if len(args[5:8]) == 0:
            push_to_github, push_to_coding, push_to_qingcloud = False, False, True
        else:
            push_to_github, push_to_coding, push_to_qingcloud = [arg == '2' for arg in args[5:8]]
        if args[-1] == "1":
            frontend_mode = "production"
        elif args[-1] == "2":
            frontend_mode = "msa"
        elif args[-1] == "3":
            frontend_mode = "ikas"
        elif args[-1] == "4":
            frontend_mode = "chowsangsang"
        return build_full, run_integrate, is_package_tar, is_docker_save, frontend_mode, push_to_github, push_to_coding, push_to_qingcloud

    def _filter_connectors(self, open_id):
        connectors = []
        ret = Session().find(open_id)
        for k, v in ret.items():
            if re.match(r"cache .+-connector", k) and v == "1":
                connectors.append(v)
        return connectors

    def _find_options(self, open_id):
        resp = {
            "build_full": True,
            "run_integrate": True,
            "is_package_tar": False,
            "is_docker_save": False,
            "frontend_mode": "1",
            "push_to_github": False,
            "push_to_coding": False,
            "push_to_qingcloud": True,
        }
        optional = Session().find(
            open_id, *[f"cache {k}" for k in resp.keys()]
        )
        for i, v in enumerate(resp.keys()):
            if v == "frontend_mode":
                if optional[i] == "2":
                    resp[v] = "msa"
                elif optional[i] == "3":
                    resp[v] = "ikas"
                elif optional[i] == "4":
                    resp[v] = "chowsangsang"
                else:
                    resp[v] = "production"
                continue
            if optional[i] is not None:
                resp[v] = optional[i] == "2"
        return resp.values()

    def _make_error_branch(self):
        message_card = mc.MessageCard(mc.Header("分支选择错误", color="red"), mc.Config())
        div_1 = mc.Div(text=mc.Text(f"未选择分支"))
        div_2 = mc.Div(text=mc.Text(f'请重新选择一遍分支，如果已经选了那就是BUG了，跟jerry报下bug吧'))
        message_card.add_element(div_1, div_2)
        return message_card

    def _make_notice(self, runners: list[dict]):
        message_card = mc.MessageCard(mc.Header("有任务正在运行，正在排队等待调度"), mc.Config())
        for runner in runners:
            fields = mc.Fields()
            fields.add_child(mc.Text(f"**▶️ 任务类型**：\n{runner['event']}", mc.TextType.lark_md), is_short=True)
            fields.add_child(mc.Text(f"**▶️ 任务ID**：\n{runner['id']}", mc.TextType.lark_md), is_short=True)
            button = trigger_button(text="取消该任务", command=f"cancel cicd {runner['id']} | clear")
            check_button = trigger_button(text="查看任务进度", command=f"check cicd {runner['id']}")
            div = mc.Div(fields=fields)
            message_card.add_element(div, button, check_button)
        return message_card

    def find_branch(self, *args):
        """
        当args为空数组，则为可视化界面触发
        当args不为空数组，则为内部命令行触发

        :param args:
        :return:
        """
        is_cloud = self.magic_arg["is_cloud"]
        print(f"args is {args}")
        # 如果是可视化界面触发
        if len(args) == 0:
            if is_cloud:
                return self.option_obj.find_option_value_in_list(
                    "opensource_branch", "enterprise_branch", "frontend_branch", "cloud_branch"
                )
            else:
                return self.option_obj.find_option_value_in_list(
                    "opensource_branch", "enterprise_branch", "frontend_branch"
                )
        # 如果是底层命令触发
        else:
            if is_cloud:
                return self.option_obj.find_branch_in_console(args[1], args[0], args[2], args[3])
            else:
                return self.option_obj.find_branch_in_console(args[1], args[0], args[2])

    def find_package_optional(self, open_id, *args):
        connectors = self._find_connector(open_id)
        app.logger.info(f"过滤前的连接器：{connectors}")
        if len(args) == 0:
            build_full, run_integrate, is_package_tar, is_docker_save, frontend_mode, push_to_github, push_to_coding, push_to_qingcloud = \
                self._find_options(open_id)
            except_connectors = self._filter_connectors(open_id)
            app.logger.info(f"过滤的连接器：{except_connectors}")
            if connectors is not None:
                connectors = list(set(connectors) - set(except_connectors))
        else:
            # 命令结构示意：
            #      0         1       2         3          4        5        6        7           8           9         10
            # [企业版分支][开源分支][前端分支][构建完整版][运行集成测试][压缩包][镜像压缩包][前端mode][推送到Github][推送到Coding][推送到青云]
            build_full, run_integrate, is_package_tar, is_docker_save, frontend_mode, push_to_github, push_to_coding, push_to_qingcloud = self._parse_options(*args[3:11])
        app.logger.info(
            f"[build image] is_package_tar: {is_package_tar}, is_docker_save: {is_docker_save}, frontend mode: {frontend_mode}"
            f"build_full: {build_full}, run_integrate: {run_integrate}, push_to_github: {push_to_github}, push_to_coding: {push_to_coding}, "
            f"push_to_qingcloud: {push_to_qingcloud}"
        )
        return {
            "build_full": build_full,
            "run_integrate": run_integrate,
            "is_package_tar": is_package_tar,
            "is_docker_save": is_docker_save,
            "connectors": connectors,
            "frontend_mode": frontend_mode,
            "push_to_github": push_to_github,
            "push_to_coding": push_to_coding,
            "push_to_qingcloud": push_to_qingcloud
        }

    def find_build_module_cloud(self, open_id):
        """
        解析和查找缓存中用户自定义打包的模块
        :param open_id:
        :param args:
        :return:
        """
        modules = Session().find(open_id,
                                 "cache is_build_frontend", "cache is_build_tcm", "cache is_build_tm_java",
                                 "cache is_build_agent", "cache is_tapdata_agent", "cache is_register_connector"
                                 )
        ret = {
            "is_build_frontend": True,
            "is_build_tcm": True,
            "is_build_tm_java": True,
            "is_build_agent": True,
            "is_tapdata_agent": True,
            "is_register_connector": True
        }
        for i, v in enumerate(modules):
            if v is not None and str(v) == "1":
                ret[list(ret.keys())[i]] = False
        return ret.values()

    def get_cloud_event_url(self) -> dict:
        """
        获取云版环境url，用来提供给pdk注册数据源
        :return: {"url": "", "a": "", "ak": "", "sk": ""}
        """
        cloud_env = self.magic_arg["cloud_env"]
        cloud_env_map = eval(os.getenv("cloud_env_map"))
        return cloud_env_map[cloud_env]

    def handle(self, open_id, chat_type, *args, **kwargs):
        """
        1. 获取分支信息
        2. 获取打包选项信息
        3. 触发打包，并发送信息供确认
        4. 开启监听线程
        """
        """1. 获取分支信息"""
        # 设置分支
        app.logger.info(f"是否云版打包：{self.magic_arg.get('is_cloud')}")
        branches = self.find_branch(*args)
        print(f"解析后分支为, {branches}")
        # 当存在一个分支为空，则报错
        if branches is None:
            return self._make_error_branch().to_dict(), False
        app.logger.info(f"解析后分支为：{branches}")
        if self.magic_arg.get("is_cloud"):
            opensource_branch, enterprise_branch, frontend_branch, cloud_branch = branches
        else:
            opensource_branch, enterprise_branch, frontend_branch = branches
        """2. 获取打包选项信息"""
        # 设置打包方式 连接器等
        # 云版支持自定义选择打包模块，企业版支持自定义连接器，自定义压缩包、推送到Coding和镜像压缩包
        if not self.magic_arg.get("is_cloud"):
            ret = self.find_package_optional(open_id, *args)
            build_full, run_integrate, is_package_tar, is_docker_save, connectors, frontend_mode, push_to_github, push_to_coding, push_to_qingcloud = ret.values()
            app.logger.info(f"连接器列表：{connectors}")
        else:
            build_frontend, build_tcm, build_tm_java, build_agent, build_tapdata_agent, register_connector = \
                self.find_build_module_cloud(open_id)
            if register_connector:
                # 获取云版环境tm链接，用来pdk上传connector
                cloud_env_map = self.get_cloud_event_url()
                cloud_env_url = cloud_env_map["url"]
                cloud_env_a = cloud_env_map["a"]
                cloud_env_ak = cloud_env_map["ak"]
                cloud_env_sk = cloud_env_map["sk"]
                connector = self._find_connector_used_by_register_connector(open_id)
                register_connector_args = f"{cloud_env_a}#{cloud_env_ak}#{cloud_env_sk}#{connector}"
            else:
                cloud_env_url = ''
                register_connector_args = ''
        # 触发打包
        cicd_job_id = kwargs.get("job_id")
        github_obj = GithubApi(owner, repo)
        # 触发打包
        # 云版
        if self.magic_arg.get("is_cloud"):
            branches = f"{frontend_branch}%{enterprise_branch}%{opensource_branch}%{cloud_branch}"
            client_payload = {
                "build_frontend": build_frontend,
                "build_tcm": build_tcm,
                "build_tm_java": build_tm_java,
                "build_agent": build_agent,
                "build_tapdata_agent": build_tapdata_agent,
            }
            # 注册数据源，要增加这两个参数
            if register_connector:
                client_payload.update({
                    "cloud_event_url": cloud_env_url,
                    "register_connector_args": register_connector_args
                })
            # 如果是不是青云镜像，则携带上这个参数指定仓库名称
            elif self.magic_arg.get("image_repo") is not None:
                client_payload.update({
                    "image_repo": self.magic_arg.get("image_repo"),
                })
            res = github_obj.trigger(
                branches,
                cicd_job_id,
                enterprise_or_cloud="cloud",
                **client_payload
            )
        else:
            branches = f"{frontend_branch}%{enterprise_branch}%{opensource_branch}"
            # 镜像仓库选择 推送到哪个仓库
            image_repo_optional = f"{push_to_github}%{push_to_coding}%{push_to_qingcloud}"
            res = github_obj.trigger(
                branches,
                cicd_job_id,
                build_full=build_full,
                run_integrate=run_integrate,
                is_package_tar=is_package_tar,
                is_docker_save=is_docker_save,
                # connectors=connectors,
                enterprise_or_cloud="enterprise",
                frontend_mode=frontend_mode,
                image_repo_optional=image_repo_optional,
            )
        # 查询当前正在运行的任务并发送通知
        runners = GithubApi("tapdata", "tapdata-enterprise").list_runner_in_progress()
        if len(runners) > 0:
            message_card = self._make_notice(runners)
            if not self.magic_arg.get("quite"):
                send_text(self.message_type, open_id, message_card.to_dict(), "p2p")
        if self.magic_arg.get("is_cloud"):
            self.output = cicd_job_id
        else:
            self.output = f"{cicd_job_id} is_package_tar={is_package_tar} is_docker_save={is_docker_save} image_repo_optional={image_repo_optional}"
        if res:
            header = "🎉 构建任务创建成功，执行成功后会通知你哦"
        else:
            header = "😭 构建任务创建失败，请联系Jerry处理"
        color = "green" if res else "red"
        # 返回打包信息
        if self.magic_arg.get("is_cloud"):
            message_card = common.field_list_message_card(
                header=header,
                color=color,
                企业版分支=enterprise_branch,
                开源版分支=opensource_branch,
                前端分支=frontend_branch,
                云版分支=cloud_branch,
                构建前端=build_frontend,
                构建TCM=build_tcm,
                构建TM_Java=build_tm_java,
                构建Agent=build_agent,
                构建Tapdata_Agent=build_tapdata_agent,
                更新数据源=register_connector
            )
        else:
            message_card = common.field_list_message_card(
                header=header,
                color=color,
                企业版分支=enterprise_branch,
                开源版分支=opensource_branch,
                前端分支=frontend_branch,
                压缩包=is_package_tar,
                推送到青云镜像仓库="默认推送",
                镜像压缩包=is_docker_save,
                前端mode参数=frontend_mode,
            )
        return message_card.to_dict(), res
