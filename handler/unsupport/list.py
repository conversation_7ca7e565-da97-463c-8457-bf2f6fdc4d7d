"""
list 命令

@author: <PERSON>
@date: 2022.08.16
"""
import json
import os
import re

from utils.common import find_last_branch
from utils.handler import Handler
from utils.schdule import CommandParser
from utils import api, common

owner = os.getenv("OWNER")


@CommandParser.register
class List(Handler):
    command = "list"
    check_template = [{"require": True}]
    except_cache = True
    help = {
        "format": "list [branch/env/images/connector]",
        "说明": "底层操作命令，用于列出相关信息列表，输出格式为 'tag: 信息'，tag用来做快速选择",
        "branch": "列出分支名",
        "env": "列出应用名",
        "images": "列出镜像tag",
        "connector": "连接器",
    }

    def __init__(self):
        super().__init__()
        self.opensource_branch = []
        self.enterprise_branch = []
        self.frontend_branch = []
        self.image_tag = []
        self.env = []
        self.connector = []

    def _find_prescribed_branch(self, branches, prescribed):
        ret_branch = []
        for branch in branches:
            if prescribed in branch:
                ret_branch.append(branch)
        return ret_branch

    def make_text(self, branches: list):
        if len(branches) == 0:
            return "无"
        branches = common.make_cache(branches)
        return '\n'.join([f"{key}: {value}" for key, value in branches.items()])

    def _branch(self, command):
        opensource_branch = api.GithubApi(owner, "tapdata").branch()
        enterprise_branch = api.GithubApi(owner, "tapdata-enterprise").branch()
        frontend_branch = api.GithubApi(owner, "tapdata-enterprise-web").branch()
        if len(command) == 0:
            opensource_branch = find_last_branch(opensource_branch)
            enterprise_branch = find_last_branch(enterprise_branch)
            frontend_branch = find_last_branch(frontend_branch)
        elif command[0] == "all":
            pass
        else:
            opensource_branch = self._find_prescribed_branch(opensource_branch, command[0])
            enterprise_branch = self._find_prescribed_branch(enterprise_branch, command[0])
            frontend_branch = self._find_prescribed_branch(frontend_branch, command[0])

        self.opensource_branch, self.enterprise_branch, self.frontend_branch = \
            opensource_branch, enterprise_branch, frontend_branch

        return '\n'.join([
            "开源仓库分支：", self.make_text(opensource_branch), "\n",
            "企业版分支：", self.make_text(enterprise_branch), "\n",
            "前端分支：", self.make_text(frontend_branch)
        ])

    def get_cache(self, *args, **kwargs):
        if self.opensource_branch and self.enterprise_branch and self.frontend_branch:
            return {
                "opensource_branch": common.make_cache(self.opensource_branch),
                "enterprise_branch": common.make_cache(self.enterprise_branch),
                "frontend_branch": common.make_cache(self.frontend_branch),
            }
        elif self.image_tag:
            return {
                "image_tag": common.make_cache(self.image_tag)
            }
        elif self.env:
            return {
                "env": common.make_cache(self.env)
            }
        elif self.connector:
            return {
                "connector": common.make_cache(self.connector)
            }

    def _env(self, command):
        namespace = os.getenv("NAMESPACE")
        k8s_api = api.K8sApi(namespace)
        app_name = []
        for item in k8s_api.env():
            name = item["cluster"]["name"]
            if len(command) > 0:
                command = command[0]
                if command not in name:
                    continue
            app_name.append(name)
        self.env = app_name
        return "\n".join([
            "环境列表：", self.make_text(app_name)
        ])

    def _images(self, tag_num=1, version_num=2):
        ret = []
        image_list = api.GithubApi(owner, "tapdata").image_tag()
        image_tags = []
        for image in image_list:
            for tag in image.keys():
                image_tags.append(tag)
        self.output = json.dumps(image_list)
        # 找到最新$tag_num个的符合要求的tag版本
        image_tag = find_last_branch(image_tags, version_count=tag_num)
        # 找到$tag_num个的符合要求的tag版本的最新的$version_num个版本号
        for image in image_list:
            for tag, version in image.items():
                if tag not in image_tag:
                    continue
                count = 0
                for v in version:
                    if count >= version_num:
                        break
                    count += 1
                    ret.append(":".join([tag, v["tag"]]))
        self.image_tag = ret
        return "按照时间顺序排序：\n" + self.make_text(ret)

    def _connectors(self):
        connectors = api.GithubApi(os.getenv("OWNER"), os.getenv("REPO")).get_connectors(
            "connectors",
            "tapdata",
            "tapdata-enterprise",
            except_path=['build', 'pom.xml', 'pom-ent.xml']
        )
        self.connector = connectors
        return self.make_text(connectors)

    def handle(self, open_id, chat_type, *args, **kwargs):
        command = args[0]
        if command == "branch":
            ret = self._branch(args[1:])
        elif command == "env":
            ret = self._env(args[1:])
        elif command == "image" or command == "images":
            ret = self._images(tag_num=5, version_num=2)
        elif command == "connector":
            ret = self._connectors()
        else:
            ret = None
        return ret, True
