"""
可视化构建

@author: <PERSON>
@date: 2022.08.24
"""
import json
import os

from utils import api, common
from utils.common import get_connectors, get_branch_threading
from utils.handler import Handler
from utils import message_card as mc
from utils.message_card import trigger_button
from utils.schdule import CommandParser


@CommandParser.register
class Buildvisual(Handler):

    command = "打包"
    help = {
        "format": "打包 [选择连接器/云版]",
        "说明": "唤出Github构建器可视化面板",
        "选择连接器": "选择要打包的connector",
        "云版": "云版打包",
    }

    def __init__(self):
        super().__init__()
        self.is_cloud = False
        self.is_connector = False
        self.except_cache = True
        self.branches = {}
        self.message_type = "message_card"

    def get_cache(self):

        opensource_branch = self.branches.get("opensource_branch")
        enterprise_branch = self.branches.get("enterprise_branch")
        frontend_branch = self.branches.get("frontend_branch")
        cloud_branch = self.branches.get("cloud_branch")
        if self.is_cloud:
            ret = {
                "opensource_branch": common.make_cache(opensource_branch),
                "enterprise_branch": common.make_cache(enterprise_branch),
                "frontend_branch": common.make_cache(frontend_branch),
                "cloud_branch": common.make_cache(cloud_branch),
            }
        else:
            ret = {
                "opensource_branch": common.make_cache(opensource_branch),
                "enterprise_branch": common.make_cache(enterprise_branch),
                "frontend_branch": common.make_cache(frontend_branch),
            }
        if self.is_connector:
            ret.update({
                "connectors": self.connectors
            })
        ret.update({
            "job_id": self.job_id
        })
        return ret

    def default_handler(self, is_cloud=False) -> mc.MessageCard:
        # 头部
        header = mc.Header("▶️ Github 构建触发器")
        config = mc.Config()
        message_card = mc.MessageCard(header, config)
        # 云版分支选择
        if is_cloud:
            opensource_branch, enterprise_branch, frontend_branch, cloud_branch = get_branch_threading(
                'tapdata', 'tapdata-enterprise', 'tapdata-enterprise-web', 'tapdata-cloud')
            self.branches = {
                "opensource_branch": opensource_branch,
                "enterprise_branch": enterprise_branch,
                "frontend_branch": frontend_branch,
                "cloud_branch": cloud_branch,
            }
            cloud_div = mc.make_select_div("云版分支(必选)", cloud_branch, "cloud_branch")
            # 按模块打包
            build_frontend_div = mc.make_select_div(
                "前端", ["否", "是"], "is_build_frontend", initial_option="2", placeholder=mc.Text("是")
            )
            build_tcm_div = mc.make_select_div(
                "TCM", ["否", "是"], "is_build_tcm", initial_option="2", placeholder=mc.Text("是")
            )
            build_tm_java_div = mc.make_select_div(
                "TM-JAVA", ["否", "是"], "is_build_tm_java", initial_option="2", placeholder=mc.Text("是")
            )
            build_agent_div = mc.make_select_div(
                "Agent", ["否", "是"], "is_build_agent", initial_option="2", placeholder=mc.Text("是")
            )
        # 企业版分支选择
        else:
            opensource_branch, enterprise_branch, frontend_branch = get_branch_threading(
                'tapdata', 'tapdata-enterprise', 'tapdata-enterprise-web'
            )
            self.branches = {
                "opensource_branch": opensource_branch,
                "enterprise_branch": enterprise_branch,
                "frontend_branch": frontend_branch,
            }
            build_full_div = mc.make_select_div("完整构建(构建connector和tapdata-cli)", ["否", "是"], "build_full", placeholder=mc.Text("是"), initial_option="2")
            run_integrate_div = mc.make_select_div("运行集成测试", ["否", "是"], "run_integrate", placeholder=mc.Text("是"), initial_option="2")
            package_tar_div = mc.make_select_div("压缩包", ["否", "是"], "is_package_tar", placeholder=mc.Text("否"))
            docker_save_div = mc.make_select_div("镜像压缩包", ["否", "是"], "is_docker_save", placeholder=mc.Text("否"))
            frontend_mode = mc.make_select_div("前端mode", ["默认", "海事局", "ikas", "周生生"], "frontend_mode", placeholder=mc.Text("默认"))
            push_to_github_div = mc.make_select_div("推送到GITHUB镜像仓库", ["否", "是"], "push_to_github", placeholder=mc.Text("否"))
            push_to_coding_div = mc.make_select_div("推送到CODING镜像仓库", ["否", "是"], "push_to_coding", placeholder=mc.Text("否"))
            push_to_qingcloud_div = mc.make_select_div("推送到青云镜像仓库", ["否", "是"], "push_to_qingcloud", placeholder=mc.Text("是"),
                                               initial_option="2")
        # 分支选择
        enterprise_div = mc.make_select_div("企业版分支(必选)", enterprise_branch, "enterprise_branch")
        opensource_div = mc.make_select_div("开源版分支(必选)", opensource_branch, "opensource_branch")
        frontend_div = mc.make_select_div("前端分支(必选)", frontend_branch, "frontend_branch")
        # add elements
        if is_cloud:
            message_card.add_element(enterprise_div, opensource_div, frontend_div, cloud_div)
            message_card.add_element(mc.Hr())
            message_card.add_element(build_frontend_div, build_tcm_div, build_tm_java_div, build_agent_div)
        else:
            message_card.add_element(enterprise_div, opensource_div, frontend_div)
            message_card.add_element(mc.Hr())
            message_card.add_element(build_full_div, run_integrate_div)
            message_card.add_element(push_to_qingcloud_div, push_to_coding_div, push_to_github_div)
            message_card.add_element(package_tar_div, docker_save_div, frontend_mode)
        return message_card

    def custom_connector(self) -> mc.MessageCard:
        message_card = self.default_handler()
        header = mc.Header("▶️ Github 构建触发器--选择连接器")
        message_card.header = header
        # 分隔符
        message_card.add_element(mc.Hr())
        message_card.add_element(mc.Div(mc.Text("连接器选择：")))
        # 连接器列表
        connectors = get_connectors()
        self.connectors = common.make_cache(connectors)
        for connector in connectors:
            select_menu = mc.make_select(["否", "是"], connector, initial_option="1", placeholder=mc.Text("是"))
            div = mc.Div(mc.Text(connector), extra=select_menu)
            message_card.add_element(div)
        return message_card

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        job_id = kwargs.get("job_id")
        note = mc.Note()
        note.add_child(mc.Text(tag_type=mc.TextType.plain_text, content=job_id))
        self.job_id = job_id
        if len(args) == 0:
            trigger_div = trigger_button("开始构建", command=f"build id={self.job_id} | watch cicd | clear")
            message_card = self.default_handler()
            message_card.add_element(trigger_div)
            message_card.add_element(note)
            return message_card.to_dict(), True
        optional = args[0]
        if optional == "选择连接器":
            self.is_connector = True
            trigger_div = trigger_button("开始构建", command=f"build id={self.job_id} | watch cicd | clear")
            message_card = self.custom_connector()
            message_card.add_element(trigger_div)
            message_card.add_element(note)
            return message_card.to_dict(), True
        elif optional == "云版":
            self.is_cloud = True
            trigger_div = trigger_button(
                "开始构建", command=f"build id={self.job_id} is_cloud=True | watch cicd is_cloud=True"
            )
            message_card = self.default_handler(is_cloud=True)
            message_card.add_element(trigger_div)
            message_card.add_element(note)
            return message_card.to_dict(), True

