"""
listVisual 命令

@author: 
@date: 2022.9.19
"""
import os

from utils.init import app
from utils.schdule import CommandParser
from utils.handler import Handler
from utils import message_card as mc, common, api


@CommandParser.register
class Listvisual(Handler):
    command = "listVisual"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "listVisual [issue] [optional]",  # 命令示例
        "选项": "list命令的可视化输出版本",  # 填写选项和说明
        "issue": "查询coding上超过一天未关闭的紧急工单，默认查询最新的迭代，查询具体的迭代为迭代号，如listVisual issue 59"
    }

    def _get_issue_list(self):
        for _ in range(3):
            try:
                issue_list = api.Coding(os.getenv("CODING_PROJECT_NAME")).describe_issue_list_with_page()
                return issue_list
            except Exception as e:
                app.logger.warn(e)
                continue
        app.logger.error("获取coding紧急事项列表失败")
        return None

    def _filter_issue(self, issue_list: list, args):
        """
        1、如果不存在指定的迭代号，则筛选当前最新的迭代
        2、筛选一天之前开启的当前为关闭的工单
        """
        if len(args) > 0:
            last_iteration = int(args[0])
        else:
            # 获取当前最新的迭代
            last_iteration = 0  # 最新的迭代代号
            for issue in issue_list:
                # 当前工单属于某个迭代
                if issue.get("Iteration") and issue.get("Iteration").get("Name"):
                    iteration_name = issue.get("Iteration").get("Name")
                    iteration_code = iteration_name.split(" ")[1].replace("#", "")
                    # 迭代代号大于最新的迭代代号
                    if float(iteration_code) > float(last_iteration):
                        last_iteration = iteration_code

        ret = []
        # 筛选一天之前开启的当前为关闭的工单
        for issue in issue_list:
            # 当前工单属于某个迭代
            if issue.get("Iteration") and issue.get("Iteration").get("Name"):
                iteration_name = f"sprint #{last_iteration}"
                # 筛选当前的工单所属迭代名称 && 筛选当前工单创建时间在一天之前
                if issue.get("Iteration").get("Name") == iteration_name and \
                        issue.get("CreatedAt") < common.get_yesterday_timestamp():
                    ret.append(issue)
        return ret

    def _coding_message_card(self, issue_list: list):
        message_card = mc.MessageCard(mc.Header("📣 超过一天未关闭的紧急工单", color="blue"), mc.Config())
        for issue in issue_list:
            div = mc.Div()
            url = f"https://tapdata.coding.net/p/tapdata/bug-tracking/issues/{issue['Code']}/detail"
            div.text = mc.Text(f"▶️ [{issue['Name']}]({url})", tag_type=mc.TextType.lark_md)
            message_card.add_element(div)
        return message_card.to_dict()

    def _coding_issue(self, args):
        issue_list = self._get_issue_list()
        # 获取失败跳过
        if issue_list is None:
            return None
        # 筛选issue列表
        issue_list = self._filter_issue(issue_list, args)
        return self._coding_message_card(issue_list)

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        # do something
        command = args[0]
        if command == "issue":
            ret = self._coding_issue(args[1:])
            return ret, True
        return None, False
