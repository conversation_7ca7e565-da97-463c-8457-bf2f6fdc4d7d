"""
deployByScripts 命令

@author: 
@date: 2023.03.29
"""

import os
import time
import traceback

from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, api, common
from utils.shell import Shell


@CommandParser.register
class Deploybyscripts(Handler):
    """通过脚本更新 主要用于阿里云的更新"""
    command = "deployByScripts"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "deployByScripts",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "profile": "grayscale",
            "image_repo": "registry-vpc.cn-beijing.aliyuncs.com/tapdata",
            "env": "",
        }
        self.concurrency_limit = True

    def _get_option(self):
        profile = self.magic_arg["profile"]
        return profile

    def get_version_and_component(self, args: [str]):
        image_repo = self.magic_arg["image_repo"]
        print(f"type is {type(args)}, value is: {args}, value: {eval(''.join(args))}")
        tm_java_tag, agent_tag, console_tag, tcm_tag, _ = eval("".join(args))
        env = self.magic_arg["env"]
        print(tm_java_tag, agent_tag, console_tag, tcm_tag, env)
        print(f"env is {env}, tcm_tag is {tcm_tag}, tcm option is: {self.option_obj.find_value('cache is_build_tcm')}")
        if env == "dfs-tm-java" and tm_java_tag:
            return "tm", tm_java_tag.replace(f"{image_repo}/", "").split(":")[1]
        elif env == "dfs-console" and console_tag:
            return "console", console_tag.replace(f"{image_repo}/", "").split(":")[1]
        elif env == "dfs-tcm" and tcm_tag:
            return "tcm", tcm_tag.replace(f"{image_repo}/", "").split(":")[1]
        else:
            return None, None

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        """
            1. 运行upgrade.sh脚本触发构建
            2. 根据upgrade.sh脚本执行情况发送更新结果
        """
        # 获取upgrade.sh脚本
        upgrade_script_path = os.path.abspath('.') + "/scripts/upgrade.sh"
        if upgrade_script_path is None:
            message = common.field_list_message_card(
                header="😭 更新失败",
                color="red",
                is_short=False,
                具体原因="upgrade脚本不存在"
            )
            return message.to_dict(), False
        # 运行脚本触发构建
        profile = self._get_option()
        if profile is None:
            message = common.field_list_message_card(
                header="😭 更新失败",
                color="red",
                is_short=False,
                具体原因="profile参数都必须填"
            )
            return message.to_dict(), False
        components, version = self.get_version_and_component(args)
        print(f"args: {args}, component: {components}, version: {version}")
        if components is None or version is None:
            return "", True
        return_code = Shell.run(
            f"bash {upgrade_script_path} -p {profile} -v {version} -c {components}"
        )
        # 发送执行结果
        if return_code == 0:
            self.output = profile, components
            message = common.field_list_message_card(
                header=f"部署{self.magic_arg['env']}任务创建成功",
                color="green",
                is_short=False,
                正在更新="最长等待2分钟"
            )
            return message.to_dict(), True
        else:
            message = common.field_list_message_card(
                header=f"部署{self.magic_arg['env']}失败",
                color="red",
                is_short=False,
                处理方式="联系Jerry查看报错信息"
            )
            return message.to_dict(), False
