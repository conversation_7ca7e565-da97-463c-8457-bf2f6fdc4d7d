"""
list_cron 命令

@author: 
@date: 2023.01.17
"""
import json
import os
import time
import traceback

from utils.schdule import CommandParser, Schedule
from utils.handler import Handler, Letter
from utils import message_card as mc, common


@CommandParser.register
class List_cron(Handler):
    command = "list_cron"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "list_cron||定时任务",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        # do something
        message_card = mc.MessageCard(mc.Header("定时任务列表"), mc.Config())
        message_card.add_element()
        for cronjob in Schedule().cron_th:
            context = json.loads(cronjob.context)
            command, cron_date = context["command"].split("@")
            print(command, cron_date, context['job_id'])
            command_div = common.make_fields_div(任务命令=command, 定时任务时间=cron_date.replace("*", r"\*"))
            message_card.add_element(command_div)
            del_button = mc.trigger_button("停止并删除该定时任务", command=f"delete_cron {context['job_id']}")
            message_card.add_element(del_button, mc.Hr())
        return message_card.to_dict(), True
