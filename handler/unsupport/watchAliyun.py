"""
watchAliyun 命令

@author: 
@date: 2023.03.30
"""
import json
from json import JSONDecodeError
import os
import time
import traceback

from lib.api import send_text
from utils.api import GithubApi
from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, common, option
from utils.shell import Shell


@CommandParser.register
class Watchaliyun(Handler):
    command = "watchAliyun"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "watchAliyun",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    class RunnerConclusion:
        success = "success"
        failure = "failure"
        cancelled = "cancelled"
        start_failed = "start_failed"

    def send_message(self, runner_id, detail_url):
        message_card = mc.make_workflow_scheduling_notes(runner_id, detail_url)
        send_text(self.message_type, self.open_id, message_card.to_dict(), self.chat_type)

    def job_status(self):
        """
        监听任务创建，启动和结束，最终返回镜像tag

        1. 根据job_id获取runner_id
        2. 发送任务已经运行的通知
        3. 监听runner运行
        4. 发送runner状态
        """
        owner, repo = os.getenv("OWNER"), os.getenv("TRIGGER_REPO")
        github_obj = GithubApi(owner, repo)
        runner_info, status = github_obj.job_status(self.job_id, runner_url_fn=self.send_message)
        image_repo = option.ImageRepo(self.job_id, self.open_id).get_option()
        if status and status not in [github_obj.RunnerConclusion.failure, github_obj.RunnerConclusion.cancelled]:
            tag = runner_info["Dfs_tag"]
            tm = f"{image_repo}/tapdata/dfs-tm-java:{tag}" if option.UpdateTM(self.job_id, self.open_id).get_option() else "不更新"
            agent = f"{image_repo}/tapdata/dfs-flow-engine:{tag}" if option.UpdateAgent(self.job_id, self.open_id).get_option() else "不更新"
            console = f"{image_repo}/tapdata/dfs-console:{tag}" if option.UpdateConsole(self.job_id, self.open_id).get_option() else "不更新"
            tcm = f"{image_repo}/tapdata/dfs-tcm:{tag}" if option.UpdateTCM(self.job_id, self.open_id).get_option() else "不更新"
            kwargs = {
                "header": "🎉 构建任务执行成功",
                "TM": tm,
                "Agent": agent,
                "TapdataAgent": agent,
                "Console": console,
                "Tcm": tcm,
                "Cloud": f"{tag}",
            }
            message = common.field_list_message_card(**kwargs)
            return message, tag
        elif status and status == github_obj.RunnerConclusion.cancelled:
            message = common.field_list_message_card(
                header="📣 流程被cancelled掉了",
                color="red",
                is_short=False,
                处理方式="自行查看Github流程执行情况"
            )
        elif status and status == github_obj.RunnerConclusion.failure:
            message = common.field_list_message_card(
                header="😭 流程执行失败了",
                color="red",
                is_short=False,
                处理方式="自行查看Github流程执行情况"
            )
        elif not status and status == github_obj.RunnerConclusion.start_failed:
            message = mc.workflow_start_error()
        else:
            message = common.field_list_message_card(
                header="😭 流程执行超时",
                color="red",
                处理方式="自行查看Github执行情况并联系Jerry处理"
            )
        return message, None

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        message, package_image_tag = self.job_status()  # 返回消息体和镜像TAG
        if package_image_tag is None:
            return message.to_dict(), False
        message = common.field_list_message_card(
            header="构建任务运行成功",
            color="green",
            is_short=False,
            运行状态="更新服务成功",
        )
        return message.to_dict(), True
