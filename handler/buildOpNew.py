"""
buildOpNew 命令

@author: 
@date: 2023.09.28
"""

import os
import time
import traceback

from utils.schdule import CommandParser
from utils.handler import Handler, Letter
from utils import message_card as mc, option, common
from utils.api import GithubApi


@CommandParser.register
class Buildopnew(Handler):
    command = "buildOpNew"
    check_template = [
        # 填写参数规则
    ]
    message_type = "message_card"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "buildOpNew",  # 命令示例
        # "选项": "说明",  # 填写选项和说明
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = True

    def branch_check(self, branch):
        """
        分支合法化判断
        分支的规则如：develop-v3.5.4, release-v3.5.4
        当分支小于3.5.4时，返回False, 如develop-v3.5.3, release-v3.5.3
        :param branch:
        :return:
        """
        try:
            branch_version = branch.split('-')[-1]
            if common.compare_version(branch_version, '3.5.4') < 0:
                return False
            return True
        except Exception as e:
            return False

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        """
        1. 获取信息：
            - Tapdata 分支
            - Tapdata Enterprise 分支
            - Tapdata License 分支
            - Tapdata Enterprise Web 分支
            - 是否提供压缩包
            - 前端编译参数
            - 部署的环境
        2. 选项合法化 判断
            - 分支是否大于3.5.4
                - 当分支不大于3.5.4，发送警告信息，提示改功能需要分支版本基于大于3.5.4
        3. 触发构建
        4. 构建信息发送
        """
        # 1. 获取信息
        tapdata_branch = option.OpenSourceBranch(self.job_id, open_id).get_option()
        tapdata_enterprise_branch = option.EnterpriseBranch(self.job_id, open_id).get_option()
        tapdata_license_branch = option.LicenseBranch(self.job_id, open_id).get_option()
        tapdata_frontend_branch = option.FrontendBranch(self.job_id, open_id).get_option()
        is_tar = option.IsPackageTar(self.job_id, open_id).get_option()
        frontend_mode = option.FrontendMode(self.job_id, open_id).get_option()
        deployment = option.Deployment(self.job_id, open_id).get_option()
        # # 2. 选项合法化 判断
        if not tapdata_branch or not tapdata_enterprise_branch or not tapdata_license_branch or not tapdata_frontend_branch:
            return common.field_list_message_card(
                header="😭 任务创建失败",
                color="red",
                处理方式="请填写完整的分支信息"
            ).to_dict(), False
        # 3. 触发构建
        owner = os.getenv("OWNER")
        repo = os.getenv("TRIGGER_REPO")
        github_obj = GithubApi(owner, repo)
        res = github_obj.trigger_by_workflow_dispatch(
            "build-self-hosted.yaml",
            tapdata_enterprise_branch,
            FRONTEND_BRANCH=tapdata_frontend_branch,
            OPENSOURCE_BRANCH=tapdata_branch,
            LISENSE_BRANCH=tapdata_license_branch,
            IS_TAR=is_tar,
            FRONTEND_MODE=frontend_mode,
            DEPLOY=deployment,
            JOB_ID=self.job_id,
        )
        if res:
            header = "🎉 构建任务创建成功，执行成功后会通知你哦"
            color = "green"
        else:
            header = "😭 构建任务创建失败，请联系Jerry处理"
            color = "red"
        return common.field_list_message_card(
            header=header,
            color=color,
            Tapdata分支=tapdata_branch,
            TapdataEnterprise分支=tapdata_enterprise_branch,
            TapdataLicense分支=tapdata_license_branch,
            TapdataEnterpriseWeb分支=tapdata_frontend_branch,
            是否提供压缩包=is_tar,
            前端编译参数=frontend_mode,
            部署的环境=deployment,
        ).to_dict(), res
