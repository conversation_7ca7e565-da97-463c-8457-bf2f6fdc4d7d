"""
Cloud_metrics 命令

@author: 
@date: 2022.12.08
"""

from utils.api import KubernetesCommand
from utils.schdule import CommandParser
from utils.handler import Handler
import requests
import datetime
import pymongo


@CommandParser.register
class Cloud_metrics(Handler):
    command = "云版稳定性指标||cloud stable stability metric||cloud metric"
    check_template = [
        # 填写参数规则
    ]
    message_type = "text"  # message_card -> 消息卡片 / text -> 文本
    help = {
        "format": "cloud_metrics",  # 命令示例
    }

    def __init__(self):
        super().__init__()
        self.magic_arg = {  # 底层命令携带的参数如 build is_cloud=False，通过这个参数可以获取到
            "is_cloud": False,
            "namespace": "dev",
            "concurrency": False
        }
        self.concurrency_limit = False
        self.result = ""

    def handle(self, open_id, chat_type, *args, **kwargs) -> (str, bool):
        tasks = {
            "<EMAIL>": {
                "name": "北京梵几家居",
                "paid": True
            },
            "wechat_n5m527": {
                "name": "济南玖个零信息科技有限公司",
                "paid": True
            },
            "wechat_ngffp0": {
                "name": "无锡市锡山三建实业有限公司",
                "paid": True
            },
            "wechat_e5dj85": {
                "name": "心诺安-无想科技",
                "paid": True
            },
            "wechat_fc030i": {
                "name": "杭州心识宇宙",
                "paid": True
            },
            "wechat_5j71ma": {
                "name": "衡哲科技",
                "paid": True
            },
            "<EMAIL>": {
                "name": "mongo的工程师（海外)",
                "paid": True
            },
            "17633157021": {
                "name": "北京陆泰数据科技有限公司",
                "paid": True
            },
            "wechat_hj6mp0": {
                "name": "朔克网络",
                "paid": True
            },
            "18910539328": {
                "name": "深圳达光传感技术有限公司",
                "paid": True
            },
            "wechat_5p2pm4": {
                "name": "心诺安-魔家",
                "paid": True
            }
        }

        task_id_black_list = [
            "64bbef3cc54ba816de38f9f6",
            "63c4efac90c8e908c659b341"
        ]

        user_id_black_list = [
            "tapdata",
            "sam",
        ]

        months = 4
        illegal_percent = -0.001

        mongo_uri = "mongodb://root:XikIlSIIvWRP@47.76.179.247:37017/net-dfsTm3?authSource=admin&directconnection=true"
        client = pymongo.MongoClient(mongo_uri)
        db = client["net-dfsTm3"]
        collection = db["TaskRecord"]

        def set_user_ids():
            task_collection = db["Task"]
            pingTime_start = round((datetime.datetime.now() - datetime.timedelta(days=14)).timestamp() * 1000, 0)
            active_tasks = task_collection.find(filter={"pingTime": {"$gte": pingTime_start}},
                                                projection={"createUser": 1})
            # get distinct user ids
            user_ids = set()
            for active_task in active_tasks:
                block = False
                for black_user_id in user_id_black_list:
                    if black_user_id in active_task["createUser"]:
                        block = True
                if block:
                    continue
                user_ids.add(active_task["createUser"])
            for user_id in user_ids:
                if user_id not in tasks:
                    tasks[user_id] = {
                        "name": user_id,
                        "paid": False,
                    }

        def set_task_ids():
            task_collection = db["Task"]
            for user in tasks:
                if "task_ids" not in tasks[user]:
                    tasks[user]["task_ids"] = []
                all_tasks = list(task_collection.find(
                    {"createUser": user, "is_deleted": False, "status": {"$nin": ["delete_failed", "deleting"]}}))
                for task in all_tasks:
                    task_id = str(task["_id"])
                    if task_id in tasks[user]["task_ids"]:
                        continue
                    # if last status is error, and error keeps more than 1 week, skip this task
                    if task["status"] == "error" and (datetime.datetime.now() - task["errorTime"]).days > 7:
                        continue
                    # if task_id in black list, skip this task
                    if task_id in task_id_black_list:
                        continue
                    tasks[user]["task_ids"].append(task_id)

        set_user_ids()
        set_task_ids()

        self.result += "active user number: {}\n".format(len(tasks))
        self.result += "active task number: {}\n".format(sum([len(tasks[user]["task_ids"]) for user in tasks]))
        self.result += ("========================================================\n")

        self.result += "active paid user number: {}\n".format(sum([1 for user in tasks if tasks[user]["paid"]]))
        self.result += "active paid task number: {}\n".format(
            sum([len(tasks[user]["task_ids"]) for user in tasks if tasks[user]["paid"]]))
        self.result += "========================================================\n"

        def get_job_run_history(task_id):
            return list(collection.find(filter={"taskId": task_id}, projection={"statusStack": 1}).sort(
                [("_id", pymongo.ASCENDING)]))

        def merge_statusStacks(history):
            # merge all statusStacks into one list, and sort by timestamp
            statusStacks = []
            for i in range(len(history)):
                # if statusStack not in history[i], skip this record, because it never run
                if "statusStack" not in history[i]:
                    continue
                statusStacks.extend(history[i]["statusStack"])
            statusStacks.sort(key=lambda x: x["timestamp"])
            return statusStacks

        def get_error_and_running_time(task_id, history, n=0):
            if len(history) == 0:
                return 0, 0
            # calculate today is how many days from start of this month
            today = datetime.datetime.now().day
            start_time = datetime.datetime.now() - datetime.timedelta(days=today + n * 30)
            end_time = start_time + datetime.timedelta(days=30)
            if end_time > datetime.datetime.now():
                end_time = datetime.datetime.now()

            last_status, last_change_time, running_time, error_time = None, None, -1, 0
            merged_statusStacks = merge_statusStacks(history)
            if merged_statusStacks is None or len(merged_statusStacks) == 0:
                return 0, 0
            if merged_statusStacks is not None and len(merged_statusStacks) > 0:
                if merged_statusStacks[0]["timestamp"] > end_time:
                    return 0, 0
            # filter statusStacks by timestamp
            filterd_statusStacks = []
            first_i = None
            for i in range(len(merged_statusStacks)):
                stack = merged_statusStacks[i]
                timestamp = stack["timestamp"]
                if timestamp < start_time or timestamp > end_time:
                    continue
                filterd_statusStacks.append(stack)
                if first_i is None:
                    first_i = i
            if first_i is None:
                previous_stack = merged_statusStacks[-1]
                previous_status = previous_stack["status"]
                if previous_status == "running":
                    return 0, (end_time - start_time).total_seconds()
                return (end_time - start_time).total_seconds(), 0

            if first_i is not None and first_i > 0:
                previous_stack = merged_statusStacks[first_i - 1]
                last_change_time = start_time
                last_status = previous_stack["status"]

            for stack in filterd_statusStacks:
                status = stack["status"]
                timestamp = stack["timestamp"]

                if status not in ["running", "error", "complete"]:
                    continue
                # init last_status and last_change_time when first status is running
                if last_status is None and status == "running":
                    last_status = status
                    last_change_time = timestamp
                    continue

                # calculate running time when status changed from running to error
                if last_status == "running" and status in ["error", "complete"]:
                    # timedelta to seconds
                    try:
                        running_time += (timestamp - last_change_time).total_seconds()
                    except Exception as e:
                        continue
                    last_change_time = timestamp
                    last_status = status
                    continue

                # calculate error time when status changed from error to running
                if last_status == "error" and status == "running":
                    try:
                        error_time += (timestamp - last_change_time).total_seconds()
                    except Exception as e:
                        continue
                    last_change_time = timestamp
                    last_status = status
                    continue

            if last_status == "running" and last_change_time < end_time:
                running_time += (end_time - last_change_time).total_seconds()
            if last_status == "error" and last_change_time < end_time:
                if (end_time - last_change_time).total_seconds() > 3600 * 24 * 7:
                    error_time += 3600 * 24
                else:
                    error_time += (end_time - last_change_time).total_seconds()
            return error_time, running_time

        def calculate_error_time_percent():
            avg_user_error_time_percent = {}
            for user in tasks:
                avg_user_error_time_percent[user] = []
                task_ids = tasks[user]["task_ids"]
                for n in range(months):
                    user_error_time, user_running_time = 0, 0
                    for task_id in task_ids:
                        history = get_job_run_history(task_id)
                        error_time, running_time = get_error_and_running_time(task_id, history, n)
                        if (error_time + 10) / (running_time + error_time + 10) > 0.2 and running_time > 3600:
                            self.result += (
                                "high error task, user: {}, name: {}, task_id: {}, in month: {}, error_time: {} h, running_time: {} h, error percent: {}\n".format(
                                    user,
                                    tasks[user]["name"],
                                    task_id,
                                    datetime.datetime.now().month - n,
                                    round(error_time / 3600, 1),
                                    round(running_time / 3600, 1),
                                    "{}%".format(round((error_time + 10) / (running_time + error_time + 10) * 100, 1))
                                ))
                        if running_time > 3600*24 and running_time / (error_time+running_time+1) > 0.2:
                            user_error_time += error_time
                            user_running_time += running_time
                    if user_running_time == 0:
                        avg_error_time_percent = illegal_percent
                    else:
                        avg_error_time_percent = round(user_error_time / (user_error_time + user_running_time) * 100, 1)
                    avg_user_error_time_percent[user].append(avg_error_time_percent)
            return avg_user_error_time_percent

        avg_user_error_time_percent = calculate_error_time_percent()
        self.result += ("=======================================================\n")
        for user in avg_user_error_time_percent:
            x = []
            for n in range(months):
                p = avg_user_error_time_percent[user][n]
                if p == illegal_percent:
                    x.append("-%")
                else:
                    x.append("{}%".format(p))
            if tasks[user]["paid"]:
                self.result += ("paid user: {}, name: {}, avg error time percent in recently {} months: {}\n".format(
                    user,
                    tasks[user]["name"],
                    months,
                    x
                ))
        self.result += ("==============================================\n")

        all_user_avg_error_time_percent = []
        all_paid_user_avg_error_time_percent = []
        self.result += "here is avg error time percent in last {} months\n".format(months)
        for n in range(months):
            sum_error_time_percent = 0
            sum_user = len(avg_user_error_time_percent)

            sum_paid_error_time_percent = 0
            sum_paid_user = sum([1 for user in tasks if tasks[user]["paid"]])

            for user in avg_user_error_time_percent:
                p = avg_user_error_time_percent[user][n]
                if p == illegal_percent:
                    sum_user -= 1
                    if tasks[user]["paid"]:
                        sum_paid_user -= 1
                    continue
                sum_error_time_percent += p
                if tasks[user]["paid"]:
                    sum_paid_error_time_percent += p
            all_user_avg_error_time_percent.append(round(sum_error_time_percent / sum_user, 1))
            all_paid_user_avg_error_time_percent.append(round(sum_paid_error_time_percent / sum_paid_user, 1))
        x = ["{}%".format(all_user_avg_error_time_percent[n]) for n in range(months)]
        y = ["{}%".format(all_paid_user_avg_error_time_percent[n]) for n in range(months)]
        self.result += ("all users avg error time percent in last {} months : {}\n".format(months, x))
        self.result += ("paid users avg error time percent in last {} months : {}".format(months, y))

        return self.result, True
