[tool.poetry]
name = "Feishu_rebot"
version = "0.1.0"
description = ""
authors = ["dreamcoin1998 <<EMAIL>>"]

[tool.poetry.dependencies]
python = "^3.7"
Flask = "2.0.2"
python-dotenv = "^0.20.0"
requests = "^2.28.1"
pycryptodome = "^3.15.0"
kubernetes = "^24.2.0"
setuptools = "57.5.0"
redis = "^4.3.4"
aiohttp = "^3.8.1"
Jinja2 = "^3.1.2"
croniter = "^1.3.7"
gunicorn = "^20.1.0"
Flask-SQLAlchemy = "^2.5.1"
PyMySQL = "^1.0.2"
gevent = "^21.12.0"
pytz = "^2022.6"
fuzzywuzzy = "^0.18.0"
cn2an = "^0.5.19"
humanfriendly = "^10.0"
pymongo = "^4.3.3"
python-Levenshtein = "^0.20.9"
flask-httpauth = "^4.8.0"
pygithub = "^2.2.0"
pyjwt = "^2.8.0"
PyGithub = "^2.3.0"
celery = "5.2"
SQLAlchemy = "1.4"

[tool.poetry.dev-dependencies]

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"
