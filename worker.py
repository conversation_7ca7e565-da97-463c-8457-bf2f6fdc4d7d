# -*- coding:utf8 -*-
"""
worker进程
"""
import os
import logging
import sys
import uuid

from dotenv import load_dotenv, find_dotenv
import requests

from utils.init import app
from utils.schdule import Schedule


if __name__ == '__main__':
    load_dotenv(find_dotenv())
    # 设置日志
    if os.getenv("FLASK_ENV") == "production":
        logging.basicConfig(filename='worker.error.log', level=logging.DEBUG)
        sys.stdout = open('worker.error.log', 'a')
    # 运行线程数量
    thread_count = int(os.getenv("WORKER_THREAD_COUNT", 1))
    # 注册到Server
    schedule = Schedule()
    schedule.init_executor(thread_count)

