name: Docker Image CI

on:
  push:
    branches: [ "master", "update-cicd" ]
  pull_request:
    branches: [ "master" ]

jobs:

  build:

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v3
      - name: Set Docker Tag
        run: |
          docker login --username=tapdata_net registry.cn-hongkong.aliyuncs.com -p Gotapd8!
          tag=`date +%s`
          echo "docker_tag=registry.cn-hongkong.aliyuncs.com/tapdata-inter/feishu-robot:$tag" >> $GITHUB_ENV
          echo "tag=$tag" >> $GITHUB_ENV
      - name: install and auth to gcloud
        run: |
          sudo apt-get install kubectl
      - name: Build Push the Docker image
        run: |
          docker build . --file Dockerfile --tag ${{ env.docker_tag }}
          docker push ${{ env.docker_tag }}
      - name: Print summary
        if: ${{ steps.push_img.conclusion == 'success' }}
        run: |
          echo '**Github Package Image:**' >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          echo "docker pull ${{ env.docker_tag }}" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
      - name: 安装kubectl
        uses: azure/setup-kubectl@v3
      - id: 'auth'
        uses: 'google-github-actions/auth@v1'
        with:
          credentials_json: '${{ secrets.GCP_CREDENTIALS }}'
      - id: 'get-credentials'
        uses: 'google-github-actions/get-gke-credentials@v1'
        with:
          cluster_name: 'tapdata-cloud-cluster'
          location: 'asia-east2'
      - name: 部署
        run: |
          sed -i 's/--version--/${{ env.tag }}/g' deployment/server.yaml
          sed -i 's/--version--/${{ env.tag }}/g' deployment/worker.yaml
          kubectl --kubeconfig ./conf/alicloud/kubeconfig apply -f deployment/server.yaml
          kubectl --kubeconfig ./conf/alicloud/kubeconfig apply -f deployment/worker.yaml
