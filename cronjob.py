# -*- coding:utf8 -*-
"""
定时任务进程
"""
import os
import logging
import sys

from dotenv import load_dotenv, find_dotenv

from utils.init import app
from utils.schdule import Schedule


if __name__ == '__main__':
    load_dotenv(find_dotenv())
    if os.getenv("FLASK_ENV") == "production":
        logging.basicConfig(filename='cron.error.log', level=logging.DEBUG)
        sys.stdout = open('cron.error.log', 'a')
    schedule = Schedule()
    schedule.init_load_cronjob()
