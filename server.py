#!/usr/bin/env python3
import os
import logging
import uuid
import hmac
import hashlib
import re

import requests
from celery.result import As<PERSON>R<PERSON>ult

from flask import jsonify, request
import json, time
import datetime

from utils.init import app, celery
from lib.event import event_manager
from utils.schdule import Schedule, Context
from lib.event import MessageReceiveEvent, UrlVerificationEvent, TaskUpdateReceiveEvent
from utils.session import Session
from lib.api import send_text
from utils import common
from utils.common import github_map_feishu, get_branch_threading, all_release_branch
from utils.common import get_help
from utils import message_card
from flask_httpauth import HTTPBasicAuth
from werkzeug.security import generate_password_hash, check_password_hash
from utils import api
from utils.tasks import *
from model.version import Version
from model.task import Task
from utils.workflows import merge_to_develop, merge_to_release
from utils.hooks import *

VERIFICATION_TOKEN = os.getenv("VERIFICATION_TOKEN")
ENCRYPT_KEY = os.getenv("ENCRYPT_KEY")
os.environ.setdefault("WORK_DIR", os.getcwd())
auth = HTTPBasicAuth()
users = {
    "gcp_webhook": generate_password_hash("************************25480e65c78e"),
}


def init_schedule():
    schedule = Schedule()
    return schedule


@event_manager.register("url_verification")
def request_url_verify_handler(req_data: UrlVerificationEvent):
    # url verification, just need return challenge
    if req_data.event.token != VERIFICATION_TOKEN:
        raise Exception("VERIFICATION_TOKEN is invalid")
    return jsonify({"challenge": req_data.event.challenge})


@app.route("/test", methods=["GET"])
def test():
    # repo = "tapdata-connectors-enterprise"
    # workflow_run = {
    #     "id": "10365663634"
    # }
    # failed_jobs = api.GithubApi("tapdata", repo).get_failed_job_by_workflow_id(workflow_run.get("id"))

    # if repo == "tapdata-connectors" or repo == "tapdata-connectors-enterprise":
    #     failed_jobs = api.GithubApi("tapdata", repo).get_failed_job_in_sub_workflow(workflow_run.get("id"))

    # # fields["失败的任务"] = failed_jobs
    # print(failed_jobs)
    send_text("text", "oc_f811a33fb1326d7016f6b2d164b721e1", """任务：sprint #107, release-v3.23.0 Release Task 使用 release-v3.23.0 分支更新OP测试环境和云版灰度环境并打包开源版已完成
OP测试环境：http://192.168.1.184:3030
云版灰度环境：https://cloud.tapdata.net""", "group")
    return jsonify({"version": common.get_current_release()})


@event_manager.register("task.task.updated_v1")
def task_update_receiver_event_handler(req_data: TaskUpdateReceiveEvent):
    """
    任务信息变更回调
    1. 任务完成时通知到群，@ 指定的人
    2. 当合并完分支后，自动更新环境
    """
    task_id = req_data.event.task_id
    obj_type = req_data.event.obj_type
    # obj_type 值
    # 5 任务完成
    # 6 任务取消完成
    # 7 任务删除
    app.logger.info(req_data)
    app.logger.info(f"task_id: {task_id}, obj_type: {obj_type}")
    if obj_type == 5:
        task = Task.query.filter_by(id=task_id).all()
        if len(task) == 0:
            return jsonify()
        else:
            task = task[0]
        version = Version.query.filter_by(version=f"release-v{task.release_version}").all()
        if len(version) == 0:
            return jsonify()
        else:
            version = version[0]
        sprint = common.get_active_sprint()
        # 1. 合并到develop分支，则
        #   (1) 更新OP测试环境
        # 6. 合并到release分支，则
        #   (1) 更新OP测试环境
        #   (2) 更新云版灰度环境
        #   (3) 打开云版灰度开关
        #   (4) 发布agent到灰度环境
        if task.summary.startswith("1."):  # 以 1. 开头则为合并到develop分支
            merge_to_develop(sprint, task, task_id)
        elif task.summary.startswith("6."):  # 以 6. 开头则为合并到release分支
            merge_to_release(sprint, task, task_id, version)
    elif obj_type == 6:
        pass
    elif obj_type == 7:
        pass
    return jsonify()


@event_manager.register("im.message.receive_v1")
def message_receive_event_handler(req_data: MessageReceiveEvent):
    sender_id = req_data.event.sender.sender_id
    message = req_data.event.message
    if message.message_type != "text":
        logging.warn("Other types of messages have not been processed yet")
        return jsonify()
        # get open_id and text_content
    open_id = sender_id.open_id
    user_id = sender_id.user_id
    text_content = message.content
    app.logger.info("receive text content is: %s" % text_content)
    # register job
    receive_text = json.loads(text_content)["text"]
    if "@所有人" in receive_text:
        return jsonify()
    if "@_all" in receive_text:
        return jsonify()
    if receive_text.startswith("@"):
        command = ' '.join(receive_text.split(" ")[1:])
    else:
        command = receive_text
    # get chat_type
    chat_type = req_data.event.message.chat_type
    if chat_type == "p2p":
        open_id = sender_id.open_id
    if chat_type == "group":
        open_id = req_data.event.message.chat_id
        at_robot = False
        if not hasattr(message, "mentions"):
            return jsonify()
        for mention in message.mentions:
            if mention.name == "打包构建小助手" or mention.name == "小助手":
                at_robot = True
                break
        if not at_robot:
            return jsonify()
    context = Context(command=command, open_id=open_id, chat_type=chat_type, receive_text=receive_text, user_id=user_id)
    schedule.register(context)
    app.logger.info("command is: %s, open_id is: %s, chat_type is: %s" % (command, open_id, chat_type))
    return jsonify()


@event_manager.register("action_call_back")
def action_call_back(req_data: MessageReceiveEvent):
    open_id = req_data["open_id"]
    command = req_data["action"]["value"]["command"]
    option = req_data["action"].get("option")
    chat_id = req_data['open_chat_id']
    app.logger.info(f"chat id: {chat_id}, open id: {open_id}")
    # option 不为空代表缓存任务，将使用cache命令缓存option的值
    if option is None:
        context = Context(command=command, open_id=open_id, chat_type="p2p", chat_id=open_id,
                          user_id=req_data["user_id"])
    else:
        context = Context(command=" ".join([command, option]), open_id=open_id, chat_type="p2p",
                          chat_id=open_id, user_id=req_data["user_id"])

    schedule.register(context)
    return jsonify()


@app.errorhandler
def msg_error_handler(ex):
    logging.error(ex)
    response = jsonify(message=str(ex))
    response.status_code = (
        ex.response.status_code if isinstance(ex, requests.HTTPError) else 500
    )
    return response


@app.route("/debug", methods=["POST"])
def debug():
    print(request.json.get("message"))
    context = Context(
        command=request.json.get("message"),
        open_id=request.json.get("open_id"),
        chat_type=request.json.get("chat_type"),
        receive_text=request.json.get("message"))
    schedule.register(context)
    return 'OK'


@app.route("/", methods=["POST"])
def callback_event_handler():
    # init callback instance and handle
    event_handler, event = event_manager.get_handler_with_event(VERIFICATION_TOKEN, ENCRYPT_KEY)
    return event_handler(event)


@app.route("/healthy", methods=["GET"])
def healthy():
    return jsonify({"status": 1})


@app.route("/coding_webhook", methods=["POST"])
def coding_webhook():
    chat_id = "oc_75ad0cc0590b805483c5d156d047fc2a"

    def comment_notice(zoho_number, issue, user, comment):
        content = comment.get("content", "")
        if len(content) > 100:
            content = content[0:100] + "..."
        message = common.field_list_message_card(
            header="💭 紧急工单: {} 有新的评论".format(zoho_number),
            is_short=True,
            color="blue",
            工单标题=issue.get("title"),
            工单地址=issue.get("html_url"),
            zoho地址=issue.get("zoho_url"),
            提单人=user,
            评论人=comment.get("creator", {}).get("name"),
            评论内容=content,
            工单未解决已经=common.time_readable(time.time() - issue.get("created_at") / 1000),
        )
        return message

    def created_notice(zoho_number, issue, user):
        return common.field_list_message_card(
            header="❗紧急工单被创建: {}".format(zoho_number),
            is_short=True,
            color="red",
            工单标题=issue.get("title"),
            工单地址=issue.get("html_url"),
            zoho地址=issue.get("zoho_url"),
            提单人=user,
            工单未解决已经=common.time_readable(time.time() - issue.get("created_at") / 1000)
        )

    def status_notice(zoho_number, issue, user, status):
        message = common.field_list_message_card(
            header="🧭 紧急工单: {} 状态发生变化".format(zoho_number),
            is_short=True,
            color="blue",
            工单标题=issue.get("title"),
            工单地址=issue.get("html_url"),
            zoho地址=issue.get("zoho_url"),
            提单人=user,
            状态变更为=status,
            工单未解决已经=common.time_readable(time.time() - issue.get("created_at") / 1000),
        )
        if status in ["已关闭", "已关闭", "挂起", "已拒绝", "已发布"]:
            message = common.field_list_message_card(
                header="✅ 紧急工单: {} 已解决".format(zoho_number),
                is_short=True,
                color="green",
                工单标题=issue.get("title"),
                工单地址=issue.get("html_url"),
                zoho地址=issue.get("zoho_url"),
                提单人=user,
                状态变更为=status,
                工单解决耗时=common.time_readable(time.time() - issue.get("created_at") / 1000),
            )
        return message

    def priority_notice(zoho_number, issue, user):
        message = common.field_list_message_card(
            header="❗工单: {} 已经调整为紧急工单".format(zoho_number),
            is_short=True,
            color="red",
            工单标题=issue.get("title"),
            工单地址=issue.get("html_url"),
            zoho地址=issue.get("zoho_url"),
            提单人=user,
            工单未解决已经=common.time_readable(time.time() - issue.get("created_at") / 1000)
        )
        return message

    def assign_notice(zoho_number, issue, user, assign):
        message = common.field_list_message_card(
            header="🔥 紧急工单: {} 被指派处理人".format(zoho_number),
            is_short=True,
            color="blue",
            工单标题=issue.get("title"),
            工单地址=issue.get("html_url"),
            zoho地址=issue.get("zoho_url"),
            提单人=user,
            处理人=assign,
            工单未解决已经=common.time_readable(time.time() - issue.get("created_at") / 1000)
        )
        return message

    try:
        data = json.loads(request.data)
    except Exception as e:
        return jsonify({"ok": 0})
    action = data.get("action", "")

    if action not in ["created", "comment", "update_status", "update_assignee", "update_priority"]:
        return jsonify({"ok": 0})

    issue = None
    if action == "comment":
        issue = data.get("issueComment", {}).get("issue")
    else:
        issue = data.get("issue")

    if issue is None:
        return jsonify({"ok": 0})

    issue_priority = issue.get("priority", 0)
    title = issue.get("title", "")
    if issue_priority != 3 and not title.startswith("#0000"):
        return jsonify({"ok": 0})

    if title.startswith("#0000"):
        if action != "created":
            return jsonify({"ok": 0})

        message = common.field_list_message_card(
            header="❗云版用户任务出错, 工单被创建, 请售后同学及时进行初筛",
            is_short=True,
            color="yellow",
            工单标题=issue.get("title"),
            工单地址=issue.get("html_url"),
            处理方式="请售后同学进行快速初筛, 如果判断不为产品问题, 请评论+拒绝, 如果无法快速判断, 无需对此进行处理"
        )
        send_text("message_card", chat_id, message.to_dict(), "group")
        return jsonify({"ok": 0})

    title = issue.get("title", "")
    if not title.startswith("#"):
        return jsonify({"ok": 0})

    import re
    zoho_number = 0
    result = re.findall("#([0-9]*)(.*)", title)
    try:
        zoho_number = result[0][0]
    except Exception as e:
        pass

    description = issue.get("description", "")
    user = ""
    zoho_url = ""
    try:
        user = description.split("跟进人")[1].split("\n")[0]
    except Exception as e:
        pass
    try:
        zoho_url = description.split("工单地址")[1].split("\n")[0]
        x = zoho_url.split("http")
        zoho_url = "http" + x[len(x) - 1]
        zoho_url = zoho_url.rstrip(")")
    except Exception as e:
        pass

    issue["zoho_url"] = zoho_url

    if action == "created":
        message = created_notice(zoho_number, issue, user)
    if action == "comment":
        comment = data.get("issueComment")
        message = comment_notice(zoho_number, issue, user, comment)
    if action == "update_assignee":
        assign = issue.get("assignee", {}).get("name", "")
        message = assign_notice(zoho_number, issue, user, assign)
    if action == "update_status":
        status = issue.get("status")
        message = status_notice(zoho_number, issue, user, status)
    if action == "update_priority":
        message = priority_notice(zoho_number, issue, user)
    send_text("message_card", chat_id, message.to_dict(), "group")
    return jsonify({"ok": 0})


@app.route('/pr_webhook', methods=["POST"])
def pr_webhook():
    def pr_notice(data, repository, title, html_url, committer, reviewers, assignee):
        committer_aka_name = "{}, aka as: {}".format(github_map_feishu.get(committer, ["", ""])[1], committer)
        pull_request_id = data.get("pull_request", {}).get("id", "")
        if pull_request_id == "":
            return jsonify()
        for reviewer in reviewers:
            from utils.connector import redis_cli
            if redis_cli.hget(pull_request_id, reviewer) is not None:
                continue
            feishu_reviewer = github_map_feishu.get(reviewer, ["", ""])
            if feishu_reviewer is None:
                continue
            message = common.field_list_message_card(
                header="🍪 有新的代码审核提交到你这里了, 请及时享用 ~",
                is_short=True,
                PR地址=html_url,
                PR标题=title,
                发起人=committer_aka_name,
                代码仓库=repository,
            )
            redis_cli.hset(pull_request_id, reviewer, 1)
            redis_cli.expire(pull_request_id, 60)
            send_text("message_card", feishu_reviewer[0], message.to_dict(), "p2p")

    def pr_assign_notice(data, repository, title, html_url, committer, reviewers, assignee):
        committer_aka_name = "{}, aka as: {}".format(github_map_feishu.get(committer, ["", ""])[1], committer)
        feishu_assignee = github_map_feishu.get(assignee, None)
        if feishu_assignee is None:
            feishu_assignee = github_map_feishu.get("xbsura", ["", ""])
        message = common.field_list_message_card(
            header="🍪 有新的代码合并请求需要你处理, 请及时合并 ~",
            is_short=True,
            PR地址=html_url,
            PR标题=title,
            发起人=committer_aka_name,
            代码仓库=repository,
        )
        send_text("message_card", feishu_assignee[0], message.to_dict(), "p2p")

    def pr_review_notice(data, repository, title, html_url, committer, reviewers, assignee):
        comment = data.get("review", {}).get("body", "")
        if len(comment) >= 100:
            comment = comment[0:100] + "..."
        feishu_committer = github_map_feishu.get(committer, ["", ""])
        header = ""
        color = "green"
        state = data.get("review", {}).get("state", "")
        if state == "changes_requested":
            header = "📝 你发起的 PR 需要调整才能被合并, 请及时修改 ~"
            color = "blue"
        if state == "approved":
            header = "⛄ 你发起的 PR 通过了 Review, 请等待合并 ~"
        if state == "commented":
            header = "💭 你发起的 PR 有新的审核意见, 请关注 ~"
            color = "blue"

        message = common.field_list_message_card(
            header=header,
            color=color,
            is_short=True,
            评论=comment,
            PR地址=html_url,
            PR标题=title,
            代码仓库=repository,
        )
        send_text("message_card", feishu_committer[0], message.to_dict(), "p2p")

    def issues_open_notice(data):
        chat_id = "oc_ac19765a9c6ba7ac9e9d66d6e47456b3"
        issue = data.get("issue", {})
        body = issue.get("body", "")
        if len(body) > 100:
            body = body[0:100] + "..."
        message = common.field_list_message_card(
            header="🔖 有人在项目中提了一个 Issue ~",
            color="blue",
            is_short=True,
            Issue标题=issue.get("title", ""),
            Issue地址=issue.get("html_url"),
            内容概要=body,
            提出人=issue.get("user", {}).get("login", ""),
            代码仓库=data.get("repository", {}).get("full_name")
        )
        send_text("message_card", chat_id, message.to_dict(), "group")

    def pr_closed_notice(data, repository, title, html_url, committer, reviewers, assignee):
        merged = data.get("pull_request", {}).get("merged", False)
        feishu_committer = github_map_feishu.get(committer, ["", ""])
        if merged:
            merged_user = data.get("pull_request", {}).get("merged_by", {}).get("login", "")
            feishu_merged_user = github_map_feishu.get(merged_user, ["", ""])
            message = common.field_list_message_card(
                header="✅ 你发起的 PR 已经被合并, 请知晓 ~",
                is_short=True,
                PR地址=html_url,
                PR标题=title,
                合并人=feishu_merged_user[1] + ", aka as " + merged_user,
                代码仓库=repository,
            )
        else:
            close_user = data.get("pull_request", {}).get("user", {}).get("login", "")
            feishu_close_user = github_map_feishu.get(close_user, ["", ""])
            message = common.field_list_message_card(
                header="❌ 你发起的 PR 被关闭了, 而且没有合并, 请查看 ~",
                color="red",
                is_short=True,
                PR地址=html_url,
                PR标题=title,
                关闭人=feishu_close_user[1] + ", aka as " + close_user,
                代码仓库=repository,
            )
        send_text("message_card", feishu_committer[0], message.to_dict(), "p2p")

    try:
        data = json.loads(request.data)
    except Exception as e:
        return jsonify({"ok": 0})

    def get_fn(event_type, action):
        fn_key = event_type + "." + action
        if fn_key in event_action_fn:
            return event_action_fn[fn_key]
        import re
        for p, fn in event_action_fn.items():
            if re.match(p, fn_key):
                return fn
        return None

    def parse_pr_info(data):
        title = data.get("pull_request", {}).get("title", "")
        repository = data.get("repository", {}).get("full_name", "")
        html_url = data.get("pull_request", {}).get("html_url", "")
        committer = data.get("pull_request", {}).get("user", {}).get("login", "")
        reviewers = [i.get("login") for i in data.get("pull_request", {}).get("requested_reviewers", [])]
        assignee = data.get("assignee", {}).get("login", "")
        return repository, title, html_url, committer, reviewers, assignee

    def pr_issue_comment_notice(data):
        comment = data.get("comment", {}).get("body", "")
        if len(comment) >= 100:
            comment = comment[0:100] + "..."
        issue = data.get("issue", {})
        committer = data.get("comment", {}).get("user", {}).get("login", "")
        feishu_committer = github_map_feishu.get(committer, ["", ""])
        message = common.field_list_message_card(
            header="💭 你发起的 PR 有新的评论, 请关注 ~",
            is_short=True,
            评论=comment,
            PR地址=issue["html_url"],
            PR标题=issue["title"],
            代码仓库=data.get("repository", {}).get("full_name"),
        )
        send_text("message_card", feishu_committer[0], message.to_dict(), "p2p")

    def workflow_run_completed(data):
        workflow_run = data.get("workflow_run", {})
        workflow_name = workflow_run.get("name", "")
        status = workflow_run.get("conclusion", "")
        # pr 地址, 如果是空数组说明不是 PR 触发的工作流
        try:
            pr_url = workflow_run.get("pull_requests", [{}])[0].get("url", "")
            # 将api地址转换为html地址
            pr_url = pr_url.replace("api.github.com/repos", "github.com").replace("pulls", "pull")
        except IndexError as e:
            print(e)
            return
        # 获取commiter
        committer = data.get("sender", {}).get("login", "dreamcoin1998")
        feishu_committer = github_map_feishu.get(committer, ["", ""])
        repo = data.get("repository", {}).get("name", "")

        fields = {
            "工作流名称": workflow_name,
            "状态": status,
            "工作流地址": workflow_run.get("html_url"),
            "PR地址": pr_url,
            "仓库": repo,
        }

        if status == "success":
            color = "green"
            text = "成功"
        else:
            color = "red"
            text = "失败"

            failed_jobs = api.GithubApi("tapdata", repo).get_failed_job_by_workflow_id(workflow_run.get("id"))

            if repo == "tapdata-connectors" or repo == "tapdata-connectors-enterprise":
                failed_jobs = api.GithubApi("tapdata", repo).get_failed_job_in_sub_workflow(workflow_run.get("id")) or failed_jobs

            fields["失败的任务"] = failed_jobs

        message = common.field_list_message_card(
            header=f"🔖 有一个工作流执行{text}了",
            is_short=True,
            color=color,
            **fields
        )
        print(message.to_dict())
        print(feishu_committer[0])
        send_text("message_card", feishu_committer[0], message.to_dict(), "p2p")

    event_type = request.headers.get("X-GitHub-Event", "")
    action = data.get("action", "")
    event_action_fn = {
        "pull_request.review_requested": pr_notice,  # 有新的 PR 需要 Review, 此时通知 Review 的同学及时 Review
        "pull_request_review.submitted": pr_review_notice,  # 有 PR 被通过/调整/评论了 Review, 通知发起 PR 的同学
        "pull_request.closed": pr_closed_notice,  # PR 被关闭了, 通知发起 PR 的同学
        "issues.opened": issues_open_notice,  # 有新的 Issue, 通知相关人员
        "pull_request.assigned": pr_assign_notice,  # PR 被指派了, 通知被指派的同学
        # "issue_comment.created": pr_issue_comment_notice,  # PR 有新的评论, 通知发起 PR 的同学
        "workflow_run.completed": workflow_run_completed,  # 工作流执行结果通知
    }
    fn = get_fn(event_type, action)
    if fn is None:
        return jsonify({"ok": 1})
    if fn in [issues_open_notice, pr_issue_comment_notice, workflow_run_completed]:
        fn(data)
    else:
        fn(data, *(parse_pr_info(data)))
    return jsonify({"ok": 1})


@app.route('/job-qps', methods=["POST"])
def job_qps():
    dict_data = json.loads(request.data)
    headers = dict_data["headers"]


@app.route('/qingcloud-alarm', methods=["POST"])
def qingcloud_alarm():
    dict_data = json.loads(request.data)
    alerts = dict_data["alerts"]
    for alert in alerts:
        try:
            # label相关信息
            host_ip = alert["labels"]["host_ip"]
            node = alert["labels"]["node"]
            severity = alert["labels"]["severity"]
            aliasName = alert["annotations"]["aliasName"]
            startsAt = alert["startsAt"]
            message = alert["annotations"]["message"]
            color = "yellow" if severity == "warnning" else "red"
            message_card = common.field_list_message_card("青云资源告警", color=color,
                                                          节点IP=host_ip,
                                                          节点名称=node,
                                                          告警级别=severity,
                                                          告警规则名称=aliasName,
                                                          告警消息=message,
                                                          开始时间=common.convert_utctime_to_local(startsAt,
                                                                                                   "%Y-%m-%dT%H:%M:%S") + "Z")
            send_text("message_card", os.getenv("ALARM_GROUP_ID"), message_card.to_dict(), "group")
        except Exception as e:
            pass
    return "OK"


@auth.verify_password
def verify_password(username, password):
    if username in users and \
            check_password_hash(users.get(username), password):
        return username


@app.route('/gcp/alert', methods=["POST"])
@auth.login_required
def gcp_alert():
    if auth.current_user():
        dict_data = json.loads(request.data)
        incident = dict_data["incident"]
        print(json.dumps(incident, indent=4))
        policy_name = incident["policy_name"]
        color = "green"
        metrics = {}
        if "state" in incident:
            if incident["state"].lower() == "open":
                if "severity" in incident and incident["severity"] in ["No severity", "Warning"]:
                    color = "yellow"
                    metrics["告警级别"] = incident["severity"]
                else:
                    color = "red"
            metrics["告警状态"] = incident["state"]
        if "started_at" in incident:
            dt_object = datetime.fromtimestamp(incident["started_at"])
            metrics["告警开始时间"] = dt_object.strftime('%Y-%m-%d %H:%M:%S')
        if "ended_at" in incident and incident["ended_at"] is not None:
            dt_object = datetime.fromtimestamp(incident["ended_at"])
            metrics["告警结束时间"] = dt_object.strftime('%Y-%m-%d %H:%M:%S')
        if "metric" in incident:
            if "displayName" in incident["metric"] and incident["metric"]["displayName"]:
                metrics["监控指标"] = incident["metric"]["displayName"]
        if "observed_value" in incident:
            metrics["监控指标值"] = incident["observed_value"]
        if "threshold_value" in incident:
            metrics["监控指标阈值"] = incident["threshold_value"]
        if "resource_display_name" in incident:
            metrics["资源名称"] = incident["resource_display_name"]
        message_card = common.field_list_message_card(
            policy_name, color=color, is_short=False,
            告警名称=policy_name,
            **metrics
        )
        send_text("message_card", "oc_ad9541775c0726e5445a0a1a7ad075b1", message_card.to_dict(), "group")
        return jsonify({"ok": 0, "msg": ""})
    else:
        return jsonify({"ok": 1, "msg": "no permission"}), 401


@app.route('/webhook', methods=["POST"])
def webhook():
    dict_data = json.loads(request.data)
    secrets = os.getenv("WEBHOOK_SECRET")
    if not common.verify_webhook_token(request, secrets):
        return "no permissions"
    if request.headers.get("X-GitHub-Event") == "push":
        branch = dict_data.get("ref").replace("refs/heads/", "")
        app.logger.info(branch)
        if not common.compile_match_version(branch, "dfs-v[0-9]+.[0-9]+"):
            app.logger.info(f"branch is {branch}, skip cicd.")
            return "Skip CICD because branch not startswith dfs"
        opensource_branch, enterprise_branch, frontend_branch, cloud_branch = get_branch_threading(
            'tapdata', 'tapdata-enterprise', 'tapdata-enterprise-web', 'tapdata-cloud')
        ret = {
            "opensource_branch": common.make_cache(opensource_branch),
            "enterprise_branch": common.make_cache(enterprise_branch),
            "frontend_branch": common.make_cache(frontend_branch),
            "cloud_branch": common.make_cache(cloud_branch),
        }
        open_index = common.find_element_index_in_list(branch, opensource_branch) + 1
        enter_index = common.find_element_index_in_list(branch, enterprise_branch) + 1
        front_index = common.find_element_index_in_list(branch, frontend_branch) + 1
        cloud_index = common.find_element_index_in_list(branch, cloud_branch) + 1
        if open_index == 0 or enter_index == 0 or front_index == 0 or cloud_index == 0:
            app.logger.info(f"branches: {open_index}, {enter_index}, {front_index}, {cloud_index}")
            return "Skip CICD because branches not found"
        for key, value in ret.items():
            Session().add_session("webhook", key, json.dumps(value))
        Session().add_session("webhook", "cache opensource_branch", open_index)
        Session().add_session("webhook", "cache enterprise_branch", enter_index)
        Session().add_session("webhook", "cache frontend_branch", front_index)
        Session().add_session("webhook", "cache cloud_branch", cloud_index)
        command = f"build id={str(uuid.uuid4())} is_cloud=True quite=True | watch cicd is_cloud=True quite=True | " \
                  f"deploy update dfs-tm-java concurrency=True is_cloud=True namespace=cloud-dev quite=True % " \
                  f"deploy update dfs-agent concurrency=True is_cloud=True namespace=cloud-dev quite=True % " \
                  f"deploy update dfs-console concurrency=True is_cloud=True namespace=cloud-dev quite=True % " \
                  f"deploy update dfs-tcm concurrency=True is_cloud=True namespace=cloud-dev quite=True %" \
                  f"deploy update dfs-tapdata-agent concurrency=True is_cloud=True namespace=cloud-dev quite=True"
        context = Context(command=command, open_id="webhook", chat_type="p2p")
        Schedule().register(context)
        return "OK"
    return "event not support, only push"


@app.route("/ask", methods=["POST", "HEAD", "GET"])
def ask():
    try:
        phone = str(request.json["session"]["phone"]["value"]).lower()
    except Exception:
        return {
            "resp": "系统未获取您的用户信息, 请登录系统, 绑定您的手机号再使用, 或者联系我们的人工客服解决任务报错问题"}
    resp = get_help(user=None, phone=phone, last_seconds=86400 * 7)
    if len(resp) > 1500:
        resps = resp.split("\n")
        resp = ""
        for r in resps:
            if len(resp + r) > 1500:
                break
            resp = resp + "\n" + r
    return jsonify({"resp": resp.replace("\n", "\\n")})


@app.route("/private_ask", methods=["POST", "HEAD", "GET"])
def ask_2():
    task_id = str(request.json["task_id"])
    resp = get_help(user=None, task_id=task_id, last_seconds=86400 * 7)
    return jsonify({"task_id": task_id, "resp": resp.replace("\n", "\\n")})


@app.route("/performance_test", methods=["POST"])
def performance_test():
    jobs_infos = request.json
    text_array = []
    header_processor = f'\n**处理节点:** {jobs_infos["processor"]["type"]}' \
        if "external_storage_type" not in jobs_infos["processor"] \
        else f'\n**处理节点/外存类型:** {jobs_infos["processor"]["type"]}/{jobs_infos["processor"]["external_storage_type"]}'

    header = f'**服务器:** {jobs_infos["server"]}\t\t\t**服务器配置(CPU/内存):** ' \
             f'{jobs_infos["server_config"]["cpu"]}/{jobs_infos["server_config"]["memory"]}' \
             f'\n**环境版本:** {jobs_infos["env_version"]}\t\t\t**对比包版本:** 基准线版本' \
             f'\n**用例名字:** {jobs_infos["test_case_name"]}\t\t\t**任务类型:** {jobs_infos["job_type"]}' \
             f'\n**源节点:** {jobs_infos["source"]}\t\t\t**目标节点:** {jobs_infos["target"]}{header_processor}'

    header_text = message_card.Text(content=header, tag_type=message_card.TextType.lark_md)

    for res in jobs_infos["test_cases_infos"]:

        text_array.append(f'\n字段数：**{res["table_fields"]}**')

        for k, v in res.items():
            if k == "table_fields":
                continue

            text_array.append(
                f"- 当进行 **{common.TEXT_CONVERT[k]}** 同步时，**QPS** 比 **基准线** 包版本 **下降** 了 **{abs(v['qps_rate'])}%**。"
                f"\n  当前包版本QPS: **{v['source']}**, 基准线包版本QPS: **{v['target']}**")

    text = message_card.Text(content="\n".join(text_array), tag_type=message_card.TextType.lark_md)

    card = message_card.MessageCard(header=message_card.Header("性能测试预警报告"), config=message_card.Config())

    card.add_element(message_card.Div(header_text))
    card.add_element(message_card.Hr())
    card.add_element(message_card.Div(text))
    send_text("message_card", "oc_79ef2aafcf9a712bfc31280f80498732", card.to_dict(), "group")
    return jsonify()


@app.route("/send_to/feishu/group", methods=["POST"])
def send():
    """
    请求头：
    {
        "Token": "cba0125db7a18a32508a4e9e077058f33352c1c9124d2c3cbeb3f426096f100a"
    }
    请求体：
    {
        title: "Agent离线告警通知熔断提醒",
        content: "最近XX分钟，累计超过N个Agent离线，已自动启动Agent离线告警熔断机制，请尽快检查相关服务是否正常！"
        color: "red",
        groupId: "oc_d6bc5fe48d56453264ec73a2fb3eec70" # 产研群ID
    }
    :return:
    """
    token = request.headers.get("Token")
    if token != "cba0125db7a18a32508a4e9e077058f33352c1c9124d2c3cbeb3f426096f100a":
        return jsonify({"msg": "token error", "code": 401}), 401
    data = request.json
    msg_card = message_card.MessageCard(
        header=message_card.Header(
            data.get("title", "Agent离线告警通知熔断提醒"),
            color=data.get("color", "red")),
        config=message_card.Config()
    )
    msg_card.add_element(message_card.Div(message_card.Text(content=data["content"])))
    send_text(
        "message_card",
        data.get("groupId", "oc_d6bc5fe48d56453264ec73a2fb3eec70"),
        msg_card.to_dict(), "group")
    return jsonify({"msg": "success", "code": 0})


@app.route("/send_forward", methods=["POST"])
def send_forward():
    data = request.json
    send_text(data["msg_type"], data["open_id"], data["content"], data["chat_type"])


@app.route("/sonarqube", methods=["POST"])
def sonarqube_receive():
    data = request.json
    secret_key = os.getenv("SONARQUBE_KEY")
    signature = hmac.new(secret_key.encode(), request.data, hashlib.sha256).hexdigest()
    if request.headers.get("X-Sonar-Webhook-HMAC-SHA256") != signature:
        print(json.dumps(data, indent=4, ensure_ascii=False))
        return jsonify({"msg": "token error", "code": 401}), 401
    print(json.dumps(data, indent=4, ensure_ascii=False))
    return jsonify({"msg": "success", "code": 0})


@app.route("/alert-manager", methods=["POST"])
def alert_manager():
    data = request.json
    color = {
        "inactive": "green",
        "pending": "yellow",
        "firing": "red"
    }
    alert_title = data["commonAnnotations"].get("summary", "告警通知")
    msg_card = message_card.MessageCard(
        header=message_card.Header(alert_title, color=color[data["status"]]),
        config=message_card.Config()
    )
    msg_card.add_element(
        message_card.Div(
            text=message_card.Text(
                content=json.dumps(data, indent=4, ensure_ascii=False),
                tag_type=message_card.TextType.lark_md
            )
        )
    )
    send_text("message_card", common.github_map_feishu['dreamcoin1998'][0], msg_card.to_dict(), "p2p")
    return jsonify({"msg": "success", "code": 0})


@app.route("/artifacts", methods=["GET"])
@common.login_verify
def list_artifacts():
    alist = api.AlistFileserver("admin", "Gotapd8!")
    data = []
    for item in alist.list_path("/gz"):
        if re.match(r".*-tapdata-enterprise-.*.tar.gz", item["name"]):
            version = re.findall(r".*-tapdata-enterprise-(.*).tar.gz", item["name"])[0]
            data.append({
                "version": version,
                "name": item["name"],
                "size": "%.2f GB" % (item["size"] / 1024 / 1024 / 1024),
                "created": common.time_convert(item["created"]),
                "downloadLink": f"http://58.251.34.123:5244/d/gz/{item['name']}?sign={item['sign']}"
            })
    return jsonify({
        "msg": "success",
        "code": 0,
        "data": data,
    })


@app.route("/artifacts/connectors", methods=["GET"])
@common.login_verify
def list_connectors():
    alist = api.AlistFileserver("admin", "Gotapd8!")
    version = request.args.get("version", "")
    data = []
    connectors_list = alist.list_path("/connectors/" + version)
    if len(connectors_list) == 0:
        return jsonify({
            "msg": "success",
            "code": 0,
            "data": [],
        })
    for item in connectors_list:
        if not item["is_dir"] and re.match(r".*-v[0-9]+\.[0-9]+-SNAPSHOT\.jar", item["name"]):
            data.append({
                "name": item["name"],
                "size": "%.2f MB" % (item["size"] / 1024 / 1024),
                "created": common.time_convert(item["created"]),
            })
    return jsonify({
        "msg": "success",
        "code": 0,
        "data": data,
    })


@app.route("/artifact/trigger", methods=["POST"])
@common.login_verify
def artifact_trigger():
    data = json.loads(request.data)
    connectors_list, package_name, include_mongodb, os = data.get("connectors_list"), data.get("package_name"), data.get("include_mongodb"), data.get("os")

    task = trigger_workflow.delay("tapdata",
                                  "tapdata-application",
                                  "package-specified-connectors.yaml",
                                  "main",
                                  ConnectorsList=connectors_list,
                                  PackageName=package_name,
                                  IncludeMongoDB=include_mongodb,
                                  OS=os)

    return jsonify({
        "msg": "success",
        "code": 0,
        "data": task.id,
    })
    

@app.route("/artifact/lite", methods=["POST"])
@common.login_verify
def artifact_lite():
    data = json.loads(request.data)
    package_name, os, include_jdk_and_mongodb = data.get("package_name"), data.get("os"), data.get("include_jdk_and_mongodb")
    task = trigger_workflow.delay("tapdata",
                                  "tapdata-application",
                                  "package-lite-version.yaml",
                                  "main",
                                  PackageName=package_name,
                                  IncludeJdkAndMongodb=include_jdk_and_mongodb,
                                  OS=os)
    return jsonify({"msg": "success", "code": 0, "data": task.id})


@app.route("/cicd/trigger/opensource", methods=["POST"])
@common.login_verify
def cicd_trigger_opensource():
    data = json.loads(request.data)
    tapdata_branch = data.get("tapdata")
    tapdata_enterprise_web_branch = data.get("tapdata_enterprise_web")
    tapdata_connectors_branch = data.get("tapdata_connectors")
    task = trigger_workflow.delay("tapdata",
                                  "tapdata",
                                  "build.yml",
                                  tapdata_branch,
                                  frontend_branch=tapdata_enterprise_web_branch,
                                  connectors_branch=tapdata_connectors_branch)
    return jsonify({
        "msg": "success",
        "code": 0,
        "data": task.id,
    })


@app.route("/cicd/trigger/op", methods=["POST"])
@common.login_verify
def cicd_trigger_op():
    data = json.loads(request.data)
    tapdata_branch = data.get("tapdata")
    tapdata_enterprise_branch = data.get("tapdata_enterprise")
    tapdata_enterprise_web_branch = data.get("tapdata_enterprise_web")
    tapdata_connectors_branch = data.get("tapdata_connectors")
    tapdata_connectors_enterprise_branch = data.get("tapdata_connectors_enterprise")
    license_branch = data.get("license_branch")
    is_tar = data.get("is_tar")
    frontend_mode = data.get("frontend_mode")
    tapdata_platform_version = data.get("tapdata_platform_version")

    task = trigger_workflow.delay("tapdata",
                                  "tapdata-application",
                                  "build-tapdata-op.yaml",
                                  "main",
                                  OPENSOURCE_BRANCH=tapdata_branch,
                                  ENTERPRISE_BRANCH=tapdata_enterprise_branch,
                                  FRONTEND_BRANCH=tapdata_enterprise_web_branch,
                                  CONNECTORS_BRANCH=f"{tapdata_connectors_branch}#{tapdata_connectors_enterprise_branch}",
                                  LISENSE_BRANCH=license_branch,
                                  IS_TAR=is_tar, FRONTEND_MODE=frontend_mode,
                                  TAPDATA_PLATFORM_VERSION=tapdata_platform_version)

    return jsonify({
        "msg": "success",
        "code": 0,
        "data": task.id,
    })


@app.route("/artifact/worker/result", methods=["POST"])
@common.login_verify
def worker_result():
    data = json.loads(request.data)
    task_id = data["task_id"]
    async_result = AsyncResult(task_id, app=celery)
    if async_result.status == "FAILURE":
        result = False
    else:
        result = async_result.result
    return jsonify({
        "msg": "success",
        "code": 0,
        "data": result,
    })


@app.route("/artifact/result", methods=["GET"])
@common.login_verify
def artifact_result():
    workflow_id = request.args.get("workflow_id")
    if workflow_id is None:
        return jsonify({
            "msg": "workflow_id is None",
            "code": 1,
        })
    run_info = api.GithubApi('tapdata', 'tapdata-application').get_runner_job_info(workflow_id)
    # conclusion, status = run_info.get("conclusion"), run_info.get("status")
    return jsonify({
        "msg": "success",
        "code": 0,
        "data": run_info,
    })


@app.route("/artifact/login", methods=["POST"])
def artifact_login():
    data = json.loads(request.data)
    username, password = data.get("username"), data.get("password")
    if username != "admin" or password != "Gotapd8!":
        return jsonify({
            "msg": "密码或用户名不正确",
            "code": 1,
        })
    return jsonify({
        "msg": "",
        "code": 0,
        "data": common.Jwt().encode("admin")
    })


@app.route("/cicd/branches", methods=["GET"])
def cicd_branches():
    branches = request.args.get("branches")
    branches = common.get_branch_threading(*(branches.split(",")))
    print(branches)
    return jsonify({
        "msg": "",
        "code": 0,
        "data": list(branches)
    })


@app.route("/cicd/release_branch", methods=["GET"])
def release_branch():
    return jsonify({
        "msg": "",
        "code": 0,
        "data": all_release_branch(),
    })


@app.route("/jira_webhook", methods=["POST"])
def jira_webhook():

    # 发送目标
    who = "oc_ce0579e2a0d536a16336b560a8c92c1a"

    def no_solution_design_send(data):
        """
        如果有新的评论产生, 需要发一个飞书消息, 内容需要包括: issue 编号, 链接, 谁 进行了评论, 内容是: XXX, 用 info/success 的方式发出来
        """
        new_comment = data.get("fields", {}).get("comment", {}).get("comments", [])[-1]
        if new_comment["body"].lower().startswith("taptest"):
            return
        message = common.field_list_message_card(
            header="🔖 Issue 有新的评论了",
            color="blue",
            is_short=True,
            issue编号=data.get("key", ""),
            issue地址=f"https://tapdata.atlassian.net/browse/{data.get('key', '')}",
            评论人=new_comment["author"]["displayName"],
            评论内容=new_comment["body"]
        )
        send_text("message_card", who, message.to_dict(), "group")

    def branch_created_with_no_comment(data):
        """这个 issue, 如果有 branch created, 但是这个 issue 下面没有任何评论, 发一个飞书消息, 内容是: issue 编号, 链接, 在没有任何评论的情况下, 创建了分支"""
        issue_key = data.get("key", "")
        issue_url = f"https://tapdata.atlassian.net/browse/{issue_key}"
        # text = f"issue 编号: {issue_key}, {issue_url}, 在没有任何评论的情况下, 创建了分支"
        message = common.field_list_message_card(
            header="🔖 一个新的分支, 在没有任何评论的情况下创建了",
            color="yellow",
            is_short=True,
            issue编号=issue_key,
            issue地址=issue_url
        )
        send_text("message_card", who, message.to_dict(), "group")

    def branch_created_with_comment(data):
        issue_key = data.get("key", "")
        issue_url = f"https://tapdata.atlassian.net/browse/{issue_key}"
        comments = data.get("fields", {}).get("comment", {}).get("comments", [])
        comments_text = []
        for comment in comments:
            if comment['body'].lower().startswith("taptest"):
                continue
            comments_text.append(f"{comment['author']['displayName']}:{comment['body']}")
        comments_text = "\n".join(comments_text)
        # text = f"issue 编号: {issue_key}, {issue_url}, 创建了 branch, 以下是这个 issue 的全部评论: \n{comments_text}"
        message = common.field_list_message_card(
            header="🔖 有新的分支创建了, 评论如下, 请您查看",
            color="green",
            is_short=True,
            issue编号=issue_key,
            issue地址=issue_url,
            评论=comments_text
        )
        send_text("message_card", who, message.to_dict(), "group")

    if request.headers.get("X-Atlassian-Token") != "************************25480e65c78e":
        return jsonify({"ok": 1})
    a_map = {
        "comment.created.no_solution_design": no_solution_design_send,
        "branch.created.no_comment": branch_created_with_no_comment,
        "branch.created.with_comment": branch_created_with_comment,
    }
    a_map[request.args.get("a")](request.json)
    return jsonify()


@app.route("/targets", methods=["GET"])
def targets():
    ports_map = {
        "dfs-gateway": "34567",
        "tm-agent-prod3": "34567",
        "tm-prod3": "34567",
        "tcm-prod3": "34567",
    }

    namespace, app_name = request.args.get("namespace", "cloud-net"), request.args.get("appName")
    ips = KubernetesCommand(cloud_type="alicloud").get_pods_ips(app_name, namespace)[0]
    if len(ips) == 0:
        return jsonify([{
            "targets": [],
            "labels": {
                "project": app_name,
                "unitname": "pod_metrics",
                "service": app_name,
                "namespace": namespace,
            }
        }])
    print(ips)
    t = [f"{ip}:{ports_map[app_name]}" for ip in ips.split(" ")]
    return jsonify([{
        "targets": t,
        "labels": {
            "project": app_name,
            "unitname": "pod_metrics",
            "service": app_name,
            "namespace": namespace,
        }
    }])


if __name__ == "__main__":
    schedule = init_schedule()
    app.run(host="0.0.0.0", port=8888, debug=True, use_reloader=False)
else:
    schedule = init_schedule()
    gunicorn_error_logger = logging.getLogger('gunicorn.debug')
    gunicorn_info_logger = logging.getLogger("gunicorn.info")
    handler = logging.FileHandler('test.log', encoding='UTF-8')
    logging_format = logging.Formatter(  # 设置日志格式
        '%(asctime)s - %(levelname)s - %(filename)s - %(funcName)s - %(lineno)s - %(message)s')
    handler.setFormatter(logging_format)
    app.logger.addHandler(handler)
    app.logger.handlers.extend(gunicorn_error_logger.handlers)
